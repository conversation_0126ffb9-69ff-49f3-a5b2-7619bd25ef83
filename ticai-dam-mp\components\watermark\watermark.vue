<template>
  <view v-if="isTestEnvironment" class="watermark">
    <text class="watermark-text">测试版</text>
  </view>
</template>

<script>
import settings from '../../utils/settings.js'

export default {
  name: 'Watermark',
  data() {
    return {
      isTestEnvironment: false
    }
  },
  created() {
    // 检查是否为测试环境
    this.isTestEnvironment = settings.isTestEnvironment()
  }
}
</script>

<style scoped>
.watermark {
  position: fixed;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  pointer-events: none;
  /* 不阻止用户交互 */
}

.watermark-text {
  display: inline-block;
  padding: 12rpx 20rpx;
  background-color: rgba(255, 0, 0, 0.1);
  color: rgba(255, 0, 0, 0.6);
  font-size: 14px;
  border: 1rpx solid rgba(255, 0, 0, 0.3);
  border-radius: 8rpx;
  font-weight: bold;
}
</style>