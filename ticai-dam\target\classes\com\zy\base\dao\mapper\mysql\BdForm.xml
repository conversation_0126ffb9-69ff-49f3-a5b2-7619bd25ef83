<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.base.dao.BdFormDAO">

    <resultMap id="VO" type="com.zy.base.orm.BdForm">
        <result column="a.NO_" javaType="String" jdbcType="BLOB"/>
    </resultMap>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.base.orm.BdForm">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into BD_FORM(ID_,CODE_,NAME_,TIME_,USER_,FIELD_,DATA_,FLAG_)
        values(#{id},#{code},#{name},now(),#{user},#{field},#{data, jdbcType=BLOB},'1')
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.base.orm.BdForm">
		update BD_FORM
		set CODE_=#{code},NAME_=#{name},TIME_=now(),USER_=#{user},FIELD_=#{field},DATA_=#{data, jdbcType=BLOB}
		where ID_=#{id}
	</update>

    <select id="findIdByCode" resultType="String">
		select ID_ from BD_FORM where FLAG_='1' and CODE_=#{0}
	</select>

    <!-- 分页 -->
    <select id="list" resultType="com.zy.base.orm.BdForm">
        select a.ID_,a.CODE_,a.NAME_,a.TIME_,a.USER_,a.FIELD_
        from BD_FORM a
        where a.FLAG_='1'
        order by a.TIME_ desc
    </select>

    <!-- 获取唯一的基础-表单设计数据 -->
    <select id="findOne" resultMap="VO">
        select a.ID_,a.CODE_,a.NAME_,a.TIME_,a.USER_,a.FIELD_,a.DATA_
        from BD_FORM a where a.ID_=#{0}
    </select>

    <select id="findByCode" resultMap="VO">
        select a.ID_,a.CODE_,a.NAME_,a.TIME_,a.USER_,a.FIELD_,a.DATA_
        from BD_FORM a where a.FLAG_='1' and a.CODE_=#{0}
    </select>

    <!-- 删除 -->
    <update id="delete">
		update BD_FORM set FLAG_='9' where ID_=#{0}
	</update>
</mapper>
