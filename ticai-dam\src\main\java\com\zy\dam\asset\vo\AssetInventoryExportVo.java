package com.zy.dam.asset.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 资产盘点导出VO
 * 用于资产盘点页面的导出功能，只包含前端显示的字段
 */
@Data
@Schema(description = "资产盘点导出VO")
public class AssetInventoryExportVo {

    @ExcelProperty("资产类型")
    @Schema(description = "资产类型")
    private String typeName;

    @ExcelProperty("资产编码")
    @Schema(description = "资产编码")
    private String no;

    @ExcelProperty("终端号")
    @Schema(description = "终端号")
    private String nowSn;

    @ExcelProperty("资产名称")
    @Schema(description = "资产名称")
    private String name;

    @ExcelProperty("规格型号")
    @Schema(description = "规格型号")
    private String spec;

    @ExcelProperty("所属部门")
    @Schema(description = "所属部门")
    private String deptName;

    @ExcelProperty("使用部门")
    @Schema(description = "使用部门")
    private String useDeptName;

    @ExcelProperty("所在区域")
    @Schema(description = "所在区域")
    private String regionName;

    @ExcelProperty("所在地点")
    @Schema(description = "所在地点")
    private String locationName;
}
