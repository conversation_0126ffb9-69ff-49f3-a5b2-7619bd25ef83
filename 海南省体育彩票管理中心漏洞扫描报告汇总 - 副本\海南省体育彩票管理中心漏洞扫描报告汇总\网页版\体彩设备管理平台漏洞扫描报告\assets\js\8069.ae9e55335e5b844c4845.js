"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[8069],{27046:(a,n,s)=>{s.d(n,{Z:()=>d});var e=s(8081),t=s.n(e),r=s(23645),p=s.n(r)()(t());p.push([a.id,".snapShot[data-v-69fb114a] {\n  display: flex;\n  justify-content: center;\n}\n.snapShot > img[data-v-69fb114a] {\n  padding: 10px;\n  width: 100%;\n  border: #e6e6e6 solid 1px;\n}\n",""]);const d=p},8069:(a,n,s)=>{s.r(n),s.d(n,{default:()=>Z});var e=s(66252);const t={class:"snapShot"},r=["src"],p=(0,e.aZ)({__name:"WebSnapShot",props:{paramsData:{}},setup(a){const n=a;return(a,s)=>((0,e.wg)(),(0,e.iD)("div",t,[n.paramsData.webIndexSnapshot?((0,e.wg)(),(0,e.iD)("img",{key:0,src:`data:image/png;base64,${n.paramsData.webIndexSnapshot}`},null,8,r)):(0,e.kq)("v-if",!0)]))}});var d=s(93379),o=s.n(d),i=s(7795),l=s.n(i),c=s(90569),b=s.n(c),h=s(3565),u=s.n(h),f=s(19216),m=s.n(f),w=s(44589),g=s.n(w),v=s(27046),S={};S.styleTagTransform=g(),S.setAttributes=u(),S.insert=b().bind(null,"head"),S.domAPI=l(),S.insertStyleElement=m(),o()(v.Z,S),v.Z&&v.Z.locals&&v.Z.locals;const Z=(0,s(83744).Z)(p,[["__scopeId","data-v-69fb114a"]])}}]);