"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[9611],{65499:(e,t,o)=>{o.d(t,{B3:()=>r,KC:()=>c}),o(85827);var a=o(43139),l=(o(5255),o(78951)),i=o(79414);const{t:n}=i.i18n.global,r=["#55A722","#5f96ea","#6b69f2","#f1a45d","#65779b","#1680ff","#602dff","#8f11fc","#d14bf8","#d629a6","#ff5a7a","#0084ff","#c66cac","#9c2c34"];let s=[],d={};const c=(e,t)=>{const{data:o,colors:i=r,title:c="",finished:f,customOption:u,showLegend:p=!1,mode:y="custom",customSeries:g,isClearRef:h=!1}=t,{setOptions:x,getInstance:m,resize:b}=(0,a.l)(e,"default",f);s=g||"sum"===y?[{type:"pie",radius:["50%","65%"],datasetIndex:2,label:{show:!1,position:"center",formatter:["{num|{c}}","{name|{b}}"].join("\n"),rich:{num:{color:"#535353",fontSize:"24px",fontWeight:600},name:{fontSize:"12px",color:"#7e7e7e",padding:[10,0,0,0]}}},data:o.map(((e,t)=>({label:{color:i[t]},emphasis:{},...e}))),emphasis:{scaleSize:10,label:{show:!1},itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}},{type:"pie",center:["50%","50%"],radius:["50%","54%"],label:{show:!0,position:"center",formatter:[`{num|${1==o.length?o[0].value:o.reduce(((e,t)=>e+t.value),0)}}`,`{name|${n("custom.total")}}`].join("\n"),rich:{num:{color:"#535353",fontSize:"24px",fontWeight:600},name:{fontSize:"12px",color:"#7e7e7e",padding:[8,0,0,0]}}},data:o.map((e=>({emphasis:{},...e}))),itemStyle:{color:"rgba(0,0,0,.1)"},emphasis:{disabled:!0}}]:[{type:"pie",radius:["50%","70%"],datasetIndex:2,label:{show:!1,position:"center",formatter:["{num|{c}}","{name|{b}}"].join("\n"),rich:{num:{color:"#2e2e2e",fontSize:"24px"},name:{fontSize:"12px",color:"#7e7e7e",padding:[10,0,0,0]}}},data:o.map(((e,t)=>({label:{color:i[t]},emphasis:{},...e}))),emphasis:{scaleSize:10,label:{show:!0},itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}},{type:"pie",center:["50%","50%"],radius:["50%","56%"],label:{show:!1},data:o.map(((e,t)=>({label:{color:i[t]},emphasis:{},...e}))),itemStyle:{color:"rgba(0,0,0,.1)"},emphasis:{disabled:!0}}],d=g||"sum"===y?{show:!0,orient:"horizontal",y:"bottom",align:"auto",icon:"circle",bottom:20,itemWidth:13,itemHeight:13,textStyle:{color:"#7e7e7e",fontSize:14},formatter:e=>{const t=o.find((t=>t.name===e));return`${e}[${(null==t?void 0:t.value)||0}${n("onlineReport.items")}]`}}:{show:p,orient:"horizontal",align:"auto",bottom:10,itemWidth:13,itemHeight:13,textStyle:{color:"#333",fontSize:14}};const v={color:i,legend:d,tooltip:{show:!1},series:s};if(c&&(v.title={text:c,padding:[10,24],textStyle:{fontSize:16}}),o.length>0){e.value&&!h&&(e.value.innerHTML="");const t=u||{...v};x(t);const a=m();"sum"===y&&a&&a.off("legendselectchanged").on("legendselectchanged",(function(e){x(((e,t)=>(e.series[1]={...e.series[1],label:{...e.series[1].label,formatter:[`{num|${1==o.length?o[0].value:o.reduce(((e,o)=>(t.selected[o.name]&&(e+=o.value),e)),0)}}`,`{name|${n("custom.total")}}`].join("\n")}},{...e,legend:{...e.legend,selected:t.selected}}))(t,e))}))}else(0,l.T)(c).mount(e.value);return{data:o,colors:i,getInstance:m,resize:b}}},43139:(e,t,o)=>{o.d(t,{l:()=>$});var a=o(66252),l=o(2262),i=o(68023),n=o(51177),r=o(34870);const s=n.extendShape({shape:{x:0,y:0},buildPath:function(e,t){const o=t.wid||18,a=Math.sin(Math.PI/3)*o,l=t.xAxisPoint,i=[t.x,t.y],n=[t.x-a,t.y],r=[t.x-a,l[1]],s=[t.x,l[1]];e.moveTo(i[0],i[1]).lineTo(n[0],n[1]).lineTo(r[0],r[1]).lineTo(s[0],s[1]).closePath()}}),d=n.extendShape({shape:{x:0,y:0},buildPath:function(e,t){const o=t.wid||18,a=Math.sin(Math.PI/6)*o,l=Math.sin(Math.PI/3)*o,i=o/2,n=t.xAxisPoint,r=[t.x,t.y],s=[t.x,n[1]],d=[t.x+a,n[1]-l+i],c=[t.x+a,t.y-l+i];e.moveTo(r[0],r[1]).lineTo(s[0],s[1]).lineTo(d[0],d[1]).lineTo(c[0],c[1]).closePath()}}),c=n.extendShape({shape:{x:0,y:0},buildPath:function(e,t){const o=t.wid||18,a=Math.sin(Math.PI/6)*o,l=Math.sin(Math.PI/3)*o,i=o/2,n=[t.x,t.y],r=[t.x+a,t.y-l+i],s=[t.x-l+a,t.y-l+i],d=[t.x-l,t.y];e.moveTo(n[0],n[1]).lineTo(r[0],r[1]).lineTo(s[0],s[1]).lineTo(d[0],d[1]).closePath()}}),f=n.extendShape({shape:{x:0,y:0},buildPath:function(e,t){const o=t.yAxisPoint,a=[t.x,t.y],l=[t.x+5,t.y-5],i=[o[0]+5,o[1]-5],n=[o[0],o[1]];e.moveTo(a[0],a[1]).lineTo(l[0],l[1]).lineTo(i[0],i[1]).lineTo(n[0],n[1]).closePath()}}),u=n.extendShape({shape:{x:0,y:0},buildPath:function(e,t){const o=t.yAxisPoint,a=[t.x,t.y],l=[o[0],o[1]],i=[o[0]+5,o[1]+10],n=[t.x+5,t.y+10];e.moveTo(a[0],a[1]).lineTo(l[0],l[1]).lineTo(i[0],i[1]).lineTo(n[0],n[1]).closePath()}}),p=n.extendShape({shape:{x:0,y:0},buildPath:function(e,t){const o=[t.x,t.y],a=[t.x+5,t.y+10],l=[t.x+10,t.y+5],i=[t.x+5,t.y-5];e.moveTo(o[0],o[1]).lineTo(a[0],a[1]).lineTo(l[0],l[1]).lineTo(i[0],i[1]).closePath()}});var y=o(59333),g=o(15746),h=o(26947),x=o(58445),m=o(97178),b=o(23707),v=o(41969),S=o(28989),w=o(93450),A=o(8690),C=o(17813),z=o(31281),I=o(3754),T=o(30322),E=o(60520),P=o(21391),L=o(20573),F=o(55958),N=o(73865),H=o(31251),U=o(56042),V=o(2935),M=o(26379);i.D([w.N,A.N,C.N,z.N,I.N,T.N,E.N,y.N,g.N,h.N,x.N,m.N,M.N,b.N,P.N,L.N,F.N,N.N,H.N,U.N,V.N,v.N,S.N]),n.registerShape("CubeLeft",s),n.registerShape("CubeRight",d),n.registerShape("CubeTop",c),n.registerShape("CubeLeftByHorizontal",f),n.registerShape("CubeRightByHorizontal",u),n.registerShape("CubeTopByHorizontal",p);const O=r;o(21703);var R=o(64886),B=o(22560);function D(e,t,o=!1){if(!(0,B.mf)(e))throw new Error("handle is not Function!");const{readyRef:i,stop:n,start:r}=function(e){const t=(0,l.iH)(!1);let o;function a(){t.value=!1,o&&window.clearTimeout(o)}function i(){a(),o=setTimeout((()=>{t.value=!0}),e)}return i(),(0,R.sP)(a),{readyRef:t,stop:a,start:i}}(t);return o?e():(0,a.YP)(i,(t=>{t&&e()}),{immediate:!1}),{readyRef:i,stop:n,start:r}}function $(e,t="default",o){const i=(0,a.Fl)((()=>t));let n=null,r=u;const s=(0,l.iH)({});(0,a.Fl)((()=>appSrore.getCollapsed));let d=()=>{};r=(0,R.DI)(u,200);const c=(0,a.Fl)((()=>"dark"!==i.value?s.value:{backgroundColor:"transparent",...s.value}));function f(i=t){const s=(0,l.SU)(e);if(!s||!(0,l.SU)(s))return;n=O.init(s,i),n.on("finished",(function(){o&&o(n)})),n.on("dataZoom",(function(e){const t=e.batch&&e.batch.some((e=>0!==e.start||100!==e.end)),o=s.querySelector("div");t?o&&o.classList.add("zoom_grab"):o&&o.classList.remove("zoom_grab")}));const{removeEvent:c}=function({el:e=window,name:t,listener:o,options:i,autoRemove:n=!0,isDebounce:r=!0,wait:s=80}){let d=()=>{};const c=(0,l.iH)(!1);if(e){const f=(0,l.iH)(e),u=r?(0,R.DI)(o,s):(0,R.vA)(o,s),p=s?u:o,y=e=>{c.value=!0,e.removeEventListener(t,p,i)},g=e=>e.addEventListener(t,p,i),h=(0,a.YP)(f,((e,t,o)=>{e&&(!(0,l.SU)(c)&&g(e),o((()=>{n&&y(e)})))}),{immediate:!0});d=()=>{y(f.value),h()}}return{removeEvent:d}}({el:window,name:"resize",listener:r});d=c}function u(){var e;null===(e=n)||void 0===e||e.resize({animation:{duration:300,easing:"quadraticIn"}})}return(0,R.sP)((()=>{n&&(d(),n.dispose(),n=null)})),{setOptions:function t(o,r=!0){var d;s.value=o,0!==(null===(d=(0,l.SU)(e))||void 0===d?void 0:d.offsetHeight)?(0,a.Y3)((()=>{D((()=>{var e,t;(n||(f(i.value),n))&&(r&&(null===(e=n)||void 0===e||e.clear()),null===(t=n)||void 0===t||t.setOption((0,l.SU)(c)))}),30)})):D((()=>{t((0,l.SU)(c))}),30)},resize:u,echarts:O,getInstance:function(){return n||f(i.value),n}}}},69611:(e,t,o)=>{o.d(t,{fE:()=>y,gE:()=>g.KC,Ao:()=>f,y8:()=>m,HC:()=>r}),o(85827),o(57658);var a=o(43139),l=o(2262);const i={xdata:(0,l.iH)(["www","数据库","其他","ATP","SSL","SMB","DCE","KERNEL"]),result:[{name:"高危",data:[0,435,90,340,320,270,360,400],legendColor:"#EC4F4F"},{name:"中危",data:[119,235,190,220,120,270,560,200],legendColor:"#FA8C16"},{name:"低危",data:[130,120,240,280,40,180,288,200],legendColor:"#0072EE"}]},n=[{left:[{offset:0,color:"rgba(236,79,79,0.9)"},{offset:1,color:"rgba(236,79,79,0.6)"}],right:[{offset:0,color:"rgba(236,79,79,1)"},{offset:1,color:"rgba(236,79,79,0.9)"}],top:[{offset:0,color:"rgba(236,79,79,1)"},{offset:1,color:"rgba(236,79,79,1)"}]},{left:[{offset:0,color:"rgba(250,140,22,0.9)"},{offset:1,color:"rgba(250,140,22,0.6)"}],right:[{offset:0,color:"rgba(250,140,22,1)"},{offset:1,color:"rgba(250,140,22,0.9)"}],top:[{offset:0,color:"rgba(250,140,22,1)"},{offset:1,color:"rgba(250,140,22,1)"}]},{left:[{offset:0,color:"rgba(0,114,238,0.9)"},{offset:1,color:"rgba(0,114,238,0.6)"}],right:[{offset:0,color:"rgba(0,114,238,1)"},{offset:1,color:"rgba(0,114,238,0.9)"}],top:[{offset:0,color:"rgba(0,114,238,1)"},{offset:1,color:"rgba(0,114,238,1)"}]}],r=(e,t)=>{const{title:o,grid:r={top:"25%",left:"5%",right:"2%",bottom:"10%",containLabel:!0},result:s=i.result,xdata:d=i.xdata,colors:c=n,yAxisName:f="漏洞数量（个）",isShowBar:u=(0,l.iH)(!0),isShowline:p=(0,l.iH)(!0),finished:y,legendOpt:g={},titleOpt:h={}}=t;s.reduce(((e,t,o)=>(e[o]=t.data.map(((t,a)=>t+(e[o-1]?e[o-1][a]:0))),e)),[]);const x=((e,t,o,a)=>{const l={duration:1e3,easing:"cubicInOut",delay:10};return e.reduce(((e,i,n)=>{const r=t[n];return o.value&&e.push({type:"custom",name:i.name,renderItem:(e,t)=>{const o=t.coord([t.value(0),t.value(1)]);o[0]=o[0]+19*n;const a=t.coord([t.value(0),0]);a[0]=a[0]+19*n;const i={style:{opacity:0},y:t.value(1)};let s=!0;return t.value(1)||(s=!1),{type:"group",x:-14,y:0,children:s?[{type:"CubeLeft",shape:{api:t,wid:12,xValue:t.value(0),yValue:t.value(1),x:o[0],y:o[1],xAxisPoint:a},style:{fill:{type:"linear",x:0,x2:0,y:0,y2:1,colorStops:null==r?void 0:r.left}},enterAnimation:l,enterFrom:i},{type:"CubeRight",shape:{api:t,wid:12,xValue:t.value(0),yValue:t.value(1),x:o[0],y:o[1],xAxisPoint:a},style:{fill:{type:"linear",x:0,x2:0,y:0,y2:1,colorStops:r.right}},enterAnimation:l,enterFrom:i},{type:"CubeTop",shape:{api:t,wid:12,xValue:t.value(0),yValue:t.value(1),x:o[0],y:o[1],xAxisPoint:a},style:{fill:{type:"linear",x:0,x2:0,y:0,y2:1,colorStops:r.top}},enterAnimation:l,enterFrom:i}]:[]}},colorBy:"series",color:i.legendColor,data:i.data,clip:!0}),a.value&&e.push({type:"line",color:i.legendColor,colorBy:"series",symbol:"circle",symbolSize:8,name:i.name,data:i.data,tooltip:{show:!1}}),e}),[])})(s,c,u,p),m={grid:r,legend:{data:s.map((e=>({name:e.name,itemStyle:{color:e.legendColor||"inherit"}}))),textStyle:{fontSize:12,color:"#7e7e7e"},itemWidth:12,itemHeight:4,borderRadius:2,itemGap:15,top:"10%",right:"10%",selectedMode:!1,...g},tooltip:{trigger:"axis"},xAxis:{splitLine:{show:!0,lineStyle:{color:"#ebeef2",type:"dashed"}},axisLine:{lineStyle:{color:"#ebeef2",type:"dashed"}},axisLabel:{interval:0,hideOverlap:!0},data:(0,l.SU)(d)},yAxis:{name:(0,l.SU)(f),splitLine:{lineStyle:{color:"#ebeef2",type:"dashed"}}},series:x,textStyle:{fontSize:12,color:"#7E7E7E"},dataZoom:[{type:"inside",show:!0,xAxisIndex:[0],start:0,end:100}]};o&&(m.title={text:o,textStyle:{fontSize:16},...{...{padding:[10,24],...h}}});const{setOptions:b,getInstance:v,resize:S}=(0,a.l)(e,"default",y);return b(m),{getInstance:v,resize:S}};(0,l.iH)(["www","数据库","其他","ATP","SSL","SMB","DCE","KERNEL"]);var s=o(78951);const d={name:"",data:[],legendColor:""},c=[{left:[{offset:0,color:"rgba(85,167,34,0.9)"},{offset:1,color:"rgba(85,167,34,0.6)"}],right:[{offset:0,color:"rgba(85,167,34,1)"},{offset:1,color:"rgba(85,167,34,.9)"}],top:[{offset:0,color:"rgba(85,167,34,1)"},{offset:1,color:"rgba(85,167,34,1)"}]},{left:[{offset:0,color:"#FCB92D"},{offset:1,color:"#FFA03A"}],right:[{offset:0,color:"#FCB92D"},{offset:1,color:"#FFA03A"}],top:[{offset:0,color:"#FFA03A"},{offset:1,color:"#FFA03A"}]},{left:[{offset:0,color:"#005559"},{offset:1,color:"#34ADFF"}],right:[{offset:0,color:"#005559"},{offset:1,color:"#34ADFF"}],top:[{offset:0,color:"#34ADFF"},{offset:1,color:"#34ADFF"}]}],f=(e,t)=>{var o,i,n,r;const{title:f="",xdata:u=(0,l.iH)([]),colors:p=c,lineData:y=(0,l.iH)(d),barData:g=(0,l.iH)(d),finished:h,grid:x={top:"25%",left:"15%",right:"10%",bottom:"10%",containLabel:!0},minInterval:m=0,xAxisAxisLabel:b={},dataZoom:v}=t;null!==(o=(0,l.SU)(g))&&void 0!==o&&o.data.length||null!==(i=(0,l.SU)(y))&&void 0!==i&&i.data.length||console.error("参数barData或者lineData 必须传一种");const S=((e,t,o)=>{const a=[],l={duration:1e3,easing:"cubicInOut",delay:10};if(null!=t&&t.data.length){const e=o[0];a.push({type:"custom",name:t.name,renderItem:(t,o)=>{const a=o.coord([o.value(0),o.value(1)]),i=o.coord([o.value(0),0]),n={style:{opacity:0},y:o.value(1)};let r=!0;return o.value(1)||(r=!1),{type:"group",y:0,children:r?[{type:"CubeLeft",shape:{api:o,wid:12,xValue:o.value(0),yValue:o.value(1),x:a[0],y:a[1],xAxisPoint:i},style:{fill:{type:"linear",x:0,x2:0,y:0,y2:1,colorStops:null==e?void 0:e.left}},enterAnimation:l,enterFrom:n},{type:"CubeRight",shape:{api:o,wid:12,xValue:o.value(0),yValue:o.value(1),x:a[0],y:a[1],xAxisPoint:i},style:{fill:{type:"linear",x:0,x2:0,y:0,y2:1,colorStops:e.right}},enterAnimation:l,enterFrom:n},{type:"CubeTop",shape:{api:o,wid:12,xValue:o.value(0),yValue:o.value(1),x:a[0],y:a[1],xAxisPoint:i},style:{fill:{type:"linear",x:0,x2:0,y:0,y2:1,colorStops:e.top}},enterAnimation:l,enterFrom:n}]:[]}},color:t.legendColor,data:t.data,yAxisIndex:0,clip:!0})}return null!=e&&e.data.length&&a.push({type:"line",color:e.legendColor,colorBy:"series",symbol:"circle",symbolSize:8,name:e.name,data:e.data,yAxisIndex:null!=e&&e.data.length&&null!=t&&t.data.length?1:0,tooltip:{show:!0}}),a})((0,l.SU)(y),(0,l.SU)(g),p),w={splitLine:{show:!0,lineStyle:{color:"#ebeef2",type:"dashed"}},axisLine:{lineStyle:{color:"#ebeef2",type:"dashed"}},axisLabel:{interval:0,hideOverlap:!0,...b},data:(0,l.SU)(u)},A=[];var C,z,I,T,E,P,L,F,N;null!==(n=(0,l.SU)(g))&&void 0!==n&&n.data.length&&A.push({type:"value",minInterval:m,nameGap:20,name:[`{a|${null===(C=(0,l.SU)(g))||void 0===C?void 0:C.name}}   `,"{b|''}"].join(""),nameTextStyle:{rich:{b:{width:12,height:4,color:null===(z=(0,l.SU)(g))||void 0===z?void 0:z.legendColor,fontSize:2,backgroundColor:null===(I=(0,l.SU)(g))||void 0===I?void 0:I.legendColor,borderRadius:2}}},splitLine:{lineStyle:{color:"#ebeef2",type:"dashed"}}}),null!==(r=(0,l.SU)(y))&&void 0!==r&&r.data.length&&A.push({type:"value",minInterval:m,position:null!==(T=(0,l.SU)(g))&&void 0!==T&&T.data.length?"right":"left",nameGap:20,name:null!==(E=(0,l.SU)(g))&&void 0!==E&&E.data.length?["{b|''}",`{a| ${null===(P=(0,l.SU)(y))||void 0===P?void 0:P.name}} `].join(""):[`{a| ${null===(L=(0,l.SU)(y))||void 0===L?void 0:L.name}}   `,"{b|''}"].join(""),nameTextStyle:{rich:{a:{},b:{width:12,height:4,color:null===(F=(0,l.SU)(y))||void 0===F?void 0:F.legendColor,fontSize:2,backgroundColor:null===(N=(0,l.SU)(y))||void 0===N?void 0:N.legendColor,borderRadius:2}}},splitLine:{lineStyle:{color:"#ebeef2",type:"dashed"}}});const H={grid:x,tooltip:{trigger:"axis"},xAxis:w,yAxis:A,series:S,textStyle:{fontSize:12,color:"#7E7E7E"}};v&&(H.dataZoom=[...v]),f&&(H.title={text:f,textAlign:"left",left:50,textStyle:{width:100,fontSize:16}});const{setOptions:U}=(0,a.l)(e,"default",h);u.value.length>0?(e.value&&(e.value.innerHTML=""),U(H)):(0,s.T)(f).mount(e.value)},u={xdata:(0,l.iH)(["www","数据库","其他","ATP","SSL","SMB","DCE","KERNEL"]),result:[{name:"高危",data:[0,435,90,340,320,270,360,400],legendColor:"#EC4F4F"},{name:"中危",data:[119,235,190,220,120,270,560,200],legendColor:"#FA8C16"},{name:"低危",data:[130,120,240,280,40,180,288,200],legendColor:"#0072EE"}]},p=[{left:[{offset:0,color:"rgba(236,79,79,0.9)"},{offset:1,color:"rgba(236,79,79,0.6)"}],right:[{offset:0,color:"rgba(236,79,79,1)"},{offset:1,color:"rgba(236,79,79,0.9)"}],top:[{offset:0,color:"rgba(236,79,79,1)"},{offset:1,color:"rgba(236,79,79,1)"}]},{left:[{offset:0,color:"rgba(250,140,22,0.9)"},{offset:1,color:"rgba(250,140,22,0.6)"}],right:[{offset:0,color:"rgba(250,140,22,1)"},{offset:1,color:"rgba(250,140,22,0.9)"}],top:[{offset:0,color:"rgba(250,140,22,1)"},{offset:1,color:"rgba(250,140,22,1)"}]},{left:[{offset:0,color:"rgba(0,114,238,0.9)"},{offset:1,color:"rgba(0,114,238,0.6)"}],right:[{offset:0,color:"rgba(0,114,238,1)"},{offset:1,color:"rgba(0,114,238,0.9)"}],top:[{offset:0,color:"rgba(0,114,238,1)"},{offset:1,color:"rgba(0,114,238,1)"}]}],y=(e,t)=>{const{title:o,grid:i={top:"10%",left:"8%",right:"3%",bottom:"10%"},result:n=u.result,xdata:r=u.xdata,colors:s=p,yAxisName:d="漏洞数量（个）",finished:c,legendOpt:f={},titleOpt:y={},xAxisAxisLabel:g={}}=t,h=((e,t,o)=>e.reduce(((e,a,l)=>{const i=t[l];return e.push({type:"custom",name:a.name,renderItem:(e,t)=>{let a=!0;(0===o[l][e.dataIndex]||l>0&&o[l][e.dataIndex]==o[l-1][e.dataIndex])&&(a=!1);const n=t.coord([t.value(0),o[l][e.dataIndex]]);n[0]=n[0]+0;const r=t.coord([t.value(0),o[l][e.dataIndex]-t.value(1)]);return r[0]=r[0]+0,t.value(1),{type:"group",x:0,y:0,children:a?[{type:"CubeLeft",shape:{api:t,wid:12,xValue:t.value(0),yValue:t.value(1),x:n[0],y:n[1],xAxisPoint:r},style:{fill:{type:"linear",x:0,x2:0,y:0,y2:1,colorStops:null==i?void 0:i.left}}},{type:"CubeRight",shape:{api:t,wid:12,xValue:t.value(0),yValue:t.value(1),x:n[0],y:n[1],xAxisPoint:r},style:{fill:{type:"linear",x:0,x2:0,y:0,y2:1,colorStops:i.right}}},{type:"CubeTop",shape:{api:t,wid:12,xValue:t.value(0),yValue:t.value(1),x:n[0],y:n[1],xAxisPoint:r},style:{fill:{type:"linear",x:0,x2:0,y:0,y2:1,colorStops:i.top}}}]:[]}},colorBy:"series",color:a.legendColor,data:a.data,clip:!0,stack:"zl"}),e}),[]))(n,s,n.reduce(((e,t,o)=>(e[o]=t.data.map(((t,a)=>t+(e[o-1]?e[o-1][a]:0))),e)),[])),x={grid:i,legend:{data:n.map((e=>({name:e.name,itemStyle:{color:e.legendColor||"inherit"}}))),textStyle:{fontSize:12,color:"#7e7e7e"},itemWidth:12,itemHeight:4,borderRadius:2,itemGap:15,top:"0%",right:"2%",selectedMode:!1,...f},tooltip:{trigger:"axis"},xAxis:{splitLine:{show:!0,lineStyle:{color:"#ebeef2",type:"dashed"}},axisLine:{lineStyle:{color:"#ebeef2",type:"dashed"}},axisLabel:{interval:0,hideOverlap:!0,...g},data:(0,l.SU)(r)},yAxis:{name:(0,l.SU)(d),splitLine:{lineStyle:{color:"#ebeef2",type:"dashed"}}},series:h,textStyle:{fontSize:12,color:"#7E7E7E"},dataZoom:[{type:"inside",show:!0,xAxisIndex:[0],start:0,end:100}]};o&&(x.title={text:o,textStyle:{fontSize:16},...{...{padding:[10,24],...y}}});const{setOptions:m,getInstance:b,resize:v}=(0,a.l)(e,"default",c);return m(x),{getInstance:b,resize:v}};var g=o(65499);const h={ydata:(0,l.iH)(["www","数据库","其他","ATP"]),result:[{name:"高",data:[0,15,20,40],legendColor:"#EC4F4F"},{name:"中",data:[10,35,90,20],legendColor:"#FA8C16"},{name:"低",data:[30,20,40,80],legendColor:"#0072EE"}]},x=[{left:[{offset:0,color:"rgba(236,79,79,0.9)"},{offset:1,color:"rgba(236,79,79,0.6)"}],right:[{offset:0,color:"rgba(236,79,79,1)"},{offset:1,color:"rgba(236,79,79,0.9)"}],top:[{offset:0,color:"rgba(236,79,79,1)"},{offset:1,color:"rgba(236,79,79,1)"}]},{left:[{offset:0,color:"rgba(250,140,22,0.9)"},{offset:1,color:"rgba(250,140,22,0.6)"}],right:[{offset:0,color:"rgba(250,140,22,1)"},{offset:1,color:"rgba(250,140,22,0.9)"}],top:[{offset:0,color:"rgba(250,140,22,1)"},{offset:1,color:"rgba(250,140,22,1)"}]},{left:[{offset:0,color:"rgba(0,114,238,0.9)"},{offset:1,color:"rgba(0,114,238,0.6)"}],right:[{offset:0,color:"rgba(0,114,238,1)"},{offset:1,color:"rgba(0,114,238,0.9)"}],top:[{offset:0,color:"rgba(0,114,238,1)"},{offset:1,color:"rgba(0,114,238,1)"}]}],m=(e,t)=>{const{title:o,grid:i={top:"25%",left:"20%",right:"15%",bottom:"10%",containLabel:!0},result:n=h.result,ydata:r=h.ydata,colors:s=x,xAxisName:d="风险数（个）",finished:c}=t,f=n.reduce(((e,t,o)=>(e[o]=t.data.map(((t,a)=>t+(e[o-1]?e[o-1][a]:0))),e)),[]),u=((e,t,o)=>{const a={duration:1e3,easing:"cubicInOut",delay:10};return e.reduce(((e,l,i)=>{const n=t[i];return e.push({type:"custom",name:l.name,renderItem:(e,t)=>{let l=!0;(0===o[i][e.dataIndex]||i>0&&o[i][e.dataIndex]==o[i-1][e.dataIndex])&&(l=!1);const r=o[i][e.dataIndex],s=o[i][e.dataIndex]-t.value(0),d=t.coord([r,t.value(1)]),c=t.coord([s,t.value(1)]),f={style:{opacity:0},y:t.value(1)};return{type:"group",x:0,y:0,children:l?[{type:"CubeLeftByHorizontal",shape:{api:t,wid:1,xValue:t.value(0),yValue:t.value(1),x:d[0],y:d[1],yAxisPoint:c},style:{fill:{type:"linear",x:0,x2:0,y:0,y2:1,colorStops:null==n?void 0:n.left}},enterAnimation:a,enterFrom:f},{type:"CubeRightByHorizontal",shape:{api:t,wid:1,xValue:t.value(0),yValue:t.value(1),x:d[0],y:d[1],yAxisPoint:c},style:{fill:{type:"linear",x:0,x2:0,y:0,y2:1,colorStops:n.right}},enterAnimation:a,enterFrom:f},{type:"CubeTopByHorizontal",shape:{api:t,wid:1,xValue:t.value(0),yValue:t.value(1),x:d[0],y:d[1],yAxisPoint:c},style:{fill:{type:"linear",x:0,x2:0,y:0,y2:1,colorStops:n.top}},enterAnimation:a,enterFrom:f}]:[]}},colorBy:"series",color:l.legendColor,data:l.data,clip:!0}),e}),[])})(n,s,f),p={grid:i,legend:{data:n.map((e=>({name:e.name,itemStyle:{color:e.legendColor||"inherit"}}))),textStyle:{fontSize:12,color:"#7e7e7e"},itemWidth:12,itemHeight:4,borderRadius:2,itemGap:15,top:"10%",right:"10%",selectedMode:!1},tooltip:{trigger:"axis"},xAxis:{name:(0,l.SU)(d),max:e=>{const t=e.max,o=Array(r.value.length).fill(0);return n.forEach((e=>{for(const t in e.data)o[t]=o[t]+=e.data[t]})),Math.max(t,...o)+Math.ceil(Math.max(t,...o)/10)},splitLine:{show:!0,lineStyle:{color:"#ebeef2",type:"dashed"}},axisLine:{lineStyle:{color:"#ebeef2",type:"dashed"}},axisLabel:{interval:0,hideOverlap:!0}},yAxis:{data:(0,l.SU)(r),axisLine:{show:!1,lineStyle:{color:"white"}}},series:u,textStyle:{fontSize:12,color:"#7E7E7E"}};o&&(p.title={text:o,padding:[10,24]});const{setOptions:y,getInstance:g,resize:m}=(0,a.l)(e,"default",c);return y(p),{getInstance:g,resize:m}};o(5255)},78951:(e,t,o)=>{o.d(t,{T:()=>s});var a=o(12534),l=o(49963),i=o(66252),n=o(79414);const{t:r}=n.i18n.global,s=e=>{const t=a.Z.PRESENTED_IMAGE_SIMPLE;return(0,l.ri)({render:()=>(0,i.h)("div",{},[(0,i.h)("span",{style:{fontSize:"16px",fontWeight:600,position:"relative",top:"10px",left:"50px",display:e?"":"none"}},e),(0,i.h)(a.Z,{image:t,description:r("custom.Nodata"),style:{marginTop:"100px"}})])})}}}]);