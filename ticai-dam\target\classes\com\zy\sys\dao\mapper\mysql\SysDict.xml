<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.sys.dao.SysDictDAO">

    <sql id="meta">
        a.CODE_
        ,a.NAME_
        ,a.LENGTH_
        ,a.TYPE_
        ,a.FLAG_EDIT_
        ,a.ORD_
    </sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.sys.orm.SysDict">
        insert into SYS_DICT(CODE_,NAME_,LENGTH_,TYPE_,ORD_,FLAG_EDIT_)
        values(#{code},#{name},#{length},#{type},#{ord},#{flagEdit})
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.sys.orm.SysDict">
        update SYS_DICT
        set CODE_=#{code},NAME_=#{name},LENGTH_=#{length},TYPE_=#{type},ORD_=#{ord},FLAG_EDIT_=#{flagEdit}
        where ID_=#{id}
    </update>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.sys.orm.SysDict">
        select
        <include refid="meta"/>
        from SYS_DICT a
        <where>
            <if test="keyword != null">and a.NAME_ like concat('%',#{keyword},'%')</if>
        </where>
        order by a.ORD_, a.CODE_
    </select>

    <!-- 获取唯一的系统-字典目录数据 -->
    <select id="findOne" resultType="com.zy.sys.orm.SysDict">
        select
        <include refid="meta"/>
        from SYS_DICT a where a.ID_=#{0}
    </select>

    <!-- 删除 -->
    <delete id="delete">
        delete from SYS_DICT where ID_=#{0}
    </delete>

    <select id="findCodeByDict" resultType="com.zy.sys.orm.SysCode">
        select
        a.DICT_
        ,a.CODE_
        ,a.NAME_
        ,a.MEMO_
        ,a.ORD_
        ,a.STATUS_
        from SYS_CODE a where a.DICT_=#{0}
        order by a.ORD_,a.CODE_
    </select>

    <insert id="insertCode">
        insert into SYS_CODE(DICT_,CODE_,NAME_,MEMO_,ORD_,STATUS_)
        values(#{dict},#{code},#{name},#{memo},#{ord},#{status})
    </insert>

    <delete id="deleteCodeByDict">
        delete from SYS_CODE where DICT_=#{0}
    </delete>
</mapper>
