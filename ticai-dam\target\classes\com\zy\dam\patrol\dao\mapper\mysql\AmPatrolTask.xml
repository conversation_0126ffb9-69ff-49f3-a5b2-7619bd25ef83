<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.patrol.dao.AmPatrolTaskDAO">

    <sql id="meta">
			a.ID_
			,a.PLAN_
			,a.START_TIME_
			,a.END_TIME_
			,a.EXEC_STATUS_
			,a.NO_
			,a.PUSER_
			,a.EUSER_
			,a.EXEC_START_
			,a.EXEC_END_
			,a.START_LNG_
			,a.START_LAT_
			,a.END_LNG_
			,a.END_LAT_
			,a.RESULT_
			,a.MEMO_
			,a.PERIOD_STATUS_
			,a.CTIME_
			,a.CUSER_
			,a.FLAG_
	</sql>
    <insert id="insert" parameterType="com.zy.dam.patrol.orm.AmPatrolTask">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into AM_PATROL_TASK(ID_,PLAN_,START_TIME_,END_TIME_,EXEC_STATUS_,NO_,PUSER_,EUSER_,EXEC_START_,EXEC_END_,START_LNG_,START_LAT_,END_LNG_,END_LAT_,PERIOD_STATUS_,CTIME_,CUSER_,FLAG_)
        values(#{id},#{plan},#{startTime},#{endTime},'1',#{no},#{puser},#{euser},#{execStart},#{execEnd},#{startLng},#{startLat},#{endLng},#{endLat},#{periodStatus},now(),#{cuser},'1')
    </insert>

    <select id="findByPlan" resultType="com.zy.dam.patrol.vo.PatrolTaskVo">
        select
        <include refid="meta"/>
        from AM_PATROL_TASK a where a.FLAG_='1' and a.PLAN_=#{0}
        order by START_TIME_
    </select>

    <update id="deleteByPlan">
		update AM_PATROL_TASK set FLAG_='9' where FLAG_='1' and PLAN_=#{0}
	</update>

    <select id="page" resultType="com.zy.dam.patrol.vo.PatrolTaskVo">
        select
        <include refid="meta"/>
        ,b.NAME_ plan_name,b.TYPE_ plan_type
        ,o.NAME_ euser_name,o.PHONE_ euser_phone
        from AM_PATROL_TASK a left join SYS_USER o on o.ID_=a.EUSER_, AM_PATROL_PLAN b
        where a.FLAG_='1' and b.FLAG_='1' and a.PLAN_=b.ID_
        <if test="keyword != null">and (b.NAME_ like concat('%',#{keyword},'%') or b.NO_ like concat('%',#{keyword},'%') or a.NO_ like concat('%',#{keyword},'%') )</if>
        <if test="periodStatus != null">and a.PERIOD_STATUS_=#{periodStatus}</if>
        <if test="execStatus != null">and a.EXEC_STATUS_=#{execStatus}</if>
        <if test="userName != null">and (o.NAME_ like concat('%',#{userName},'%') or o.NO_ like concat('%',#{userName},'%') )</if>
        <if test="begin != null">and a.START_TIME_ &gt;=#{begin}</if>
        <if test="end != null">and a.END_TIME_ &lt; date_add(#{end}, interval 1 day)</if>
        <if test="result != null">and a.RESULT_=#{result}</if>
        <if test="orderTimeDesc != null">order by a.START_TIME_ desc</if>
        <if test="orderTimeDesc == null">order by a.START_TIME_</if>
    </select>

    <select id="remove">
		update AM_PATROL_TASK set EXEC_STATUS_='8' where ID_=#{0}
	</select>

    <select id="findVo" resultType="com.zy.dam.patrol.vo.PatrolTaskVo">
        select
        <include refid="meta"/>
        ,b.NAME_ plan_name,b.TYPE_ plan_type
        ,o.NAME_ euser_name,o.PHONE_ euser_phone
        from AM_PATROL_TASK a left join SYS_USER o on o.ID_=a.EUSER_, AM_PATROL_PLAN b
        where a.FLAG_='1' and b.FLAG_='1' and a.PLAN_=b.ID_
        and a.ID_=#{0}
    </select>
</mapper>
