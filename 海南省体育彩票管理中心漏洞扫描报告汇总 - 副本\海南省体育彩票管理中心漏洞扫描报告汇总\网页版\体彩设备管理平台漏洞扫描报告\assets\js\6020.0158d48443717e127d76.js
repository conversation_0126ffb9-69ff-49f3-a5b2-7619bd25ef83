"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[6020],{66020:(a,e,d)=>{d.r(e),d.d(e,{default:()=>f});var c=d(66252),t=d(2262),s=d(79414);const f=(0,c.aZ)({__name:"FailedHostsInfoList",props:{paramsData:{},serialNum:{},source:{},taskType:{},taskTypeNum:{},exportTypeTemplate:{}},setup(a){const e=a,{t:d}=s.i18n.global,f=(0,c.Fl)((()=>(0,t.SU)(e.paramsData))),b=[{title:d("a68595c99d93674665c126f168481901c9d7e8837a8ec3a078b0650f268131b0ea1ebdad70aff2c04a9c1d212b73cf32be7cd60b98dd3f87ea3edcbef6456d00df5c9703107888341070de6c5f948c28"),dataIndex:"ip",key:"ip",width:280},{title:d("a68595c99d93674665c126f168481901c9d7e8837a8ec3a078b0650f268131b0ea1ebdad70aff2c04a9c1d212b73cf325c23fc7db8b9eeb18d1be9fec5043175593237bc5a3748f6414ddcdf33d891af"),dataIndex:"reason",key:"reason",width:280}];return(a,e)=>{const d=(0,c.up)("a-table");return(0,c.wg)(),(0,c.j4)(d,{columns:b,dataSource:f.value.failed_hosts_info,rowKey:"ip",pagination:!1},null,8,["dataSource"])}}})}}]);