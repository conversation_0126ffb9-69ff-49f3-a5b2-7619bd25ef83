<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.wx.dao.WxBannerDAO">

    <sql id="meta">
			a.ID_
			,a.NAME_
			,a.PIC_
			,a.URL_
			,a.LINK1_
			,a.LINK2_
			,a.ORD_
			,a.STATUS_
			,a.CTIME_
			,a.CUSER_
			,a.MTIME_
			,a.MUSER_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into WX_BANNER(ID_,NAME_,PIC_,URL_,LINK1_,LINK2_,ORD_,STATUS_,CTIME_,CUSER_,MTIME_,MUSER_)
        values(#{id},#{name},#{pic},#{url},#{link1},#{link2},#{ord},'0',now(),#{cuser},now(),#{muser})
    </insert>

    <!-- 更新数据 -->
    <update id="update">
		update WX_BANNER
		set NAME_=#{name},PIC_=#{pic},URL_=#{url},LINK1_=#{link1},LINK2_=#{link2},ORD_=#{ord},STATUS_='0',MTIME_=now(),MUSER_=#{muser}
		where ID_=#{id}
	</update>

    <select id="listAll" resultType="com.zy.dam.wx.orm.WxBanner">
        select
        <include refid="meta"/>
        from WX_BANNER a
        where a.STATUS_!='9'
        order by a.ORD_ desc
    </select>

    <select id="listValid" resultType="com.zy.dam.wx.orm.WxBanner">
        select
        <include refid="meta"/>
        from WX_BANNER a
        where a.STATUS_='1'
        order by a.ORD_ desc
    </select>

    <update id="updateStatus">
		update WX_BANNER set STATUS_=#{1} where ID_=#{0}
	</update>

</mapper>
