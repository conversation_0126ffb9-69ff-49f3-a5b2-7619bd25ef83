<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.sys.dao.SysAccessDAO">

    <sql id="meta">
			a.ID_
			,a.URI_
			,a.TIME_
			,a.IP_
			,a.USER_
			,a.CONTENT_
	</sql>
    <insert id="insert" parameterType="com.zy.sys.orm.SysAccess">
        insert into SYS_ACCESS(ID_,URI_,TIME_,IP_,USER_,CONTENT_)
        values(uuid(),#{0},now(),#{1},#{2},#{3})
    </insert>

    <update id="update" parameterType="com.zy.sys.orm.SysAccess">
		update SYS_ACCESS
		set URI_=#{uri},TIME_=#{time},IP_=#{ip},USER_=#{user},CONTENT_=#{content}
		where ID_=#{id}
	</update>

    <select id="page" resultType="com.zy.sys.vo.AccessVo">
        select
        <include refid="meta"/>
        ,b.NAME_ user_name
        ,(select m.NAME_ from SYS_ACCESS_NAME m where a.URI_ like m.URI_ limit 0,1) uri_name
        from SYS_ACCESS a left join SYS_USER b on a.USER_=b.ID_
        <where>
            <if test="keyword != null">and (b.NAME_ like concat('%',#{keyword},'%') or b.ACCOUNT_ like concat('%',#{keyword},'%'))</if>
            <if test="begin != null">and a.TIME_ &gt;= #{begin}</if>
            <if test="end != null">and a.TIME_ &lt; date_add(#{end}, interval 1 day)</if>
        </where>
        order by a.TIME_ desc
    </select>

    <select id="findOne" resultType="com.zy.sys.orm.SysAccess">
        select
        <include refid="meta"/>
        from SYS_ACCESS a where a.ID_=#{0}
    </select>

    <delete id="delete">
		delete from SYS_ACCESS where ID_=#{0}
	</delete>

    <select id="findVo" resultType="com.zy.sys.vo.AccessVo">
        select
        <include refid="meta"/>
        from SYS_ACCESS a where a.ID_=#{0}
    </select>

</mapper>
