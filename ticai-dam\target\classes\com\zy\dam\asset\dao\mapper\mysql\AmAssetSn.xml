<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.asset.dao.AmAssetSnDAO">

    <sql id="meta">
			a.ID_
			,a.ASSET_
			,a.LAST_SN_
			,a.LAST_ASSET_
			,a.NOW_SN_
			,a.USER_
			,a.TIME_
	</sql>
    <insert id="insert" parameterType="com.zy.dam.asset.orm.AmAssetSn">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into AM_ASSET_SN(ID_,ASSET_,LAST_SN_,LAST_ASSET_,NOW_SN_,USER_,TIME_)
        values(#{id},#{asset},#{lastSn},#{lastAsset},#{nowSn},#{user},#{time})
    </insert>

    <update id="update" parameterType="com.zy.dam.asset.orm.AmAssetSn">
		update AM_ASSET_SN
		set ASSET_=#{asset},LAST_SN_=#{lastSn},LAST_ASSET_=#{lastAsset},NOW_SN_=#{nowSn},USER_=#{user},TIME_=#{time}
		where ID_=#{id}
	</update>

    <select id="findOne" resultType="com.zy.dam.asset.orm.AmAssetSn">
        select
        <include refid="meta"/>
        from AM_ASSET_SN a where a.FLAG_='1'and a.ASSET_=#{0}
    </select>

    <delete id="delete">
		delete from AM_ASSET_SN where ID_=#{0}
	</delete>

</mapper>
