"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[3803],{83803:(e,a,t)=>{t.r(a),t.d(a,{default:()=>u});var r=t(66252),s=t(2262),p=t(425),l=t(38610);const u=(0,r.aZ)({__name:"VulnerabilityRiskCategory",props:{paramsData:{},exportTypeTemplate:{},source:{}},setup(e){const a=e,t=(0,r.Fl)((()=>(0,s.SU)(a.paramsData)));return(e,s)=>((0,r.wg)(!0),(0,r.iD)(r.HY,null,(0,r.Ko)(t.value.riskCategories,(e=>((0,r.wg)(),(0,r.j4)(p.Z,{key:e,title:e.name},{default:(0,r.w5)((()=>[(0,r.Wm)(l.Z,{data:e.data,name:e.name,source:a.source,exportTypeTemplate:a.exportTypeTemplate},null,8,["data","name","source","exportTypeTemplate"])])),_:2},1032,["title"])))),128))}})}}]);