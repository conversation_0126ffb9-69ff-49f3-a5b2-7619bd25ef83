<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.sys.dao.SysRoleDAO">

    <sql id="meta">
        a.ID_
        ,a.NAME_
        ,a.MEMO_
        ,a.ORD_
        ,a.STATUS_
    </sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.sys.orm.SysRole">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into SYS_ROLE(ID_,NAME_,MEMO_,ORD_,STATUS_)
        values(#{id},#{name},#{memo},#{ord},'1')
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.sys.orm.SysRole">
        update SYS_ROLE
        set NAME_=#{name},
        MEMO_=#{memo},
        ORD_=#{ord}
        where ID_ = #{id}
    </update>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.sys.orm.SysRole">
        select
        <include refid="meta"/>
        from SYS_ROLE a
        where a.STATUS_ != '9'
        <if test="keyword != null">and a.NAME_ like concat('%',#{keyword},'%')</if>
        order by a.ORD_ desc
    </select>

    <!-- 获取唯一的系统角色信息数据 -->
    <select id="findOne" resultType="com.zy.sys.orm.SysRole">
        select
        <include refid="meta"/>
        from SYS_ROLE a where a.ID_=#{0}
    </select>

    <select id="findAll" resultType="com.zy.sys.orm.SysRole">
        select
        <include refid="meta"/>
        from SYS_ROLE a
        where a.STATUS_ != '9'
        order by a.ORD_
    </select>

    <!-- 删除 -->
    <delete id="delete">
        delete
        from SYS_ROLE
        where ID_ = #{0}
    </delete>

    <select id="findMenuByRole" resultType="String">
        select MENU_
        from SYS_ROLE_MENU
        where ROLE_ = #{0}
    </select>

    <insert id="insertRoleMenu">
        insert into SYS_ROLE_MENU(ROLE_, MENU_)
        values (#{0}, #{1})
    </insert>

    <delete id="deleteMenuByRole">
        delete
        from SYS_ROLE_MENU
        where ROLE_ = #{0}
    </delete>
</mapper>
