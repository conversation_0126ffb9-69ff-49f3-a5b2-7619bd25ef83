<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.sys.dao.SysSmsDAO">

    <sql id="meta">
			a.ID_
			,a.MOBILE_
			,a.CONTENT_
			,a.TYPE_
			,a.BID_
			,a.TIME_
			,a.USER_
	</sql>

    <insert id="insert" parameterType="com.zy.sys.orm.SysSms">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into SYS_SMS(ID_,MOBILE_,CONTENT_,TYPE_,BID_,TIME_,USER_)
        values(#{id},#{mobile},#{content},#{type},#{bid},now(),#{user})
    </insert>

    <select id="page" resultType="com.zy.sys.vo.SmsVo">
        select
        <include refid="meta"/>
        from SYS_SMS a
        <where>
            <if test="mobile != null">and a.MOBILE_ like concat('%',#{mobile},'%')</if>
            <if test="begin != null">and a.TIME_&gt;=#{begin}</if>
            <if test="end != null">and a.TIME_&lt;date_add(#{end}, interval 1 day)</if>
        </where>
        order by a.TIME_ desc
    </select>

</mapper>
