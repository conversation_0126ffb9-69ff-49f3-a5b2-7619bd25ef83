<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.sys.dao.SysNoDAO">

    <sql id="meta">
			a.TYPE_
			,a.PREFIX_
			,a.SUFFIX_
			,a.DATE_FORMAT_
			,a.LEN_
			,a.STATUS_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.sys.orm.SysNo">
		insert into SYS_NO(TYPE_,PREFIX_,SUFFIX_,DATE_FORMAT_,LEN_,STATUS_)
		values(#{type},#{prefix},#{suffix},#{dateFormat},#{len},'1')
	</insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.sys.orm.SysNo">
		update SYS_NO
		set PREFIX_=#{prefix},SUFFIX_=#{suffix},DATE_FORMAT_=#{dateFormat},LEN_=#{len},STATUS_='1'
		where TYPE_=#{type}
	</update>

    <select id="list" resultType="com.zy.sys.orm.SysNo">
        select
        <include refid="meta"/>
        from SYS_NO a
        order by a.TYPE_,a.ORG_
    </select>

    <select id="listByType" resultType="com.zy.sys.orm.SysNo">
        select
        <include refid="meta"/>
        from SYS_NO a
        where a.TYPE_ in (
        <foreach collection="types" item="type" separator=",">#{type}</foreach>
        )
        order by a.TYPE_
    </select>

    <!-- 获取唯一的基础-编码规则数据 -->
    <select id="findOne" resultType="com.zy.sys.orm.SysNo">
        select
        <include refid="meta"/>
        from SYS_NO a where a.TYPE_=#{0}
    </select>

    <select id="findValue" resultType="com.zy.sys.orm.SysNoValue">
        select TYPE_,DATE_,SEQ_,NO_ from SYS_NO_VALUE where TYPE_=#{0} and DATE_=#{1}
    </select>

    <update id="insertValue">
		insert into SYS_NO_VALUE(TYPE_,DATE_,SEQ_,NO_) values(#{0}, #{1}, #{2}, #{3})
	</update>

    <update id="updateValue">
		update SYS_NO_VALUE set SEQ_=#{2},NO_=#{3} where TYPE_=#{0} and DATE_=#{1}
	</update>

    <update id="disable">
		update SYS_NO set STATUS_='2' where TYPE_=#{0}
	</update>

</mapper>
