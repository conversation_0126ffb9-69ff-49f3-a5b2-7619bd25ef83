package com.zy.dam.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * 时区检查工具类
 * 用于检查和诊断系统时区配置问题
 */
@Component
public class TimezoneCheckUtil {

    private static final Logger logger = LoggerFactory.getLogger(TimezoneCheckUtil.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 应用启动时自动检查时区配置
     */
    @PostConstruct
    public void checkTimezoneOnStartup() {
        logger.info("=== 时区配置检查开始 ===");
        
        try {
            // 检查JVM时区
            checkJvmTimezone();
            
            // 检查数据库时区
            checkDatabaseTimezone();
            
            // 检查时间一致性
            checkTimeConsistency();
            
        } catch (Exception e) {
            logger.error("时区检查过程中发生错误", e);
        }
        
        logger.info("=== 时区配置检查结束 ===");
    }

    /**
     * 检查JVM时区设置
     */
    public void checkJvmTimezone() {
        TimeZone defaultTimeZone = TimeZone.getDefault();
        ZoneId systemZoneId = ZoneId.systemDefault();
        
        logger.info("JVM默认时区: {}", defaultTimeZone.getID());
        logger.info("JVM时区显示名称: {}", defaultTimeZone.getDisplayName());
        logger.info("JVM时区偏移量: {} 小时", defaultTimeZone.getRawOffset() / (1000 * 60 * 60));
        logger.info("系统默认时区ID: {}", systemZoneId.getId());
        logger.info("当前JVM时间: {}", ZonedDateTime.now());
    }

    /**
     * 检查数据库时区设置
     */
    public void checkDatabaseTimezone() {
        try {
            // 检查数据库系统时区
            List<Map<String, Object>> systemTimezone = jdbcTemplate.queryForList("SELECT @@system_time_zone as system_timezone");
            if (!systemTimezone.isEmpty()) {
                logger.info("数据库系统时区: {}", systemTimezone.get(0).get("system_timezone"));
            }

            // 检查数据库会话时区
            List<Map<String, Object>> sessionTimezone = jdbcTemplate.queryForList("SELECT @@session.time_zone as session_timezone");
            if (!sessionTimezone.isEmpty()) {
                logger.info("数据库会话时区: {}", sessionTimezone.get(0).get("session_timezone"));
            }

            // 检查数据库全局时区
            List<Map<String, Object>> globalTimezone = jdbcTemplate.queryForList("SELECT @@global.time_zone as global_timezone");
            if (!globalTimezone.isEmpty()) {
                logger.info("数据库全局时区: {}", globalTimezone.get(0).get("global_timezone"));
            }

            // 检查数据库当前时间
            List<Map<String, Object>> dbNow = jdbcTemplate.queryForList("SELECT NOW() as db_now, UTC_TIMESTAMP() as utc_now");
            if (!dbNow.isEmpty()) {
                logger.info("数据库当前时间: {}", dbNow.get(0).get("db_now"));
                logger.info("数据库UTC时间: {}", dbNow.get(0).get("utc_now"));
            }

        } catch (Exception e) {
            logger.error("检查数据库时区时发生错误", e);
        }
    }

    /**
     * 检查时间一致性
     */
    public void checkTimeConsistency() {
        try {
            // 获取JVM当前时间
            long jvmTime = System.currentTimeMillis();
            
            // 获取数据库当前时间
            List<Map<String, Object>> dbTime = jdbcTemplate.queryForList("SELECT UNIX_TIMESTAMP(NOW()) * 1000 as db_timestamp");
            
            if (!dbTime.isEmpty()) {
                long dbTimestamp = ((Number) dbTime.get(0).get("db_timestamp")).longValue();
                long timeDiff = Math.abs(jvmTime - dbTimestamp);
                
                logger.info("JVM时间戳: {}", jvmTime);
                logger.info("数据库时间戳: {}", dbTimestamp);
                logger.info("时间差异: {} 毫秒", timeDiff);
                
                if (timeDiff > 5000) { // 超过5秒差异
                    logger.warn("⚠️ 警告: JVM和数据库时间差异超过5秒，可能存在时区配置问题！");
                } else {
                    logger.info("✅ JVM和数据库时间基本一致");
                }
            }
            
        } catch (Exception e) {
            logger.error("检查时间一致性时发生错误", e);
        }
    }

    /**
     * 手动触发时区检查（用于调试）
     */
    public void manualCheck() {
        logger.info("=== 手动时区检查 ===");
        checkJvmTimezone();
        checkDatabaseTimezone();
        checkTimeConsistency();
    }

    /**
     * 获取时区诊断报告
     */
    public String getTimezoneReport() {
        StringBuilder report = new StringBuilder();
        
        try {
            // JVM信息
            TimeZone defaultTimeZone = TimeZone.getDefault();
            report.append("JVM时区: ").append(defaultTimeZone.getID()).append("\n");
            report.append("JVM时区偏移: ").append(defaultTimeZone.getRawOffset() / (1000 * 60 * 60)).append("小时\n");
            
            // 数据库信息
            List<Map<String, Object>> dbInfo = jdbcTemplate.queryForList(
                "SELECT @@system_time_zone as sys_tz, @@session.time_zone as sess_tz, NOW() as db_now"
            );
            
            if (!dbInfo.isEmpty()) {
                Map<String, Object> info = dbInfo.get(0);
                report.append("数据库系统时区: ").append(info.get("sys_tz")).append("\n");
                report.append("数据库会话时区: ").append(info.get("sess_tz")).append("\n");
                report.append("数据库当前时间: ").append(info.get("db_now")).append("\n");
            }
            
        } catch (Exception e) {
            report.append("获取时区信息时发生错误: ").append(e.getMessage());
        }
        
        return report.toString();
    }
}
