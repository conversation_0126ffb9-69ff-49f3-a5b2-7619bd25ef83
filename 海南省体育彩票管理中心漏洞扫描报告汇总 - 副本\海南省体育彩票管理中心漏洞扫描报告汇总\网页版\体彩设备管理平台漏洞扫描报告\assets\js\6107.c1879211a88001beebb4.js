"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[6107],{96107:(l,a,n)=>{n.r(a),n.d(a,{default:()=>e});var i=n(66252),t=n(3577);const u={style:{margin:"20px 0"}},e=(0,i.aZ)({__name:"AllLinkList",props:{paramsData:{},source:{}},setup(l){const a=l,n=[{title:"URL",dataIndex:"url",key:"url"}],e=(0,i.Fl)((()=>{var l,n;return(null===(l=a.paramsData)||void 0===l||null===(l=l.all_link)||void 0===l?void 0:l.all_link_list)||(null===(n=a.paramsData)||void 0===n||null===(n=n.all_link)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.all_link_list)||[]}));return(l,r)=>{var s;const o=(0,i.up)("a-table");return(0,i.wg)(),(0,i.iD)(i.HY,null,[(0,i._)("p",u," 所有链接列表总数为："+(0,t.zw)(null===(s=a.paramsData)||void 0===s||null===(s=s.all_link)||void 0===s||null===(s=s.all_link_list)||void 0===s?void 0:s.length),1),(0,i.Wm)(o,{columns:n,dataSource:e.value,rowKey:"domainName",pagination:!1},null,8,["dataSource"])],64)}}})}}]);