<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.wx.dao.WxUserDAO">

    <sql id="meta">
			a.ID_
			,a.USER_
			,a.MA_OPEN_ID_
			,a.MA_UNION_ID_
			,a.WX_OPEN_ID_
			,a.NICK_
			,a.AVATAR_
			,a.MOBILE_
			,a.GENDER_
			,a.LOGIN_TIME_
			,a.LOGIN_IP_
			,a.LAST_TIME_
			,a.LAST_IP_
			,a.SMARK_
			,a.STIME_
			,a.STATUS_
			,a.CTIME_
			,a.CUSER_
			,a.MTIME_
			,a.MUSER_
	</sql>

    <select id="page" resultType="com.zy.dam.wx.vo.WxUserVo">
        select
        <include refid="meta"/>
        ,b.NAME_ user_name
        ,b.ACCOUNT_ user_account
        ,b.<PERSON>HONE_ user_phone
        from WX_USER a left join SYS_USER b on b.ID_=a.USER_
        <where>
            <if test="keyword != null">and (a.NICK_ like concat('%',#{keyword},'%') or a.MOBILE_ like concat('%',#{keyword},'%'))</if>
        </where>
        order by a.CTIME_ desc
    </select>

</mapper>
