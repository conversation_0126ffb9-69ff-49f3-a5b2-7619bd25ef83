<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.report.dao.RpZdjZbtDAO">

    <sql id="bill_meta">
        a.ID_
        ,a.DEPT_
        ,a.MONTH_
        ,a.STATUS_
        ,a.BUILD_TIME_
        ,a.BUILD_USER_
        ,a.MEMO_
        ,a.CHECK_TIME_
        ,a.CHECK_USER_
        ,a.CHECK_MEMO_
    </sql>
    <sql id="meta">
        a.ID_
        ,a.BILL_
        ,a.DEPT_
        ,a.MONTH_
        ,a.SEQ_
        ,a.NAME_
        ,a.SPEC_
        ,a.TOTAL_
        ,a.RESERVE_
        ,a.LAST_CENTER_
        ,a.LAST_LOCATION_
        ,a.LAST_STOCK_
        ,a.PRODUCT_NUM_
        ,a.BACK_NUM_
        ,a.PUTIN_NUM_
        ,a.SCRAP_NUM_
        ,a.CURR_TOTAL_
        ,a.CURR_RESERVE_
        ,a.CURR_CENTER_
        ,a.CURR_LOCATION_
        ,a.CURR_STOCK_
        ,a.STATUS_
    </sql>
    <select id="findBillId" resultType="String">
        select ID_ from RP_ZDJ_ZBT_BILL where DEPT_=#{0} and MONTH_=#{1}
    </select>
    <insert id="insertBill" parameterType="com.zy.dam.report.orm.RpZdjZbt">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into RP_ZDJ_ZBT_BILL(ID_,DEPT_,MONTH_,STATUS_,BUILD_TIME_,BUILD_USER_,MEMO_)
        values(#{id},#{dept},#{month},'1',now(),#{buildUser},#{memo})
    </insert>

    <update id="updateBill" parameterType="com.zy.dam.report.orm.RpZdjZbt">
        update RP_ZDJ_ZBT_BILL
        set MEMO_=#{memo}
        where ID_=#{id}
    </update>

    <update id="checkBill">
        update RP_ZDJ_ZBT_BILL set STATUS_='2',CHECK_TIME_=now(),CHECK_USER_=#{1},CHECK_MEMO_=#{2} where ID_=#{0}
    </update>

    <insert id="insert" parameterType="com.zy.dam.report.orm.RpZdjZbt">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into
        RP_ZDJ_ZBT(ID_,BILL_,DEPT_,MONTH_,SEQ_,NAME_,SPEC_,TOTAL_,RESERVE_,LAST_CENTER_,LAST_LOCATION_,LAST_STOCK_,PRODUCT_NUM_,BACK_NUM_,PUTIN_NUM_,SCRAP_NUM_,CURR_TOTAL_,CURR_RESERVE_,CURR_CENTER_,CURR_LOCATION_,CURR_STOCK_,STATUS_)
        values(#{id},#{bill},#{dept},#{month},#{seq},#{name},#{spec},#{total},#{reserve},#{lastCenter},#{lastLocation},#{lastStock},#{productNum},#{backNum},#{putinNum},#{scrapNum},#{currTotal},#{currReserve},#{currCenter},#{currLocation},#{currStock},'1')
    </insert>

    <update id="updateStatusByBill">
        update RP_ZDJ_ZBT set STATUS_=#{1} where BILL_=#{0}
    </update>

    <select id="page" resultType="com.zy.dam.report.vo.ZdjZbtBillVo">
        select
        <include refid="bill_meta"/>
        ,b.NAME_ dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.BUILD_USER_) build_user_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.CHECK_USER_) check_user_name
        from RP_ZDJ_ZBT_BILL a, SYS_DEPT b
        where a.DEPT_=b.ID_
        <if test="dept != null">and a.DEPT_=#{dept}</if>
        <if test="month != null">and a.MONTH_=#{month}</if>
        order by a.MONTH_ desc, b.NO_
    </select>

    <select id="findBill" resultType="com.zy.dam.report.vo.ZdjZbtBillVo">
        select
        <include refid="bill_meta"/>
        ,b.NAME_ dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.BUILD_USER_) build_user_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.CHECK_USER_) check_user_name
        from RP_ZDJ_ZBT_BILL a, SYS_DEPT b
        where a.DEPT_=b.ID_ and a.ID_=#{0}
    </select>

    <select id="findBillByMonth" resultType="com.zy.dam.report.vo.ZdjZbtBillVo">
        select
        <include refid="bill_meta"/>
        from RP_ZDJ_ZBT_BILL a
        where a.DEPT_=#{0} and a.MONTH_=#{1}
    </select>

    <select id="findByBill" resultType="com.zy.dam.report.vo.ZdjZbtVo">
        select
        <include refid="meta"/>
        from RP_ZDJ_ZBT a
        where a.BILL_=#{0}
        order by a.SEQ_
    </select>

    <delete id="deleteBill">
        delete from RP_ZDJ_ZBT_BILL where ID_=#{0}
    </delete>

    <delete id="deleteByBill">
        delete from RP_ZDJ_ZBT where BILL_=#{0}
    </delete>

    <select id="findCurrTotalByDept" resultType="com.zy.dam.report.vo.ZdjZbtVo">
        select a.NAME_,a.SPEC_,count(0) curr_total,
        sum(case when LOCATION_ is null then 1 else 0 end) curr_stock,
        sum(case when LOCATION_ is null then 0 else 1 end) curr_location
        from AM_ASSET a where a.TYPE_=#{0} and a.DEPT_=#{1}
        group by a.NAME_,a.SPEC_
    </select>
    <!-- 类型：1-新增，2-退机，3-入库，4-报废 -->
    <select id="findBiz1" resultType="com.zy.dam.report.vo.ZdjZbtBizVo">
        select '1' type_,c.ID_ asset_id,c.NO_ asset_no,c.NAME_,c.SPEC_,b.TIME_,d.CODE_ location_code,d.NAME_
        location_name
        from AM_ASSET_CONSUMING_DETAIL a,AM_ASSET_CONSUMING b,AM_ASSET c, AM_LOCATION d
        where a.BILL_=b.ID_ and a.ASSET_=c.ID_ and b.LOCATION_=d.ID_ and b.STATUS_='2'
        and c.TYPE_=#{0} and b.DEPT_=#{1} and b.TIME_ &gt;=#{2} and b.TIME_ &lt; date_add(#{2}, interval 1 month)
    </select>

    <select id="countBiz1" resultType="int">
        select count(0)
        from AM_ASSET_CONSUMING_DETAIL a,AM_ASSET_CONSUMING b,AM_ASSET c, AM_LOCATION d
        where a.BILL_=b.ID_ and a.ASSET_=c.ID_ and b.LOCATION_=d.ID_ and b.STATUS_='2'
        and c.TYPE_=#{0} and b.DEPT_=#{1} and b.TIME_ &gt;=#{2} and b.TIME_ &lt; date_add(#{2}, interval 1 month)
        and c.NAME_=#{3} and c.SPEC_=#{4}
    </select>

    <select id="findBiz2" resultType="com.zy.dam.report.vo.ZdjZbtBizVo">
        select '2' type_,c.ID_ asset_id,c.NO_ asset_no,c.NAME_,c.SPEC_,b.TIME_,d.CODE_ location_code,d.NAME_
        location_name
        from AM_ASSET_BACK_DETAIL a,AM_ASSET_BACK b,AM_ASSET c, AM_LOCATION d
        where a.BILL_=b.ID_ and a.ASSET_=c.ID_ and a.LOCATION_=d.ID_
        and b.STATUS_='2' and c.TYPE_=#{0} and b.DEPT_=#{1} and b.TIME_ &gt;=#{2} and b.TIME_ &lt; date_add(#{2},
        interval 1 month)
    </select>

    <select id="countBiz2" resultType="int">
        select count(0)
        from AM_ASSET_BACK_DETAIL a,AM_ASSET_BACK b,AM_ASSET c, AM_LOCATION d
        where a.BILL_=b.ID_ and a.ASSET_=c.ID_ and a.LOCATION_=d.ID_
        and b.STATUS_='2' and c.TYPE_=#{0} and b.DEPT_=#{1} and b.TIME_ &gt;=#{2} and b.TIME_ &lt; date_add(#{2},
        interval 1 month)
        and c.NAME_=#{3} and c.SPEC_=#{4}
    </select>

    <select id="findBiz3" resultType="com.zy.dam.report.vo.ZdjZbtBizVo">
        select '3' type_,c.ID_ asset_id,c.NO_ asset_no,c.NAME_,c.SPEC_,b.TIME_
        from AM_ASSET_ALLOCATE_DETAIL a,AM_ASSET_ALLOCATE b,AM_ASSET c
        where a.BILL_=b.ID_ and a.ASSET_=c.ID_ and (a.DEPT_='A' or a.DEPT_ is null || a.DEPT_='')
        and b.STATUS_='2' and c.TYPE_=#{0} and b.DEPT_=#{1} and b.FTIME_ &gt;=#{2} and b.FTIME_ &lt; date_add(#{2},
        interval 1 month)
    </select>

    <select id="countBiz3" resultType="int">
        select count(0)
        from AM_ASSET_ALLOCATE_DETAIL a,AM_ASSET_ALLOCATE b,AM_ASSET c
        where a.BILL_=b.ID_ and a.ASSET_=c.ID_ and (a.DEPT_='A' or a.DEPT_ is null || a.DEPT_='')
        and b.STATUS_='2' and c.TYPE_=#{0} and b.DEPT_=#{1} and b.FTIME_ &gt;=#{2} and b.FTIME_ &lt; date_add(#{2},
        interval 1 month)
        and c.NAME_=#{3} and c.SPEC_=#{4}
    </select>

    <select id="findBiz4" resultType="com.zy.dam.report.vo.ZdjZbtBizVo">
        select '4' type_,c.ID_ asset_id,c.NO_ asset_no,c.NAME_,c.SPEC_,b.TIME_
        from AM_ASSET_SCRAP_DETAIL a,AM_ASSET_SCRAP b,AM_ASSET c
        where a.BILL_=b.ID_ and a.ASSET_=c.ID_ and b.STATUS_='2' and c.TYPE_=#{0} and a.DEPT_=#{1} and b.TIME_ &gt;=#{2}
        and b.TIME_ &lt; date_add(#{2}, interval 1 month)
    </select>

    <select id="countBiz4" resultType="int">
        select count(0)
        from AM_ASSET_SCRAP_DETAIL a,AM_ASSET_SCRAP b,AM_ASSET c
        where a.BILL_=b.ID_ and a.ASSET_=c.ID_ and b.STATUS_='2' and c.TYPE_=#{0} and a.DEPT_=#{1} and b.TIME_ &gt;=#{2}
        and b.TIME_ &lt; date_add(#{2}, interval 1 month)
        and c.NAME_=#{3} and c.SPEC_=#{4}
    </select>

    <insert id="insertBiz">
        insert into RP_ZDJ_ZBT_DETAIL(ID_,BILL_,DEPT_,MONTH_,TYPE_,ASSET_ID_,ASSET_NO_,LOCATION_CODE_,NAME_,SPEC_,TIME_)
        values(uuid(),#{bill},#{dept},#{month},#{type},#{assetId},#{assetNo},#{locationCode},#{name},#{spec},#{time})
    </insert>

    <delete id="deleteBizByBill">
        delete from RP_ZDJ_ZBT_DETAIL where BILL_=#{0}
    </delete>

    <select id="findBizByBill" resultType="com.zy.dam.report.vo.ZdjZbtBizVo">
        select a.ID_,a.BILL_,a.DEPT_,a.MONTH_,a.TYPE_,a.ASSET_ID_,a.ASSET_NO_,a.LOCATION_CODE_,a.NAME_,a.SPEC_,a.TIME_
        from RP_ZDJ_ZBT_DETAIL a
        where a.BILL_=#{0}
        order by a.TIME_
    </select>

</mapper>