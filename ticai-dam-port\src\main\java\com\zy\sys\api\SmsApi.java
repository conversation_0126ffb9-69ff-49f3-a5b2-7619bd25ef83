package com.zy.sys.api;

import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.zy.model.Result;
import com.zy.sys.dao.SysSmsDAO;
import com.zy.sys.orm.SysSms;
import com.zy.util.JsonUtils;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 短信接口
 */
@Service
public class SmsApi {

    private static final Logger log = LoggerFactory.getLogger(SmsApi.class);

    @Resource
    private SysSmsDAO dao;

    @Value("${zy.sms.domain}")
    private String domain;
    @Value("${zy.sms.accessKeyId}")
    private String accessKeyId;
    @Value("${zy.sms.accessKeySecret}")
    private String accessKeySecret;
    @Value("${zy.sms.woSign}")
    private String woSign;
    @Value("${zy.sms.woCode}")
    private String woCode;

    /**
     * 发送短信
     *
     * @param mobile  手机号码
     * @param content 内容
     * @param user    发送人
     * @param type    业务类型
     * @param biz     业务ID
     * @return 发送结果
     */
    public Result send(String mobile, String content, String user, String type, String biz) {
        log.info("开始发送短信，手机号：{}，内容：{}，用户：{}，类型：{}，业务ID：{}", mobile, content, user, type, biz);

        // 手机号码格式验证
        if (mobile == null || !mobile.matches("^1[3-9]\\d{9}$")) {
            log.warn("手机号码格式不正确：{}", mobile);
            return Result.err("手机号码格式不正确");
        }

        SendSmsRequest req = new SendSmsRequest();
        req.setPhoneNumbers(mobile);
        req.setSignName(woSign);
        req.setTemplateCode(woCode);
        req.setTemplateParam("{\"code\": \"" + content + "\"}");
        req.setSmsUpExtendCode("01");
        req.setOutId(biz);
        Config config = new Config().setAccessKeyId(accessKeyId).setAccessKeySecret(accessKeySecret);
        config.endpoint = domain;
        try {
            com.aliyun.dysmsapi20170525.Client client = new com.aliyun.dysmsapi20170525.Client(config);
            SendSmsResponse resp = client.sendSms(req);

            log.info("短信发送响应：{}", JsonUtils.toString(resp));

            // 用于排查
            log.info("短信发送详细信息 - 手机号：{}，签名：{}，模板：{}，参数：{}，业务ID：{}",
                mobile, woSign, woCode, req.getTemplateParam(), biz);

            if (!"OK".equalsIgnoreCase(resp.getBody().code)) {
                log.error("短信发送失败，错误码：{}，错误信息：{}", resp.getBody().code, resp.getBody().message);

                // 根据错误码返回具体错误信息
                String userMsg = getErrorMessage(resp.getBody().code, resp.getBody().message);
                return Result.err(userMsg);
            }

            log.info("短信发送成功，手机号：{}，bizId：{}，requestId：{}",
                mobile, resp.getBody().bizId, resp.getBody().requestId);

        } catch (Exception exception) {
            log.error("短信发送异常，手机号：{}，异常信息：{}", mobile, exception.getMessage(), exception);
            return Result.err("短信服务连接失败，请稍后重试");
        }

        // 记录发送日志
        SysSms sms = new SysSms();
        sms.setMobile(mobile);
        sms.setContent(content);
        sms.setUser(user);
        sms.setType(type);
        sms.setBid(biz);
        dao.insert(sms);

        return Result.ok();
    }

    /**
     * 根据阿里云短信错误码返回用户友好的错误信息
     */
    private String getErrorMessage(String code, String message) {
        switch (code) {
            case "isv.MOBILE_NUMBER_ILLEGAL":
                return "手机号码格式不正确";
            case "isv.MOBILE_COUNT_OVER_LIMIT":
                return "手机号码数量超过限制";
            case "isv.TEMPLATE_MISSING_PARAMETERS":
                return "短信模板参数缺失";
            case "isv.BUSINESS_LIMIT_CONTROL":
                return "发送频率过快，请稍后重试";
            case "isv.INVALID_PARAMETERS":
                return "参数无效";
            case "isv.SYSTEM_ERROR":
                return "系统错误，请稍后重试";
            case "isv.OUT_OF_SERVICE":
                return "短信服务暂时不可用";
            case "SignatureDoesNotMatch":
                return "短信服务配置错误";
            case "InvalidAccessKeyId.NotFound":
                return "短信服务认证失败";
            case "Forbidden.RAM":
                return "短信服务权限不足";
            case "isv.TEMPLATE_ILLEGAL":
                return "短信模板不合法或未审核通过";
            case "isv.SIGN_ILLEGAL":
                return "短信签名不合法或未审核通过";
            case "isv.EXTEND_CODE_ERROR":
                return "扩展码使用错误";
            case "isv.DOMESTIC_NUMBER_NOT_SUPPORTED":
                return "国内短信不支持该号码";
            case "isv.DENY_IP_RANGE":
                return "源IP地址不在白名单范围内";
            case "isv.DAY_LIMIT_CONTROL":
                return "触发日发送限额";
            case "isv.SMS_CONTENT_ILLEGAL":
                return "短信内容包含禁止发送内容";
            case "isv.SMS_SIGN_ILLEGAL":
                return "短信签名禁止使用";
            case "PORT_NOT_REGISTERED":
                return "手机号码无效或已停机，请检查号码状态";
            case "UNDELIV":
                return "短信发送失败，请稍后重试或联系运营商";
            case "UNKNOWN":
                return "短信状态未知，请稍后重试";
            default:
                return "发送失败：" + (message != null ? message : "未知错误");
        }
    }
}
