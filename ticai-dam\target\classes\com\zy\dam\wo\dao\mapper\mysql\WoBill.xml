<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.wo.dao.WoBillDAO">

    <sql id="meta">
			a.ID_
			,a.NO_
			,a.TYPE_
			,a.LOCATION_ID_
			,a.LOCATION_NAME_
			,a.LOCATION_ADDRESS_
			,a.REGISTER_TIME_
			,a.CONTACT_
			,a.PHONE_
			,a.PHONE_RES_
			,a.FAULT_REPORT_
			,a.FAULT_CHECK_
			,a.FEE_FLAG_
			,a.MAT_COST_
			,a.MAI_COST_
			,a.AMOUNT_
			,a.REPORT_TIME_
			,a.ARRIVE_TIME_
			,a.LEAVE_TIME_
			,a.SOLVE_TIME_
			,a.TAKE_MINUTE_
			,a.MAINTAIN_
			,a.USER_
			,a.CONFIRM_MOBILE_
			,a.CONFIRM_CODE_
			,a.CONFIRM_TIME_
			,a.CONFIRM_USER_
			,a.ASSIGN_TIME_
			,a.SERVICE_POINT_
			,a.SERVICE_MEMO_
			,a.SERVICE_USER_
			,a.STATUS_
			,a.FLAG_
			,a.CTIME_
			,a.CUSER_
			,a.MTIME_
			,a.MUSER_
			,DEPT_
	</sql>
    <insert id="insert" parameterType="com.zy.dam.wo.orm.WoBill">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into
        WO_BILL(ID_,NO_,TYPE_,LOCATION_ID_,LOCATION_NAME_,LOCATION_ADDRESS_,REGISTER_TIME_,CONTACT_,PHONE_,PHONE_RES_,FAULT_REPORT_,FAULT_CHECK_,FEE_FLAG_,MAT_COST_,MAI_COST_,AMOUNT_,REPORT_TIME_,ARRIVE_TIME_,LEAVE_TIME_,SOLVE_TIME_,TAKE_MINUTE_,MAINTAIN_,USER_,CONFIRM_MOBILE_,CONFIRM_CODE_,CONFIRM_TIME_,CONFIRM_USER_,ASSIGN_TIME_,SERVICE_POINT_,SERVICE_MEMO_,SERVICE_USER_,STATUS_,FLAG_,CTIME_,CUSER_,DEPT_)
        values(#{id},#{no},#{type},#{locationId},#{locationName},#{locationAddress},#{registerTime},#{contact},#{phone},#{phoneRes},#{faultReport},#{faultCheck},#{feeFlag},#{matCost},#{maiCost},#{amount},#{reportTime},#{arriveTime},#{leaveTime},#{solveTime},#{takeMinute},#{maintain},#{user},#{confirmMobile},#{confirmCode},#{confirmTime},#{confirmUser},#{assignTime},#{servicePoint},#{serviceMemo},#{serviceUser},'1','1',now(),#{cuser},#{dept})
    </insert>

    <update id="solve" parameterType="com.zy.dam.wo.orm.WoBill">
		update WO_BILL
		set STATUS_='2',TYPE_=#{type},LOCATION_ADDRESS_=#{locationAddress},CONTACT_=#{contact},PHONE_=#{phone},PHONE_RES_=#{phoneRes},FAULT_CHECK_=#{faultCheck},FEE_FLAG_=#{feeFlag},MAT_COST_=#{matCost},MAI_COST_=#{maiCost},AMOUNT_=#{amount},ARRIVE_TIME_=#{arriveTime},LEAVE_TIME_=#{leaveTime},SOLVE_TIME_=#{solveTime},TAKE_MINUTE_=#{takeMinute},USER_=#{user},CONFIRM_MOBILE_=#{confirmMobile},CONFIRM_CODE_=#{confirmCode},CONFIRM_TIME_=#{confirmTime},CONFIRM_USER_=#{confirmUser},ASSIGN_TIME_=#{assignTime},SERVICE_POINT_=#{servicePoint},SERVICE_MEMO_=#{serviceMemo},SERVICE_USER_=#{serviceUser},MTIME_=now(),MUSER_=#{muser},DEPT_=#{dept}
		where ID_=#{id}
	</update>

    <update id="close">
		update WO_BILL
		set STATUS_='3',CONFIRM_MOBILE_=#{confirmMobile},CONFIRM_CODE_=#{confirmCode},CONFIRM_TIME_=now(),CONFIRM_USER_=#{confirmUser},SERVICE_POINT_=#{servicePoint},SERVICE_MEMO_=#{serviceMemo},SERVICE_USER_=#{serviceUser}
		where ID_=#{id}
	</update>

    <select id="page" resultType="com.zy.dam.wo.vo.WoBillVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.MAINTAIN_) maintain_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
        ,(select n.NAME_ from SYS_USER n where n.FLAG_='1' and n.ID_=a.USER_)
        service_staff
        from WO_BILL a
        where a.FLAG_!='9'
        <if test="locationName != null">and a.LOCATION_NAME_ like concat('%',#{locationName},'%')</if>
        <if test="contact != null">and a.CONTACT_=#{contact}</if>
        <if test="no != null">and a.NO_ like concat('%',#{no},'%')</if>
        <if test="reportBegin != null">and a.REPORT_TIME_&gt;=#{reportBegin}</if>
        <if test="reportEnd != null">and a.REPORT_TIME_&lt; date_add(#{reportEnd}, interval 1 day)</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="maintain != null">and a.MAINTAIN_=#{maintain}</if>
        <if test="spec != null">and a.ID_ in (select BILL_ from WO_BILL_DETAIL where SPEC_=#{spec})</if>
        <if test="dept != null">and a.DEPT_=#{dept}</if>
        <if test="serviceStaff != null">and a.USER_ in (select ID_ from SYS_USER where FLAG_='1' and
            NAME_=#{serviceStaff})
        </if>
        order by a.CTIME_ desc
    </select>

    <select id="findOne" resultType="com.zy.dam.wo.orm.WoBill">
        select
        <include refid="meta"/>
        from WO_BILL a where a.ID_=#{0}
    </select>

    <update id="delete">
		update WO_BILL set FLAG_='9' where ID_=#{0}
	</update>

    <update id="deletes">
        update WO_BILL set FLAG_='9'
        <foreach collection="ids" item="id" open="where ID_ in (" separator="," close=")">#{id}</foreach>
    </update>

    <select id="findVo" resultType="com.zy.dam.wo.vo.WoBillVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.MAINTAIN_) maintain_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
        from WO_BILL a where a.ID_=#{0}
    </select>

    <insert id="insertDetail">
        insert into WO_BILL_DETAIL(ID_,BILL_,ASSET_,NAME_,SPEC_,SN_,FAULT_,FLAG_,SOLVE_,SEQ_) values(uuid(),#{bill},#{asset},#{name},#{spec},#{sn},#{fault},#{flag},#{solve},#{seq})
    </insert>

    <update id="updateDetail">
		update WO_BILL_DETAIL set FAULT_=#{fault},FLAG_=#{flag},SOLVE_=#{solve} where ID_=#{id}
	</update>

    <delete id="deleteDetailByBill">
		delete from WO_BILL_DETAIL where BILL_=#{0}
	</delete>

    <select id="findDetailByBill" resultType="com.zy.dam.wo.vo.WoBillDetailVo">
		select ID_,BILL_,ASSET_,NAME_,SPEC_,SN_,FAULT_,FLAG_,SOLVE_,SEQ_ from WO_BILL_DETAIL where BILL_=#{0} order by SEQ_
	</select>

    <select id="findExportByIds" resultType="com.zy.dam.wo.vo.WoBillExportVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.MAINTAIN_) maintain_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
        ,ELT(STATUS_,'待解决','已完成','已关闭') as status
        ,(select n.NAME_ from SYS_USER n where n.FLAG_='1' and n.ID_=a.USER_)
        service_staff
        from WO_BILL a
        where a.FLAG_!='9'
        <foreach collection="ids" item="id" open="and a.ID_ in (" separator="," close=")">#{id}</foreach>
    </select>

    <select id="findExportByQuery" resultType="com.zy.dam.wo.vo.WoBillExportVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.MAINTAIN_) maintain_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
        ,ELT(STATUS_,'待解决','已完成','已关闭') as status
        ,(select n.NAME_ from SYS_USER n where n.FLAG_='1' and n.ID_=a.USER_)
        service_staff
        from WO_BILL a
        where a.FLAG_!='9'
        <if test="locationName != null">and a.LOCATION_NAME_ like concat('%',#{locationName},'%')</if>
        <if test="contact != null">and a.CONTACT_=#{contact}</if>
        <if test="no != null">and a.NO_ like concat('%',#{no},'%')</if>
        <if test="reportBegin != null">and a.REPORT_TIME_&gt;=#{reportBegin}</if>
        <if test="reportEnd != null">and a.REPORT_TIME_&lt; date_add(#{reportEnd}, interval 1 day)</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="maintain != null">and a.MAINTAIN_=#{maintain}</if>
        <if test="spec != null">and a.ID_ in (select BILL_ from WO_BILL_DETAIL where SPEC_=#{spec})</if>
        <if test="dept != null">and a.DEPT_=#{dept}</if>
        <if test="serviceStaff != null">and a.USER_ in (select ID_ from SYS_USER where FLAG_='1' and
            NAME_=#{serviceStaff})
        </if>
        order by a.CTIME_ desc
    </select>

</mapper>
