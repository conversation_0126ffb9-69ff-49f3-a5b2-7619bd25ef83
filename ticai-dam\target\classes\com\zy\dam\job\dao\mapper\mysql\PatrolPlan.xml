<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.job.dao.PatrolPlanDAO">

    <select id="findNoneNo" resultType="com.zy.dam.job.vo.PatrolPlanTaskNo">
        select ID_,START_TIME_ from AM_PATROL_TASK
        where NO_ is null and START_TIME_ &lt; date_add(now(), interval 7 day)
    </select>
    <update id="updateNo">
        update AM_PATROL_TASK set NO_=#{1} where ID_=#{0} and NO_ is null
    </update>
</mapper>
