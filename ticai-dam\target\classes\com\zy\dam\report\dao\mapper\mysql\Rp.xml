<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.report.dao.RpDAO">

    <select id="countAssetDept" resultType="com.zy.model.NameInt">
        select a.NAME_,b.value_ from SYS_DEPT a, (
        select DEPT_,count(0) value_ from am_asset where FLAG_='1' and type_=#{0} group by DEPT_) b
        where a.ID_=b.DEPT_
        order by b.value_ desc
    </select>

    <select id="countAssetTypeRoot" resultType="com.zy.model.NameInt">
        select a.NAME_,b.value_ from AM_ASSET_TYPE a, (
        select b.PCODE_,count(0) value_
        from AM_ASSET a,AM_ASSET_TYPE b
        where a.FLAG_='1' and a.TYPE_=b.CODE_ group by b.PCODE_) b
        where a.CODE_=b.PCODE_
        order by b.value_ desc
    </select>

    <select id="countAssetType" resultType="com.zy.model.NameInt">
        select b.NAME_,count(0) value_
        from AM_ASSET a,AM_ASSET_TYPE b
        where a.FLAG_='1' and a.TYPE_=b.CODE_ and b.PCODE_=#{0} group by b.NAME_
        order by value_ desc
    </select>

    <select id="countAssetRegion" resultType="com.zy.model.NameInt">
        select a.NAME_, value_
        from AM_REGION a, (select REGION_,count(0) value_ from AM_ASSET where FLAG_='1' and TYPE_=#{0} and REGION_ is not null group by REGION_) b
        where b.REGION_=a.CODE_ and a.REGION_ !='460000'
        order by a.CODE_ desc
    </select>

    <!-- 公共筛选条件片段 -->
    <sql id="commonAssetFilters">
        <if test="keyword != null">
            and (c.ACCOUNT_ like concat('%',#{keyword},'%')
            or c.NAME_ like concat('%',#{keyword},'%')
            or COALESCE(a.NOW_SN_, d.SN_) like concat('%',#{keyword},'%')
            or d.NO_ like concat('%',#{keyword},'%')
            or d.NAME_ like concat('%',#{keyword},'%')
            or d.SPEC_ like concat('%',#{keyword},'%')
            )
        </if>
        <if test="locationName != null">and g.NAME_=#{locationName}</if>
        <if test="locationAddress != null">and g.ADDRESS_=#{locationAddress}</if>
        <if test="locationRegion != null">and region.ID_=#{locationRegion}</if>
        <if test="locationMemo != null">and g.MEMO_ like concat('%',#{locationMemo},'%')</if>
        <if test="dept != null">and c.DEPT_=#{dept}</if>
        <if test="timeBegin != null">AND g.CREATE_TIME_ >= #{timeBegin}</if>
        <if test="timeEnd != null">AND g.CREATE_TIME_ &lt;= date_add(#{timeEnd}, interval 1 day)</if>
    </sql>

    <!-- 已领用未绑定资产筛选条件片段 -->
    <sql id="commonUnboundAssetFilters">
        <if test="keyword != null">
            and (use_user.ACCOUNT_ like concat('%',#{keyword},'%')
            or use_user.NAME_ like concat('%',#{keyword},'%')
            or d.SN_ like concat('%',#{keyword},'%')
            or d.NO_ like concat('%',#{keyword},'%')
            or d.NAME_ like concat('%',#{keyword},'%')
            or d.SPEC_ like concat('%',#{keyword},'%')
            )
        </if>
        <if test="locationName != null">and g.NAME_=#{locationName}</if>
        <if test="locationAddress != null">and g.ADDRESS_=#{locationAddress}</if>
        <if test="locationRegion != null">and region.ID_=#{locationRegion}</if>
        <if test="locationMemo != null">and g.MEMO_ like concat('%',#{locationMemo},'%')</if>
        <if test="dept != null">and use_user.DEPT_=#{dept}</if>
        <if test="timeBegin != null">AND g.CREATE_TIME_ >= #{timeBegin}</if>
        <if test="timeEnd != null">AND g.CREATE_TIME_ &lt;= date_add(#{timeEnd}, interval 1 day)</if>
    </sql>

    <!-- 网点筛选条件片段 -->
    <sql id="commonLocationFilters">
        <if test="keyword != null">
            AND (g.NAME_ LIKE CONCAT('%', #{keyword}, '%')
            OR g.ADDRESS_ LIKE CONCAT('%', #{keyword}, '%')
            OR g.CONTACT_ LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="locationName != null">AND g.NAME_ LIKE concat('%', #{locationName}, '%')</if>
        <if test="locationAddress != null">AND g.ADDRESS_ LIKE concat('%',#{locationAddress},'%') </if>
        <if test="locationRegion != null">AND region.ID_=#{locationRegion}</if>
        <if test="locationMemo != null">AND g.MEMO_ LIKE concat('%',#{locationMemo},'%')</if>
        <if test="timeBegin != null">AND g.CREATE_TIME_ >= #{timeBegin}</if>
        <if test="timeEnd != null">AND g.CREATE_TIME_ &lt;= date_add(#{timeEnd}, interval 1 day)</if>
    </sql>

    <!-- 数据类型筛选条件片段 -->
    <sql id="dataTypeFilter">
        <if test="dataType != null">and combined_results.dataType = #{dataType}</if>
        <if test="dataType == null and dept != null">and combined_results.dataType != 3</if>
    </sql>

    <!-- 已领用已绑定资产查询片段 -->
    <sql id="assetBoundDataQuery">
        SELECT
            d.ID_ AS id,
            a.TIME_ AS bindTime,
            g.CREATE_TIME_ AS time,
            c.ACCOUNT_ AS account,
            c.NAME_ AS name,
            c.GENDER_ AS gender,
            c.STATUS_ AS status,
            dept.NAME_ AS deptName,
            a.NOW_SN_ AS nowSn,
            asset_type.NAME_ AS typeName,
            d.NO_ AS no,
            d.NAME_ AS assetName,
            d.SPEC_ AS spec,
            g.NAME_ AS locationName,
            g.ADDRESS_ AS locationAddress,
            region.NAME_ AS locationRegion,
            g.MEMO_ AS locationMemo,
            CASE WHEN d.LNG_ IS NOT NULL AND d.LAT_ IS NOT NULL THEN '是' ELSE '否' END AS whetherLocation,
            d.LOC_ADDR_ AS locAddr,
            1 AS dataType
        FROM AM_ASSET_SN a
                 JOIN AM_ASSET d ON a.ASSET_ = d.ID_ AND d.FLAG_ = '1'
                 LEFT JOIN SYS_USER c ON d.USE_USER_ = c.ID_ AND c.FLAG_ = '1'
                 LEFT JOIN SYS_DEPT dept ON dept.ID_ = c.DEPT_
                 LEFT JOIN AM_ASSET_TYPE asset_type ON asset_type.FLAG_ = '1' AND asset_type.CODE_ = d.TYPE_
                 LEFT JOIN AM_LOCATION g ON g.ID_ = d.LOCATION_ AND g.FLAG_ = '1'
                 LEFT JOIN AM_REGION region ON region.ID_ = g.REGION_ AND region.FLAG_ = '1'
        WHERE a.FLAG_ != '9'
          AND NOT EXISTS (
            SELECT 1
            FROM AM_ASSET_SN a2
            WHERE a2.ASSET_ = a.ASSET_
              AND a2.FLAG_ != '9'
              AND (
                a2.TIME_ > a.TIME_
                    OR (a2.TIME_ = a.TIME_ AND a2.ID_ > a.ID_)
                )
        )
    </sql>

    <!-- 已领用未绑定资产查询片段 -->
    <sql id="assetUnboundDataQuery">
        SELECT
            d.ID_ AS id,
            NULL AS bindTime,
            g.CREATE_TIME_ AS time,
            use_user.ACCOUNT_ AS account,
            use_user.NAME_ AS name,
            use_user.GENDER_ AS gender,
            use_user.STATUS_ AS status,
            use_dept.NAME_ AS deptName,
            d.SN_ AS nowSn,
            asset_type.NAME_ AS typeName,
            d.NO_ AS no,
            d.NAME_ AS assetName,
            d.SPEC_ AS spec,
            g.NAME_ AS locationName,
            g.ADDRESS_ AS locationAddress,
            region.NAME_ AS locationRegion,
            g.MEMO_ AS locationMemo,
            CASE WHEN d.LNG_ IS NOT NULL AND d.LAT_ IS NOT NULL THEN '是' ELSE '否' END AS whetherLocation,
            d.LOC_ADDR_ AS locAddr,
            2 AS dataType
        FROM AM_ASSET d
                 LEFT JOIN SYS_USER use_user ON d.USE_USER_ = use_user.ID_ AND use_user.FLAG_ = '1'
                 LEFT JOIN SYS_DEPT use_dept ON use_dept.ID_ = use_user.DEPT_
                 LEFT JOIN AM_ASSET_TYPE asset_type ON asset_type.FLAG_ = '1' AND asset_type.CODE_ = d.TYPE_
                 LEFT JOIN AM_LOCATION g ON d.LOCATION_ = g.ID_ AND g.FLAG_ = '1'
                 LEFT JOIN AM_REGION region ON region.ID_ = g.REGION_ AND region.FLAG_ = '1'
        WHERE d.FLAG_ = '1' AND d.STATUS_ = '2'
          AND NOT EXISTS (
            SELECT 1 FROM AM_ASSET_SN asn
            WHERE asn.ASSET_ = d.ID_ AND asn.FLAG_ != '9'
        )
    </sql>

    <!-- 已领用已绑定资产查询片段 - 导出版本 -->
    <sql id="assetBoundDataQueryExport">
        SELECT
            d.ID_ AS id,
            a.TIME_ AS bindTime,
            g.CREATE_TIME_ AS time,
            c.ACCOUNT_ AS account,
            c.NAME_ AS name,
            ELT(c.GENDER_,'男','女') AS gender,
            ELT(c.STATUS_,'在职','离职') AS status,
            dept.NAME_ AS deptName,
            a.NOW_SN_ AS nowSn,
            asset_type.NAME_ AS typeName,
            d.NO_ AS no,
            d.NAME_ AS assetName,
            d.SPEC_ AS spec,
            g.NAME_ AS locationName,
            g.ADDRESS_ AS locationAddress,
            region.NAME_ AS locationRegion,
            g.MEMO_ AS locationMemo,
            CASE WHEN d.LNG_ IS NOT NULL AND d.LAT_ IS NOT NULL THEN '是' ELSE '否' END AS whetherLocation,
            d.LOC_ADDR_ AS locAddr,
            1 AS dataType
        FROM AM_ASSET_SN a
                 JOIN AM_ASSET d ON a.ASSET_ = d.ID_ AND d.FLAG_ = '1'
                 LEFT JOIN SYS_USER c ON d.USE_USER_ = c.ID_ AND c.FLAG_ = '1'
                 LEFT JOIN SYS_DEPT dept ON dept.ID_ = c.DEPT_
                 LEFT JOIN AM_ASSET_TYPE asset_type ON asset_type.FLAG_ = '1' AND asset_type.CODE_ = d.TYPE_
                 LEFT JOIN AM_LOCATION g ON g.ID_ = d.LOCATION_ AND g.FLAG_ = '1'
                 LEFT JOIN AM_REGION region ON region.ID_ = g.REGION_ AND region.FLAG_ = '1'
        WHERE a.FLAG_ != '9'
          AND NOT EXISTS (
            SELECT 1
            FROM AM_ASSET_SN a2
            WHERE a2.ASSET_ = a.ASSET_
              AND a2.FLAG_ != '9'
              AND (
                a2.TIME_ > a.TIME_
                    OR (a2.TIME_ = a.TIME_ AND a2.ID_ > a.ID_)
                )
        )
    </sql>

    <!-- 已领用未绑定资产查询片段 - 导出版本 -->
    <sql id="assetUnboundDataQueryExport">
        SELECT
            d.ID_ AS id,
            NULL AS bindTime,
            g.CREATE_TIME_ AS time,
            use_user.ACCOUNT_ AS account,
            use_user.NAME_ AS name,
            ELT(use_user.GENDER_,'男','女') AS gender,
            ELT(use_user.STATUS_,'在职','离职') AS status,
            use_dept.NAME_ AS deptName,
            d.SN_ AS nowSn,
            asset_type.NAME_ AS typeName,
            d.NO_ AS no,
            d.NAME_ AS assetName,
            d.SPEC_ AS spec,
            g.NAME_ AS locationName,
            g.ADDRESS_ AS locationAddress,
            region.NAME_ AS locationRegion,
            g.MEMO_ AS locationMemo,
            CASE WHEN d.LNG_ IS NOT NULL AND d.LAT_ IS NOT NULL THEN '是' ELSE '否' END AS whetherLocation,
            d.LOC_ADDR_ AS locAddr,
            2 AS dataType
        FROM AM_ASSET d
                 LEFT JOIN SYS_USER use_user ON d.USE_USER_ = use_user.ID_ AND use_user.FLAG_ = '1'
                 LEFT JOIN SYS_DEPT use_dept ON use_dept.ID_ = use_user.DEPT_
                 LEFT JOIN AM_ASSET_TYPE asset_type ON asset_type.FLAG_ = '1' AND asset_type.CODE_ = d.TYPE_
                 LEFT JOIN AM_LOCATION g ON d.LOCATION_ = g.ID_ AND g.FLAG_ = '1'
                 LEFT JOIN AM_REGION region ON region.ID_ = g.REGION_ AND region.FLAG_ = '1'
        WHERE d.FLAG_ = '1'
          AND d.STATUS_ = '2'
          AND NOT EXISTS (
            SELECT 1 FROM AM_ASSET_SN asn
            WHERE asn.ASSET_ = d.ID_ AND asn.FLAG_ != '9'
        )
    </sql>

    <!-- 网点数据查询片段 -->
    <sql id="locationDataQuery">
        SELECT
            g.ID_ AS id,
            NULL AS bindTime,
            g.CREATE_TIME_ AS time,
            NULL AS account,
            NULL AS name,
            NULL AS gender,
            NULL AS status,
            NULL AS deptName,
            NULL AS nowSn,
            NULL AS typeName,
            NULL AS no,
            NULL AS assetName,
            NULL AS spec,
            g.NAME_ AS locationName,
            g.ADDRESS_ AS locationAddress,
            region.NAME_ AS locationRegion,
            g.MEMO_ AS locationMemo,
            NULL AS whetherLocation,
            NULL AS locAddr,
            3 AS dataType
        FROM AM_LOCATION g
                 LEFT JOIN AM_REGION region
                           ON region.ID_ = g.REGION_
                               AND region.FLAG_ = '1'
        WHERE g.FLAG_ = '1'
          AND NOT EXISTS (
            SELECT 1 FROM AM_ASSET d
            WHERE d.LOCATION_ = g.ID_ AND d.FLAG_ = '1'
        )
    </sql>

    <!-- 资产终端机绑定统计 -->
    <select id="getAssetSnPage" resultType="com.zy.dam.report.vo.AssetSnVo">
        SELECT * FROM (
        <!-- 已领用已绑定数据 -->
        <include refid="assetBoundDataQuery"/>
        <include refid="commonAssetFilters"/>

        UNION ALL

        <!-- 已领用未绑定数据 -->
        <include refid="assetUnboundDataQuery"/>
        <include refid="commonUnboundAssetFilters"/>

        UNION ALL

        <!-- 没有绑定设备的网点（网点未匹配任何设备） -->
        <include refid="locationDataQuery"/>
        <include refid="commonLocationFilters"/>
        ) AS combined_results
        WHERE 1=1
        <include refid="dataTypeFilter"/>
        ORDER BY dataType, COALESCE(bindTime, time) DESC, id
    </select>

    <select id="findExportByIds" resultType="com.zy.dam.report.vo.AssetSnExportVo">
        SELECT * FROM (
        <!-- 已领用已绑定数据 -->
        <include refid="assetBoundDataQueryExport"/>

        UNION ALL

        <!-- 已领用未绑定数据 -->
        <include refid="assetUnboundDataQueryExport"/>

        UNION ALL

        <!-- 没有绑定设备的网点（网点未匹配任何设备） -->
        <include refid="locationDataQuery"/>
        ) AS combined_results
        WHERE combined_results.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
        ORDER BY dataType, COALESCE(bindTime, time) DESC, id
    </select>

    <select id="findExportByQuery" resultType="com.zy.dam.report.vo.AssetSnExportVo">
        SELECT * FROM (
        <!-- 已领用已绑定数据 -->
        <include refid="assetBoundDataQueryExport"/>
        <include refid="commonAssetFilters"/>

        UNION ALL

        <!-- 已领用未绑定数据 -->
        <include refid="assetUnboundDataQueryExport"/>
        <include refid="commonUnboundAssetFilters"/>

        UNION ALL

        <!-- 没有绑定设备的网点（网点未匹配任何设备） -->
        <include refid="locationDataQuery"/>
        <include refid="commonLocationFilters"/>
        ) AS combined_results
        WHERE 1=1
        <include refid="dataTypeFilter"/>
        ORDER BY dataType, COALESCE(bindTime, time) DESC, id
    </select>

    <update id="deleteBatch">
        update AM_ASSET_SN set FLAG_='9' where ID_ in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
    </update>

    <sql id="summary_meta">
        a.ID_,a.PROP_,a.TYPE_,a.NO_,a.NAME_,a.SN_,a.BC1_,a.IMP_,a.CORP_,a.DEPT_,a.REGION_,a.LOCATION_,a.MA_DATE_,date_format(a.CTIME_,'%Y-%m-%d') in_date
        ,a.GU_DATE_,a.EX_DATE_,a.BO_DATE_,a.BO_AMOUNT_,a.EX_DAY_,a.EX_NAME_,a.USE_DEPT_,a.USE_USER_,a.STATUS_,a.LEND_STATUS_,a.SPEC_,a.LNG_,a.LAT_,CAST(a.LOC_TIME_ AS CHAR) loc_time
        ,b.NAME_ use_user_name,c.NAME_ location_name,c.CONTACT_ location_contact, c.PHONE_ location_phone, c.ADDRESS_ location_address,a.LOC_ADDR_
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=a.TYPE_) type_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.USE_DEPT_) use_dept_name
    </sql>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select
        <include refid="summary_meta"/>
        from AM_ASSET a left join SYS_USER b on a.USE_USER_=b.ID_ left join AM_LOCATION c on a.LOCATION_=c.ID_
        where a.FLAG_='1' and a.LNG_ and a.LAT_ IS NOT NULL and a.STATUS_='2'
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.NAME_ like
            concat('%',#{keyword},'%'))
        </if>
        <if test="ignoreId != null">and a.ID_!=#{ignoreId}</if>
        <if test="type != null">and a.TYPE_=#{type}</if>
        <if test="typeLike != null">and a.TYPE_ like concat(#{typeLike},'%')</if>
        <if test="region != null">and a.REGION_=#{region}</if>
        <if test="location != null">and a.LOCATION_=#{location}</if>
        <if test="dept != null">and a.DEPT_=#{dept}</if>
        <if test="useDept != null">and a.USE_DEPT_=#{useDept}</if>
        <if test="sn != null">and a.SN_ like concat('%', #{sn}, '%')</if>
        <if test="userName != null">and (b.NAME_ like concat('%', #{userName}, '%') or b.NO_ like
            concat('%',#{userName},'%') or b.ACCOUNT_ like concat('%', #{userName}, '%'))
        </if>
        <if test="imp != null">and a.IMP_=#{imp}</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="statusList != null">
            <foreach collection="statusList" item="statusItem" open="and a.STATUS_ in (" separator="," close=")">
                #{statusItem}
            </foreach>
        </if>
        <if test="exBegin != null">and a.EX_DATE_ &gt;= #{exBegin}</if>
        <if test="exEnd != null">and a.EX_DATE_ &lt; date_sub(#{exBegin}, interval 1 day)</if>
        <if test="exDateIndex != 0">
            <choose>
                <when test="exDateIndex = 1">and a.EX_DATE_ &gt;= curdate() and a.EX_DATE_ &lt; date_add(curdate(),
                    interval 91 day)
                </when>
                <when test="exDateIndex = 2">and a.EX_DATE_ &gt;= curdate() and a.EX_DATE_ &lt; date_add(curdate(),
                    interval 31 day)
                </when>
                <when test="exDateIndex = 8">and a.EX_DATE_ &gt;= curdate() and a.EX_DATE_ &lt; date_add(curdate(),
                    interval 8 day)
                </when>
                <when test="exDateIndex = 9">and a.EX_DATE_ &lt; curdate()</when>
            </choose>
        </if>
        <if test="regionLocation != null">and a.LOCATION_ like concat(#{regionLocation},'%')</if>
        <if test="spec != null">and a.SPEC_ like concat(#{spec},'%')</if>
        <if test="name != null">and c.NAME_ like concat(#{name},'%')</if>
        <if test="mold != null">and a.TYPE_=#{mold}</if>
        <if test="begin != null">and a.CTIME_ &gt;= #{begin}</if>
        <if test="end != null">and a.CTIME_ &lt; date_add(#{end}, interval 1 day)</if>
        <if test="locAddr != null">and a.LOC_ADDR_=#{locAddr}</if>
        order by a.LOC_TIME_ desc
    </select>

</mapper>
