package com.zy.controller;

import com.zy.core.EC;
import com.zy.model.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.RequestDispatcher;
import javax.servlet.http.HttpServletRequest;

/**
 * 自定义错误控制器
 * 用于处理所有未被捕获的错误，避免暴露服务器信息
 */
@RestController
public class CustomErrorController implements ErrorController {

    private static final Logger log = LoggerFactory.getLogger(CustomErrorController.class);

    @RequestMapping("/error")
    public Result handleError(HttpServletRequest request) {
        Object status = request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE);

        if (status != null) {
            Integer statusCode = Integer.valueOf(status.toString());

            // 记录错误信息到日志
            String errorMessage = (String) request.getAttribute(RequestDispatcher.ERROR_MESSAGE);
            String requestUri = (String) request.getAttribute(RequestDispatcher.ERROR_REQUEST_URI);
            log.error("错误状态码: {}, 请求URI: {}, 错误信息: {}", statusCode, requestUri, errorMessage);

            if (statusCode == HttpStatus.NOT_FOUND.value()) {
                return new Result(EC.NOT_FOUND, "请求的资源不存在");
            } else if (statusCode == HttpStatus.INTERNAL_SERVER_ERROR.value()) {
                return new Result(EC.ERR_SERVER, "系统内部错误，请联系管理员");
            } else if (statusCode == HttpStatus.FORBIDDEN.value()) {
                return new Result(EC.ERR_SERVER, "访问被拒绝");
            } else if (statusCode == HttpStatus.METHOD_NOT_ALLOWED.value()) {
                return new Result(EC.ARG_NOT_VALID, "请求方法不被允许");
            }
        }

        // 默认错误响应
        return new Result(EC.ERR_SERVER, "系统错误，请联系管理员");
    }
}
