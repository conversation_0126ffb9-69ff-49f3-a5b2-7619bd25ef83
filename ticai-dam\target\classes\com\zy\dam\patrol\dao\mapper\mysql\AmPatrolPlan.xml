<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.patrol.dao.AmPatrolPlanDAO">

    <sql id="meta">
			a.ID_
			,a.NO_
			,a.NAME_
			,a.TYPE_
			,a.GPS_
			,a.SCOPE_
			,a.ORD_FLAG_
			,a.USER_
			,a.TIME_MODE_
			,a.START_DATE_
			,a.END_DATE_
			,a.START_TIME_
			,a.END_TIME_
			,a.MEMO_
			,a.STATUS_
			,a.PERIOD_STATUS_
			,a.CTIME_
			,a.CUSER_
			,a.FLAG_
	</sql>
    <resultMap id="VO" type="com.zy.dam.patrol.vo.PatrolPlanVo">
        <result column="a.PERIOD_DATA_" jdbcType="BLOB" javaType="String"/>
    </resultMap>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.patrol.orm.AmPatrolPlan">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into
        AM_PATROL_PLAN(ID_,NO_,NAME_,TYPE_,GPS_,SCOPE_,ORD_FLAG_,USER_,TIME_MODE_,START_DATE_,END_DATE_,START_TIME_,END_TIME_,PERIOD_DATA_,MEMO_,STATUS_,PERIOD_STATUS_,CTIME_,CUSER_,FLAG_)
        values(#{id},#{no},#{name},#{type},#{gps},#{scope},#{ordFlag},#{user},#{timeMode},#{startDate},#{endDate},#{startTime},#{endTime},#{periodData, jdbcType=BLOB},#{memo},#{status},#{periodStatus},now(),#{cuser},'1')
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.dam.patrol.orm.AmPatrolPlan">
		update AM_PATROL_PLAN
		set NAME_=#{name},TYPE_=#{type},GPS_=#{gps},SCOPE_=#{scope},ORD_FLAG_=#{ordFlag},USER_=#{user},TIME_MODE_=#{timeMode},START_DATE_=#{startDate},END_DATE_=#{endDate},START_TIME_=#{startTime},END_TIME_=#{endTime},PERIOD_DATA_=#{periodData, jdbcType=BLOB},MEMO_=#{memo},
		STATUS_=(case STATUS_ when '0' then #{status} else STATUS_ end)
		where ID_=#{id}
	</update>

    <update id="updateStatus">
		update AM_PATROL_PLAN set STATUS_=#{1} where ID_=#{0}
	</update>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.patrol.vo.PatrolPlanVo">
        select
        <include refid="meta"/>
        ,b.NAME_ user_name
        from AM_PATROL_PLAN a left join SYS_USER b on a.USER_=b.ID_
        where a.FLAG_!='9'
        <if test="keyword != null">and (a.NAME_ like concat('%',#{keyword},'%') or a.NO_ like concat('%',#{keyword},'%')
            )
        </if>
        <if test="periodStatus != null">and a.PERIOD_STATUS_=#{periodStatus}</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="userName != null">and (b.NAME_ like concat('%',#{userName},'%') or b.NO_ like
            concat('%',#{userName},'%') )
        </if>
        <if test="begin != null">and a.START_DATE_ &gt;=#{begin}</if>
        <if test="end != null">and a.END_DATE_ &lt; date_add(#{end}, interval 1 day)</if>
        order by a.NO_ desc
    </select>

    <!-- 获取唯一的资管-巡检计划数据 -->
    <select id="findOne" resultType="com.zy.dam.patrol.orm.AmPatrolPlan">
        select
        <include refid="meta"/>
        from AM_PATROL_PLAN a where a.ID_=#{0}
    </select>

    <!-- 删除 -->
    <update id="delete">
		update AM_PATROL_PLAN set FLAG_='9' where ID_=#{0}
	</update>

    <select id="findVo" resultType="com.zy.dam.patrol.vo.PatrolPlanVo">
        select
        <include refid="meta"/>
        ,a.PERIOD_DATA_
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        from AM_PATROL_PLAN a where a.ID_=#{0}
    </select>

    <insert id="insertPlanItem">
        insert into AM_PATROL_PLAN_ITEM(ID_,PLAN_,TYPE_,REF_,ORD_,FLAG_) values(uuid(), #{0}, #{1}, #{2}, #{3}, '1')
    </insert>

    <update id="deletePlanItem">
        update AM_PATROL_PLAN_ITEM set FLAG_='9' where PLAN_=#{0}
    </update>

    <select id="findPath" resultType="String">
        select REF_ from AM_PATROL_PLAN_ITEM where FLAG_='1' and TYPE_='1' and PLAN_=#{0} order by ORD_ limit 0, 1
    </select>

    <select id="findPoint" resultType="String">
        select REF_ from AM_PATROL_PLAN_ITEM where FLAG_='1' and TYPE_='2' and PLAN_=#{0} order by ORD_
    </select>

    <select id="findAssetByPlan" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select a.ID_,a.NO_,a.NAME_
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.TYPE_) type_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        from AM_ASSET a,AM_PATROL_PLAN_ITEM b
        where b.FLAG_='1' and b.TYPE_='3' and a.ID_=b.REF_ and b.PLAN_=#{0}
        order by b.ORD_
    </select>

    <select id="findExportByIds" resultType="com.zy.dam.patrol.vo.PatrolPlanExportVo">
        select
        <include refid="meta"/>
        ,b.NAME_ user_name,ELT(a.TYPE_,'路线','点位','资产') as type,ELT(a.TIME_MODE_,'周期','自定义') as
        timeMode, ELT(a.PERIOD_STATUS_,'未开始','已开始','已结束','异常') as periodStatus, ELT(a.STATUS_,'草稿','已发布','启用','停用') as
        status
        from AM_PATROL_PLAN a left join SYS_USER b on a.USER_=b.ID_
        where a.FLAG_!='9'
        <foreach collection="ids" item="id" open="and a.ID_ in (" separator="," close=")">#{id}</foreach>
    </select>

    <select id="findExportByQuery" resultType="com.zy.dam.patrol.vo.PatrolPlanExportVo">
        select
        <include refid="meta"/>
        ,b.NAME_ user_name,ELT(a.TYPE_,'路线','点位','资产') as type,ELT(a.TIME_MODE_,'周期','自定义') as
        timeMode, ELT(a.PERIOD_STATUS_,'未开始','已开始','已结束','异常') as periodStatus, ELT(a.STATUS_,'草稿','已发布','启用','停用') as
        status
        from AM_PATROL_PLAN a left join SYS_USER b on a.USER_=b.ID_
        where a.FLAG_!='9'
        <if test="keyword != null">and (a.NAME_ like concat('%',#{keyword},'%') or a.NO_ like concat('%',#{keyword},'%')
            )
        </if>
        <if test="periodStatus != null">and a.PERIOD_STATUS_=#{periodStatus}</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="userName != null">and (b.NAME_ like concat('%',#{userName},'%') or b.NO_ like
            concat('%',#{userName},'%') )
        </if>
        <if test="begin != null">and a.START_DATE_ &gt;=#{begin}</if>
        <if test="end != null">and a.END_DATE_ &lt; date_add(#{end}, interval 1 day)</if>
        order by a.NO_ desc
    </select>

</mapper>
