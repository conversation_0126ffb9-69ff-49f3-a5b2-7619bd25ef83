"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[8058],{23775:(a,e,n)=>{n.d(e,{g:()=>d});var t=n(59305),c=n(94116);const s=(0,t.Q_)({id:"onlineReport",state:()=>({curTab:"VulnSummary",curExpandVulnId:-1}),getters:{getCurTab(){return this.curTab},getCurExpandVulnId(){return this.curExpandVulnId}},actions:{setCurTab(a){this.curTab=a},setCurExpandVulnId(a){this.curExpandVulnId=a}}});function d(){return s(c.h)}},43178:(a,e,n)=>{n.d(e,{Z:()=>i});var t=n(8081),c=n.n(t),s=n(23645),d=n.n(s)()(c());d.push([a.id,".StatisticInfo[data-v-7644b69c] {\n  display: flex;\n  gap: 20px;\n  justify-content: space-around;\n}\n.StatisticInfo .card[data-v-7644b69c] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: calc(100% / 2);\n  height: 150px;\n  border-radius: 4px;\n  background: #fff;\n  box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.06);\n}\n.StatisticInfo .card .right[data-v-7644b69c] {\n  display: flex;\n  flex-direction: column;\n  margin: 0 16px;\n  gap: 8px;\n}\n.StatisticInfo .card .right .val[data-v-7644b69c] {\n  font-size: 32px;\n  font-weight: 500;\n  color: #2e2e2e;\n  line-height: 32px;\n}\n.StatisticInfo .card .right .title[data-v-7644b69c] {\n  font-size: 14px;\n  font-weight: 400;\n  color: #535353;\n  line-height: 22px;\n}\n.StatisticInfo .card img[data-v-7644b69c] {\n  width: 60px;\n}\n",""]);const i=d},97261:(a,e,n)=>{n.r(e),n.d(e,{default:()=>P});var t=n(66252),c=(n(57658),n(3577));const s=n.p+"assets/ipCount_080576bdddf4316765a6.svg",d=n.p+"assets/ipv6Count_ed197d379f72b453b52d.svg",i=n.p+"assets/assets_da216b46d959065e2029.svg",f=n.p+"assets/portCount_9638ec74dbb2f31933eb.svg",r=n.p+"assets/domainCount_c28317983c7d2acc1bcd.svg",o=n.p+"assets/linkIp_32f25b2cb16eaec11cd3.svg";var u=n(2262),b=n(23775),l=n(79414);const p={class:"StatisticInfo"},g={key:0,src:s},m={key:1,src:d},v={key:2,src:i},k={key:3,src:f},C={key:4,src:r},y={key:5,src:o},w={class:"right"},h=["onClick"],x={class:"title"},I=(0,t.aZ)({__name:"StatisticInfo",props:{paramsData:{},source:{}},setup(a){const e=a,{t:n}=l.i18n.global,s=(0,b.g)(),d={ipCount:{name:n("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f298009fd981c797a27c49bb7552da6106cb030b6fe2db94a02a92326e7252e50"),imgUrl:""},ipv6Count:{name:n("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f298009fd981c797a27c49bb7552da61034880f4f3d1b8b58a256075e8ecbc71b"),imgUrl:""},webCount:{name:n("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f298009fd981c797a27c49bb7552da6106e2fb216fb559af67e854fea60f9272fa292ca0575e6f535cff1a528cf85ca08"),imgUrl:""},webAssetsCount:{name:n("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f298009fd981c797a27c49bb7552da6106e2fb216fb559af67e854fea60f9272fa292ca0575e6f535cff1a528cf85ca08"),imgUrl:""},portCount:{name:n("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f298009fd981c797a27c49bb7552da610388dc267aed5aab421dead5bb008de4a3fade03091609553be3dda803e9af5fa"),imgUrl:""},domainCount:{name:n("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f298009fd981c797a27c49bb7552da61068465aba3f0938c45d979c9598d8f538"),imgUrl:""},linkIp:{name:n("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f298009fd981c797a27c49bb7552da61036b1a4f89d51ace1cb51bc5ef19fe2fa418b3fd8fc9572cb43d90e73fd03c8fc"),imgUrl:""}},i=(0,t.Fl)((()=>e.paramsData)),f=(0,t.Fl)((()=>{let a=[],e=(0,u.SU)(i);for(let n in e)null!==e[n]&&void 0!==e[n]&&a.push({key:n,value:e[n]});return a}));return(a,n)=>((0,t.wg)(),(0,t.iD)("div",p,[((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)(f.value,(a=>((0,t.wg)(),(0,t.iD)("div",{class:"card",key:a.key},["ipCount"===a.key?((0,t.wg)(),(0,t.iD)("img",g)):(0,t.kq)("v-if",!0),"ipv6Count"===a.key?((0,t.wg)(),(0,t.iD)("img",m)):(0,t.kq)("v-if",!0),"webCount"===a.key||"webAssetsCount"===a.key?((0,t.wg)(),(0,t.iD)("img",v)):(0,t.kq)("v-if",!0),"portCount"===a.key?((0,t.wg)(),(0,t.iD)("img",k)):(0,t.kq)("v-if",!0),"domainCount"===a.key?((0,t.wg)(),(0,t.iD)("img",C)):(0,t.kq)("v-if",!0),"linkIp"===a.key?((0,t.wg)(),(0,t.iD)("img",y)):(0,t.kq)("v-if",!0),(0,t._)("div",w,[(0,t._)("span",{class:"val",onClick:n=>{return t=a.key,void("online"===e.source&&["ipCount","ipv6Count","webCount"].includes(t)&&s.setCurTab("HostAssetsInfo"));var t},style:(0,c.j5)({cursor:"online"===e.source&&["ipCount","ipv6Count","webCount"].includes(a.key)?"pointer":""})},(0,c.zw)(a.value),13,h),(0,t._)("span",x,(0,c.zw)(d[a.key].name),1)])])))),128))]))}});var _=n(93379),D=n.n(_),S=n(7795),T=n.n(S),U=n(90569),Z=n.n(U),q=n(3565),E=n.n(q),V=n(19216),A=n.n(V),j=n(44589),z=n.n(j),F=n(43178),H={};H.styleTagTransform=z(),H.setAttributes=E(),H.insert=Z().bind(null,"head"),H.domAPI=T(),H.insertStyleElement=A(),D()(F.Z,H),F.Z&&F.Z.locals&&F.Z.locals;const K=(0,n(83744).Z)(I,[["__scopeId","data-v-7644b69c"]]),P=(0,t.aZ)({__name:"StatisticInfoCategory",props:{paramsData:{}},setup(a){const e=a;return(a,n)=>((0,t.wg)(),(0,t.j4)(K,{paramsData:e.paramsData},null,8,["paramsData"]))}})}}]);