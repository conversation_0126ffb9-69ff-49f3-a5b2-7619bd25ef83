"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[6513],{36513:(e,t,s)=>{s.d(t,{Nk:()=>r,getDataBycompAPI:()=>o});var n=s(14068);s(5255);const r=(e,t)=>{const s={vul_task:`/interface/report/sys/vuln-distribution/${e.task_ids}`,vul_config:`/interface/report/sys/vuln-distribution/${e.task_ids}`,image_task:`/interface/report/image-scan/flaw-information-list/${e.task_ids}`,web_scan:`/interface/report/web-scan/vuln-distribution/${e.task_ids}`};return n.h.get({url:s[t],params:e})},o=e=>n.h.get({url:e})},14068:(e,t,s)=>{s.d(t,{h:()=>I});var n=s(61446),r=s(52861),o=s(5255),a=s(22560),i=s(18036);const c=(0,i.H)(),u=(e="")=>e;var p=s(1869),l=s(22702),d=s(947);const h=d.o9.LOCAL;function f(e,t){return(h?l.G.setLocal:l.G.setSession)(e,t,!0)}var m=s(31641);const g=(void 0).VITE_BASE_URL,k=(0,i.H)(),v={transformResponseHook(e,t){const{isReturnNativeResponse:s,isTransformResponse:r}=t;if(k.setPageLoading(!1),s)return e;if(!r)return e.data;const{data:o}=e;o||console.log("出错");const{code:a,message:i}=o;if(a&&200==a)return{...o};let c=i||"";switch(a){case 401:return f(d.B1,""),f(d.Ee,null),l.G.removeSession(d.OT),window.location.reload(),{...o};case 400:case 500:break;default:i&&(c=i)}c&&n.ZP.warning(c)},transformResponseErroHook(e){if(k.setPageLoading(!1),"canceled"!=e.message)return{code:501,data:{message:"系统服务异常，请联系管理员"}}},requestInterceptors(e){const t=(0,p.q)(),s=(n=d.B1,(h?l.G.getLocal:l.G.getSession)(n));var n;if(t.getLocale){const s=m.l.filter((e=>e.event==t.getLocale));e.headers["Accept-Language"]=s[0].serviceKey}return s&&(e.headers.token=s),e}},I=(k.setPageLoading(!0),new class{constructor(e){this.options=e,this.axiosInstance=r.Z.create(e),this.setupInterceptors()}createAxios(e){this.axiosInstance=r.Z.create(e)}getTransform(){const{transform:e}=this.options;return e}getAxios(){return this.axiosInstance}configAxios(e){this.axiosInstance&&this.createAxios(e)}setupInterceptors(){const e=this.getTransform();if(!e)return;const{requestInterceptors:t,responseInterceptorsCatch:s,responseInterceptors:n}=e||{};this.axiosInstance.interceptors.request.use((e=>(t&&(e=t(e,this.options)),e.cancelToken=new r.Z.CancelToken((t=>{c.setCancelFnByUrl(e.url,t)})),e))),this.axiosInstance.interceptors.response.use((e=>(n&&(0,a.mf)(n)&&(e=n(e)),e)),void 0),s&&(0,a.mf)(s)&&this.axiosInstance.interceptors.response.use(void 0,(e=>s(this.axiosInstance,e)))}get(e,t){const s=u(e.url);return this.request({...e,method:"GET",url:s},t||{})}post(e,t){const s=u(e.url);return this.request({...e,method:"POST",url:s},t||{})}put(e,t){const s=u(e.url);return this.request({...e,method:"PUT",url:s},t||{})}delete(e,t){const s=u(e.url);return this.request({...e,method:"DELETE",url:s},t||{})}uploadFile(e,t){const s=u(e.url),n=new window.FormData,r=t.name||"file";return t.filename?n.append(r,t.file,t.filename):n.append(r,t.file),t.data&&Object.keys(t.data).forEach((e=>{const s=t.data[e];Array.isArray(s)?s.forEach((t=>{n.append(`${e}[]`,t)})):n.append(e,t.data[e])})),this.axiosInstance.request({...e,url:s,method:"POST",data:n,headers:{"Content-type":"multipart/form-data;charset=UTF-8",ignoreCancelToken:!0}})}request(e,t){const s=(0,o.p$)(e),{requestOptions:n}=this.options,{transform:r}=this.options,{transformResponseHook:a,transformResponseErroHook:i}=r||{},c=Object.assign({},n,t);return s.requestOptions=c,new Promise(((e,t)=>{this.axiosInstance.request(s).then((s=>{if(a)try{const t=a(s,c);e(t)}catch(e){t(e)}e(s)})).catch((s=>{if(i)try{const t=i(s);e(t)}catch(s){t(s)}t(s)}))}))}}((0,o.RH)({transform:(0,o.p$)(v),baseURL:g,headers:{"Content-Type":"application/json;charset=UTF-8"},requestOptions:{isReturnNativeResponse:!1,isTransformResponse:!0}},undefined)))}}]);