"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[798],{16205:(a,e,d)=>{d.d(e,{Z:()=>l});var t=d(8081),c=d.n(t),n=d(23645),f=d.n(n)()(c());f.push([a.id,".listTable[data-v-c15f4aba] {\n  width: 100%;\n}\n.listTable tbody[data-v-c15f4aba] {\n  width: 100%;\n}\n.listTable tbody tr[data-v-c15f4aba] {\n  width: 100%;\n}\n.listTable tbody tr[data-v-c15f4aba]:nth-child(odd) {\n  background-color: #f7f8fa;\n}\n.listTable tbody tr td[data-v-c15f4aba],\n.listTable tbody tr th[data-v-c15f4aba] {\n  height: 48px;\n}\n.listTable tbody tr th[data-v-c15f4aba] {\n  padding: 0 10px 0 24px;\n  width: 10%;\n  text-align: left;\n  white-space: nowrap;\n}\n.listTable tbody tr td[data-v-c15f4aba] {\n  width: 40%;\n  /* stylelint-disable-next-line no-descending-specificity */\n}\n.listTable tbody tr td .valspan[data-v-c15f4aba] {\n  display: block;\n  padding: 12px 0;\n  min-width: 100px;\n  white-space: normal;\n  word-break: break-all;\n}\n.listTable tbody tr td .comp[data-v-c15f4aba] {\n  display: flex;\n  width: 100%;\n}\n.listTable tbody tr td .comp.justify[data-v-c15f4aba] {\n  justify-content: end;\n}\n.listTable tbody tr .justify[data-v-c15f4aba] {\n  width: 1%;\n}\n.listTable.border[data-v-c15f4aba] {\n  border: 1px solid #ebeef2;\n  /* stylelint-disable-next-line no-descending-specificity */\n}\n.listTable.border tr[data-v-c15f4aba] {\n  border-bottom: 1px solid #ebeef2;\n}\n.listTable.border tr th[data-v-c15f4aba] {\n  padding-left: 0;\n  border-right: 1px solid #ebeef2;\n  text-align: center;\n}\n.listTable.border tr td[data-v-c15f4aba] {\n  width: auto;\n  border-right: 1px solid #ebeef2;\n  text-align: center;\n  /* stylelint-disable-next-line no-descending-specificity */\n}\n.listTable.border tr td .comp[data-v-c15f4aba] {\n  display: block;\n  text-align: center;\n}\n.listTable.border tr[data-v-c15f4aba]:last-child {\n  border-bottom: none;\n}\n",""]);const l=f},27350:(a,e,d)=>{d.d(e,{Z:()=>x}),d(57658);var t=d(66252),c=d(3577),n=d(79414);const f=["colspan"],l={key:1,class:"valspan"},s=(0,t.aZ)({__name:"ListCard",props:{paramsData:{},border:{type:Boolean}},setup(a){const e=a,{t:d}=n.i18n.global,s=(0,t.Fl)((()=>(a=>{const e=[];let d=[];for(let t=0;t<a.length;t++){const c=a[t];if(c.isarow){const a=[{...c}];e.push(a)}else d.push(c),2!==d.length&&t!==a.length-1||(e.push([...d]),d=[])}return e})(e.paramsData)));return(a,d)=>((0,t.wg)(),(0,t.iD)("table",{class:(0,c.C_)(["listTable",e.border?"border":""])},[(0,t._)("tbody",null,[((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)(s.value,((e,d)=>((0,t.wg)(),(0,t.iD)("tr",{key:d},[((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)(e,(d=>((0,t.wg)(),(0,t.iD)(t.HY,{key:d.title},[(0,t._)("th",null,(0,c.zw)(d.title),1),(0,t._)("td",{class:(0,c.C_)(d.isJustify?"justify":""),colspan:1==e.length?3:0},[(0,t.WI)(a.$slots,d.key,{record:d},(()=>["object"==typeof d.value?((0,t.wg)(),(0,t.iD)("div",{key:0,class:(0,c.C_)(["comp",d.isJustify?"justify":""])},[((0,t.wg)(),(0,t.j4)((0,t.LL)(d.value)))],2)):((0,t.wg)(),(0,t.iD)("span",l,(0,c.zw)(d.value),1))]))],10,f)],64)))),128))])))),128))])],2))}});var i=d(93379),b=d.n(i),r=d(7795),o=d.n(r),p=d(90569),y=d.n(p),u=d(3565),g=d.n(u),h=d(19216),w=d.n(h),v=d(44589),m=d.n(v),T=d(16205),k={};k.styleTagTransform=m(),k.setAttributes=g(),k.insert=y().bind(null,"head"),k.domAPI=o(),k.insertStyleElement=w(),b()(T.Z,k),T.Z&&T.Z.locals&&T.Z.locals;const x=(0,d(83744).Z)(s,[["__scopeId","data-v-c15f4aba"]])},90798:(a,e,d)=>{d.r(e),d.d(e,{default:()=>f});var t=d(66252),c=d(27350),n=d(79414);const f=(0,t.aZ)({__name:"ImageInfoComp",props:{paramsData:{},source:{}},setup(a){const e=a,{t:d}=n.i18n.global,f={imageType:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fd99aaae727d9c833639a0f53df15cf61e13c8d2fe8e382dca456d82e87632d182ac9be0594ea98f11a2a1921880e14dd"),imageLayer:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fd99aaae727d9c833639a0f53df15cf61e13c8d2fe8e382dca456d82e87632d18a0e2d5dc828562af1a11f8775ce77c74"),imageSize:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fd99aaae727d9c833639a0f53df15cf61e13c8d2fe8e382dca456d82e87632d1801cbe9ceda6b46448fe85250369c64c2"),imageTag:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fd99aaae727d9c833639a0f53df15cf61e13c8d2fe8e382dca456d82e87632d188f3ed414e95fc34008e960ae8d14ae14"),imageId:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fd99aaae727d9c833639a0f53df15cf61e13c8d2fe8e382dca456d82e87632d186d66909e935c32e8cb1b131beee4efd7"),imageWarehouse:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fd99aaae727d9c833639a0f53df15cf61e13c8d2fe8e382dca456d82e87632d1814d8be32032f77571fe02964b40967aa"),imageOs:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fd99aaae727d9c833639a0f53df15cf61e2e72f6debd61a35a7529278024469070f2dc672f3fe37d66ad29cd5171d43e8"),osVersion:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fd99aaae727d9c833639a0f53df15cf618231dc507845aea0bbedc2d52c79e1ba0788aa1ab72ff5af2bba4ad74811d1d8"),osBit:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fd99aaae727d9c833639a0f53df15cf618231dc507845aea0bbedc2d52c79e1ba40ddc743a57d5fb6cdcc4bf292e7dd0d"),docker_file:"DockerFile"},l=(0,t.Fl)((()=>{let a=Object.keys(e.paramsData).map((a=>({key:f[a],value:"docker_file"===a?(0,t.h)("pre",{style:{whiteSpace:"pre-wrap",wordWrap:"break-word"}},e.paramsData[a]):e.paramsData[a]})));return[{key:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fd99aaae727d9c833639a0f53df15cf61611307737cf510e30bbc9663e91f329d96493ec62163b0870dc3b6aa42dc0942"),value:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fd99aaae727d9c833639a0f53df15cf61f8bacdad4adf603623faa74ff05d9e4884fb2acac0450d60e06c78210806c0c1")},...a].map((a=>({...a,title:a.key,isarow:!0})))}));return(a,e)=>((0,t.wg)(),(0,t.j4)(c.Z,{paramsData:l.value},null,8,["paramsData"]))}})}}]);