name: 部署到测试环境
on:
  push:
    branches: [ staging ]  # 根据您的需求调整分支名

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Java for package
        uses: actions/setup-java@v4
        with:
          java-version: '11'
          distribution: 'microsoft'

      - name: Package the application
        run: mvn clean package -DskipTests

      - name: Rename the JAR file
        run: mv target/dam-port-1.0.0.jar app.jar

      - name: Deploy to server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SIT_HOST }}
          username: ${{ secrets.SIT_USERNAME }}
          password: ${{ secrets.SIT_PASSWORD }}
          source: "app.jar"
          target: "/opt/ticai/ticai-dam-port"

      - name: Restart service
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SIT_HOST }}
          username: ${{ secrets.SIT_USERNAME }}
          password: ${{ secrets.SIT_PASSWORD }}
          script: cd /opt/ticai && docker compose down ticai-dam-port && docker compose up -d ticai-dam-port
