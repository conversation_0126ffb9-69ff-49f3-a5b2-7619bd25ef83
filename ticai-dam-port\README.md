# 基于物联网彩票设备管理平台

## 项目简介

这是一个基于物联网技术的彩票设备管理平台，提供设备监控、管理和维护等功能。该项目是外部接口部分，用于与其他系统进行数据交互。

## 技术栈

- **后端框架**：Spring Boot 2.6.6
- **微服务**：Spring Cloud 2021.0.1
- **数据库**：MySQL
- **缓存**：Redis
- **API文档**：Swagger/OpenAPI
- **其他**：MyBatis、Feign、Eureka

## 环境要求

- JDK 11+
- Maven 3.6+
- MySQL 5.7+
- Redis

## 项目配置

项目包含多个环境配置：

- `application.yml`：基础配置
- `application-dev.yml`：开发环境配置
- `application-prod.yml`：生产环境配置
- `application-tc.yml`：测试环境配置
- `application-zx.yml`：其他环境配置

## 项目启动

### 编译打包

```bash
mvn clean package
```

### 运行项目

```bash
java -jar dam-port-1.0.0.jar --spring.profiles.active=dev
```

## 接口文档

启动项目后，可通过以下地址访问Swagger接口文档：

```
http://ip:port/port-api/swagger-ui/index.html
```

## 主要功能

- 设备管理
- 数据采集
- 地理位置服务（高德地图API集成）
- 短信通知（阿里云SMS集成）
- 微信集成

## 项目结构

```
dam-port/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── zy/
│   │   │           ├── dam/
│   │   │           │   ├── app/
│   │   │           │   ├── feign/
│   │   │           │   └── DamPortApplication.java
│   │   │           ├── init/
│   │   │           └── mybatis/
│   │   └── resources/
│   │       ├── META-INF/
│   │       ├── application.yml
│   │       ├── application-dev.yml
│   │       ├── application-prod.yml
│   │       ├── logback.xml
│   │       └── mybatis-mysql.xml
│   └── test/
├── libs/
├── pom.xml
└── README.md
```

## 联系方式

- 作者：Kian Hui
- 项目地址：http://localhost

## 许可证

版权所有 © 2023