"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[1572],{13914:(e,a,d)=>{d.d(a,{Z:()=>i});var c=d(8081),t=d.n(c),f=d(23645),b=d.n(f)()(t());b.push([e.id,".chartItem[data-v-d1edd360] {\n  width: calc(100% / 2);\n  height: 100%;\n  min-height: 380px;\n  border-right: 1px solid #ebeef2;\n}\n.chartItem[data-v-d1edd360]:last-child {\n  border-right: none;\n}\n.chartItemImg[data-v-d1edd360] {\n  display: none;\n}\n",""]);const i=b},87385:(e,a,d)=>{d.d(a,{Z:()=>i});var c=d(8081),t=d.n(c),f=d(23645),b=d.n(f)()(t());b.push([e.id,".chartContent[data-v-4f627ab6] {\n  display: flex;\n  flex-wrap: wrap;\n}\n.vulnCategoriesData[data-v-4f627ab6] {\n  width: 100%;\n  min-height: 380px;\n}\n.vulnCategoriesData > .title[data-v-4f627ab6] {\n  margin-top: 22px;\n  margin-left: 25px;\n  font-size: 18px;\n  font-weight: bold;\n}\n",""]);const i=b},81572:(e,a,d)=>{d.r(a),d.d(a,{default:()=>j});var c=d(66252),t=d(3577),f=d(425),b=(d(57658),d(85827),d(2262)),i=(d(5255),d(69611)),n=d(24206),s=d(1869);const o=["id"],r=(0,c.aZ)({__name:"RiskDistribution",props:{paramsData:{},taskType:{}},setup(e){const a=e,d=(0,s.E)(),{t}=(0,n.QT)(),f={riskDistributionByHost:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd3568945b0b1f7db5e07566b81351f5aa69f270f4e2459c36b0c8ecdf0477f44848"),riskDistributionByPage:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd350172e640aa3ccf40bf1c72c65470ca405d0c4f52ea2677de969f49f67a200ff5"),riskDistributionBySite:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd356d84f9cf4d0aa8da9d5c9ecb3d1a8d4dda704c243cf027c314f853efd53f9baa"),riskConfigDistributionByHost:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35a756ac403894ddc92a2c98b07655382c59905067a06e24517cdbdd1af9a35812c27ec94153174aa2fd79223786ee1178"),riskDistributionByVuln:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35ff4f05bdcf439c7fbd36120412e32ca775f2abe92f042bde6b69529802109a1e6686438f0d9792b1d02c41a5e0a23302"),fileRiskData:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35b66be323926a0387ed0ada6d33ea20897c6b20cf32f54625a835e9fa80d74cb8"),vulnRiskData:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35e389b5277b10c31003e30ecd6f88621498e6f227b1eeb53415ee3ab99ea916abacca6e6f210ff5ec38975947c74a0bfc"),imageWholeRiskData:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35811969f573dc768958d2b345595ce3d0258e552ad4e008200ba59bd34a716c568434e2733d9fb1e296dc6f75abd76957"),imageVulnRiskData:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35a3f4f086927ecfcf46334de9e16e7420d8c295aff847784118d10b44fed86e83e4655373ae49acd7c3d36406e5069899"),imageConfigRiskData:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35a7031ce85c2b53e7b5ed394cff3e95f54c1ea19f0d0e29fb29e17762902c777396e233f364700e0628b412d4cbd99108"),vuln:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35a9276d7347c940dbb64af06b354e93b273c4c896e88b00fdaa340049adc806be83b3286846642b56954790b49f91efb8"),total:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35e8c6363ae73364ce4fd355712ae18138a9a7f671c8b5a70c8359ee8155de8d7e073a28e812bcec5d9112142715b626a4"),config:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35866e3b80949d1880c7cbd24c6d6de0f13b14453bde3460fc70c891b1231488950dcd0c04436524e57e3127e79a722a98")},r=(0,b.iH)({}),l=(0,b.iH)([]),u=((0,b.iH)({}),["#68B92E","#5F96EA","#6B69F2","#F2C664","#65789B","#62A7F2","#008685","#F1A45D","#DE5454","#BA7DE7"]),m=["#F52121","#fa8c16","#178eff","#68b92e","#dddddd"],h=["#F52121","#fa8c16","#68b92e","#dddddd"],p=[{safe:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35b38cb44aa3993b37914dd2217adab63ddaed561e56287d7cdf4c39e3c90fc187"),low:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35fa486c6f1d17d4733d28f159ab52c6e4fbf907924611c761cb4446538a8df320"),middle:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd352e428d47c581c620d7aa59736935e30dd9b5392b8d1d0c199dc20673f6d313da"),high:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35e24e8aad5922d7facbb8f42a4a82def7e1e77d3cb01e80f263f72bf69d9f38db"),unknown:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35514150a13bc2b0ae31e4eb74a420f7bcc0983d72d0bc54e20efc35dcbd1c0457")},{low:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35d006fe74848b6d06fc3fec069a6a80bd"),middle:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35b1fe74578481dded9dce3b39e9f44e7a"),high:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd356fc7da1a9ac3fb4cfde0063ac9d84b2a")},{safe:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35d12697bf2b60de160ac7a4fae513420f"),low:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35d006fe74848b6d06fc3fec069a6a80bd"),middle:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35b1fe74578481dded9dce3b39e9f44e7a"),high:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd356fc7da1a9ac3fb4cfde0063ac9d84b2a")}],g=e=>{e&&e&&e.getAttribute("id")&&(r.value[e.getAttribute("id")]=e)};function v(e,a){if("riskConfigDistributionByHost"===a){const a=[{name:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd353cd10ecc3fb9dbaa0447ee5f7f14b5d4"),data:[],legendColor:"#F52121"},{name:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35cf0afddb2b9008bccf0cd0595e0fa238"),data:[],legendColor:"#fa8c16"},{name:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd3535335cd3e10551931bb23e7f32d593fe"),data:[],legendColor:"#178eff"}],d=e.reduce(((e,a)=>(e.push(a.name||a.title),e)),[]);return e.forEach((e=>{a[0].data.push(e.high),a[1].data.push(e.middle),a[2].data.push(e.low)})),{xdata:(0,b.iH)(d),result:a,yAxisName:t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd35a756ac403894ddc92a2c98b07655382cd1d70a3103dbecab585e8adf4fcfce82")}}{const d="riskDistributionByPage"===a?p[2]:Object.keys(e).includes("safe")?p[0]:p[1];return Object.keys(e).map((a=>({name:d[a],value:e[a]})))}}function D(e,a){if("bar"===a.type)(0,i.HC)(e,{title:a.title,result:a.data.result,xdata:a.data.xdata,yAxisName:a.data.yAxisName,isShowline:(0,b.iH)(!1),finished:a.finished,grid:{top:"25%",left:"zh"===d.getLocale?50:100,right:"2%",bottom:"10%"},titleOpt:{textStyle:{fontSize:"zh"===d.getLocale?16:14,width:350,overflow:"break"}}});else{const c=[{type:"pie",radius:["50%","65%"],datasetIndex:2,labelLine:!1,label:{show:!1,position:"center",formatter:["{num|{c}}","{name|{b}}"].join("\n"),rich:{num:{color:"#535353",fontSize:"24px",fontWeight:600},name:{fontSize:"12px",color:"#7e7e7e",padding:[8,0,0,0]}}},data:a.data.map((e=>({emphasis:{},...e}))),emphasis:{scaleSize:10,label:{show:!1},itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}},{type:"pie",center:["50%","50%"],radius:["50%","54%"],label:{show:!0,position:"center",formatter:[`{num|${1==a.data.length?a.data[0].value:a.data.reduce(((e,a)=>e+a.value),0)}}`,`{name|${t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f28b51b500c5551e400a0b5be35ecfd353ab8e73216a4216e4b674d37dedbe8a8")}}`].join("\n"),rich:{num:{color:"#535353",fontSize:"24px",fontWeight:600},name:{fontSize:"12px",color:"#7e7e7e",padding:[8,0,0,0]}}},data:a.data.map((e=>({emphasis:{},...e}))),itemStyle:{color:"rgba(0,0,0,.1)"},emphasis:{disabled:!0}}],{getInstance:f}=(0,i.gE)(e,{data:a.data,title:a.title,mode:"sum",colors:u,finished:a.finished,customOption:{title:{text:a.title,padding:[20,24],left:0,textStyle:{fontSize:"zh"===d.getLocale?16:14,width:350,overflow:"break"}},color:4!=a.data.length&&5!=a.data.length||"riskDistributionByPage"==a.name?h:m,legend:{show:!0,orient:"horizontal",y:"bottom",align:"auto",icon:"circle",bottom:20,itemWidth:13,itemHeight:13,textStyle:{color:"#7e7e7e",fontSize:14},formatter:e=>{const d=a.data.find((a=>a.name===e));return`${e}[${d.value}${t("onlineReport.items")}]`}},tooltip:{show:!1},grid:{bottom:"5%",containLabel:!0},series:c}})}}return(0,c.YP)((()=>a.paramsData),(()=>{!function(){const e=a.paramsData;l.value=[];for(let a in e)e[a]&&(l.value.push({key:a}),(0,c.Y3)((()=>{D(r.value[a],{data:v(e[a],a),name:a,title:f[a],type:["riskConfigDistributionByHost"].includes(a)?"bar":"pie"})})))}()}),{deep:!0,immediate:!0}),(e,a)=>((0,c.wg)(!0),(0,c.iD)(c.HY,null,(0,c.Ko)(l.value,(e=>((0,c.wg)(),(0,c.iD)("div",{class:"chartItem chart_host riskchart",id:e.key,ref_for:!0,ref:g,key:e.key},null,8,o)))),128))}});var l=d(93379),u=d.n(l),m=d(7795),h=d.n(m),p=d(90569),g=d.n(p),v=d(3565),D=d.n(v),y=d(19216),k=d.n(y),w=d(44589),x=d.n(w),C=d(13914),S={};S.styleTagTransform=x(),S.setAttributes=D(),S.insert=g().bind(null,"head"),S.domAPI=h(),S.insertStyleElement=k(),u()(C.Z,S),C.Z&&C.Z.locals&&C.Z.locals;var Z=d(83744);const _=(0,Z.Z)(r,[["__scopeId","data-v-d1edd360"]]);var z=d(38610),B=d(79414);const A={class:"report_item riskDistribution"},H={class:"chartContent"},I={key:0,class:"vulnCategoriesData chartItem"},T={class:"title"},E=(0,c.aZ)({__name:"RiskDistribution",props:{paramsData:{},taskType:{}},setup(e){const a=e,{t:d}=B.i18n.global,b=(0,c.Fl)((()=>{const e={};for(let d in a.paramsData)"vulnCategoriesData"!=d&&a.paramsData[d]&&(e[d]=a.paramsData[d]);return e}));return(e,d)=>((0,c.wg)(),(0,c.iD)("div",A,[Object.values(b.value).length||a.paramsData.vulnCategoriesData?((0,c.wg)(),(0,c.j4)(f.Z,{key:0},{default:(0,c.w5)((()=>[(0,c._)("div",H,[(0,c.Wm)(_,{paramsData:b.value,taskType:a.taskType},null,8,["paramsData","taskType"])]),a.paramsData.vulnCategoriesData?((0,c.wg)(),(0,c.iD)("div",I,[(0,c._)("h5",T,(0,t.zw)(e.$t("onlineReport.vulnCategoriesData")),1),(0,c.Wm)(z.Z,{data:a.paramsData.vulnCategoriesData,name:""},null,8,["data"])])):(0,c.kq)("v-if",!0)])),_:1})):(0,c.kq)("v-if",!0)]))}});var F=d(87385),R={};R.styleTagTransform=x(),R.setAttributes=D(),R.insert=g().bind(null,"head"),R.domAPI=h(),R.insertStyleElement=k(),u()(F.Z,R),F.Z&&F.Z.locals&&F.Z.locals;const j=(0,Z.Z)(E,[["__scopeId","data-v-4f627ab6"]])}}]);