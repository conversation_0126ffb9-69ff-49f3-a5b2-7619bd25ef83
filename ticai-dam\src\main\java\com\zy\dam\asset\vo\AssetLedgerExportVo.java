package com.zy.dam.asset.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;
import com.zy.dam.asset.util.AssetStatusUtil;

/**
 * 资产台账导出数据模型
 * 用于将资产台账信息导出到Excel文件
 */
@Data
public class AssetLedgerExportVo {

    @ExcelIgnore
    private String id;

    @ExcelProperty("管理部门")
    private String deptName;

    @ExcelProperty("资产编码")
    private String no;

    @ExcelProperty("资产分类")
    private String categoryName;

    @ExcelProperty("资产类型")
    private String typeName;

    @ExcelProperty("资产名称")
    private String name;

    @ExcelIgnore
    private Date productDate;

    @ExcelProperty("投入使用日期")
    private String productDateText;

    @ExcelIgnore
    private Date takeDate;

    @ExcelProperty("取得日期")
    private String takeDateText;

    @ExcelProperty("资产原值")
    private Double selfValue;

    @ExcelProperty("折旧/摊销年限")
    private Integer expiryMonth;

    @ExcelIgnore
    private Date financeDate;

    @ExcelProperty("财务入账日期")
    private String financeDateText;

    @ExcelProperty("净值")
    private Double value;

    @ExcelProperty("使用部门")
    private String useDeptName;

    @ExcelProperty("使用人")
    private String useUserName;

    @ExcelProperty("规格型号")
    private String spec;

    @ExcelProperty("品牌")
    private String brand;

    @ExcelProperty("生产厂商")
    private String manu;

    @ExcelProperty("发票号")
    private String invoice;

    @ExcelProperty("销售商")
    private String seller;

    @ExcelProperty("会计凭证号")
    private String voucher;

    @ExcelProperty("合同编号")
    private String contract;

    @ExcelProperty("区域")
    private String regionName;

    @ExcelProperty("网点名称")
    private String locationName;

    @ExcelProperty("网点地址")
    private String locationAddress;

    @ExcelProperty("联系人")
    private String locationContact;

    @ExcelProperty("联系电话")
    private String locationPhone;

    @ExcelProperty("定位地址")
    private String locAddr;

    @ExcelProperty("备注")
    private String memo;

    @ExcelIgnore
    private String status;

    @ExcelProperty("状态")
    private String statusText;

    @ExcelProperty("是否定位")
    private String whetherLocation;

    // 新增字段：参考资产终端机绑定统计
    @ExcelProperty("终端编号")
    private String nowSn;

    @ExcelProperty("绑定姓名")
    private String bindUserName;

    @ExcelProperty("绑定账号")
    private String bindUserAccount;

    @ExcelIgnore
    private String bindUserGender;

    @ExcelProperty("性别")
    private String bindUserGenderText;

    @ExcelIgnore
    private String bindUserStatus;

    @ExcelProperty("状态")
    private String bindUserStatusText;

    @ExcelProperty("所属机构")
    private String bindUserDeptName;

    @ExcelIgnore
    private Date bindTime;

    @ExcelProperty("绑定时间")
    private String bindTimeText;

    // 格式化方法
    public String getProductDateText() {
        if (productDate == null)
            return "";
        return new SimpleDateFormat("yyyy-MM-dd").format(productDate);
    }

    public String getTakeDateText() {
        if (takeDate == null)
            return "";
        return new SimpleDateFormat("yyyy-MM-dd").format(takeDate);
    }

    public String getFinanceDateText() {
        if (financeDate == null)
            return "";
        return new SimpleDateFormat("yyyy-MM-dd").format(financeDate);
    }

    public String getStatusText() {
        return AssetStatusUtil.getStatusText(status);
    }

    public String getBindTimeText() {
        if (bindTime == null)
            return "";
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(bindTime);
    }

    public String getBindUserGenderText() {
        return AssetStatusUtil.getGenderText(bindUserGender);
    }

    public String getBindUserStatusText() {
        return AssetStatusUtil.getUserStatusText(bindUserStatus);
    }

    // 设置方法，自动转换格式化字段
    public void setProductDate(Date productDate) {
        this.productDate = productDate;
        this.productDateText = getProductDateText();
    }

    public void setTakeDate(Date takeDate) {
        this.takeDate = takeDate;
        this.takeDateText = getTakeDateText();
    }

    public void setFinanceDate(Date financeDate) {
        this.financeDate = financeDate;
        this.financeDateText = getFinanceDateText();
    }

    public void setStatus(String status) {
        this.status = status;
        this.statusText = getStatusText();
    }

    public void setBindTime(Date bindTime) {
        this.bindTime = bindTime;
        this.bindTimeText = getBindTimeText();
    }

    public void setBindUserGender(String bindUserGender) {
        this.bindUserGender = bindUserGender;
        this.bindUserGenderText = getBindUserGenderText();
    }

    public void setBindUserStatus(String bindUserStatus) {
        this.bindUserStatus = bindUserStatus;
        this.bindUserStatusText = getBindUserStatusText();
    }
}
