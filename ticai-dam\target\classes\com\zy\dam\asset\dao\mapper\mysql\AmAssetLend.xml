<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.asset.dao.AmAssetLendDAO">

    <sql id="meta">
			a.ID_
			,a.OPT_
			,a.NO_
			,a.USER_
			,a.TIME_
			,a.PLAN_BACK_TIME_
			,a.REAL_BACK_TIME_
			,a.BORROWER_
			,a.ADDRESS_
			,a.MEMO_
			,a.REF_
			,a.STATUS_
			,a.CDEPT_
			,a.CUSER_
			,a.CHECK_USER_
			,a.CHECK_TIME_
			,a.CHECK_MEMO_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.asset.orm.AmAssetLend">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into AM_ASSET_LEND(ID_,OPT_,NO_,USER_,TIME_,PLAN_BACK_TIME_,REAL_BACK_TIME_,BORROWER_,ADDRESS_,MEMO_,REF_,STATUS_,CDEPT_,CUSER_,CTIME_,CHECK_USER_,CHECK_TIME_,CHECK_MEMO_)
        values(#{id},#{opt},#{no},#{user},#{time},#{planBackTime},#{realBackTime},#{borrower},#{address},#{memo},#{ref},#{status},#{cdept},#{cuser},now(),#{checkUser},#{checkTime},#{checkMemo})
    </insert>

    <!-- 更新数据 -->
    <update id="updateCheck" parameterType="com.zy.dam.asset.orm.AmAssetLend">
		update AM_ASSET_LEND
		set STATUS_=#{status},CHECK_USER_=#{checkUser},CHECK_TIME_=#{checkTime},CHECK_MEMO_=#{checkMemo}
		where ID_=#{id}
	</update>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.asset.vo.AssetLendVo">
        select
        <include refid="meta"/>
        ,(select count(0) from AM_ASSET_LEND_DETAIL m where m.BILL_=a.ID_) asset_count
        ,b.NAME_ user_name
        from AM_ASSET_LEND a left join SYS_USER b on a.USER_=b.ID_
        where a.OPT_=#{opt}
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.MEMO_ like concat('%',#{keyword},'%'))</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="user != null">and a.USER_=#{user}</if>
        <if test="userKey != null">and (b.NAME_ like concat('%',#{userKey},'%') or b.NO_ like concat('%',#{userKey},'%'))</if>
        <if test="begin != null">and a.TIME_ &gt;= #{begin}</if>
        <if test="end != null">and a.TIME_ &lt; date_add(#{end}, interval 1 day)</if>
        <if test="deptIds != null">
            <foreach collection="deptIds" item="deptId" open="and a.CDEPT_ in (" separator="," close=")">#{deptId}</foreach>
        </if>
        order by a.CTIME_ desc
    </select>

    <!-- 获取唯一的资管-资产借用归还数据 -->
    <select id="findOne" resultType="com.zy.dam.asset.orm.AmAssetLend">
        select
        <include refid="meta"/>
        from AM_ASSET_LEND a where a.ID_=#{0}
    </select>

    <insert id="insertDetail">
		insert into AM_ASSET_LEND_DETAIL(BILL_, ASSET_, ORD_, DEPT_, REGION_, LOCATION_, USE_DEPT_, USE_USER_, ASSET_STATUS_)
		values(#{id}, #{asset}, #{ord}, #{dept}, #{region}, #{location}, #{useDept}, #{useUser}, #{assetStatus})
	</insert>

    <!-- 更新资产领用，置为使用中 -->
    <update id="updateLend">
        update AM_ASSET a,AM_ASSET_LEND_DETAIL b, AM_ASSET_LEND c
        set a.STATUS_='3',a.LEND_STATUS_=a.STATUS_
        where a.ID_=b.ASSET_ and b.BILL_=c.ID_ and c.ID_=#{0}
    </update>
    <!-- 更新资产退库，置为空置 -->
    <update id="updateBack">
        update AM_ASSET a,AM_ASSET_LEND_DETAIL b, AM_ASSET_LEND c
        set a.STATUS_=a.LEND_STATUS_
        where a.ID_=b.ASSET_ and b.BILL_=c.ID_ and c.ID_=#{0}
    </update>

    <select id="findVo" resultType="com.zy.dam.asset.vo.AssetLendVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        from AM_ASSET_LEND a where a.ID_=#{0}
    </select>

    <select id="findDetail" resultType="com.zy.dam.asset.vo.AssetDetailVo">
        select a.ID_,a.TYPE_,a.NAME_,a.NO_,a.BO_DATE_,a.STATUS_
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.TYPE_) type_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.USE_DEPT_) use_dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USE_USER_) use_user_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=b.DEPT_) when_dept_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=b.REGION_) when_region_name
        ,(select m.NAME_ from AM_LOCATION m where m.ID_=b.LOCATION_) when_location_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=b.USE_DEPT_) when_use_dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=b.USE_USER_) when_use_user_name
        ,b.ASSET_STATUS_ when_status
        from AM_ASSET a,AM_ASSET_LEND_DETAIL b
        where a.ID_=b.ASSET_ and b.BILL_=#{0}
        order by b.ORD_
    </select>
</mapper>
