package com.zy.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletContextInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.IOException;

/**
 * Session Cookie安全配置
 */
@Configuration
@Slf4j
public class SessionConfig {

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Bean
    public ServletContextInitializer servletContextInitializer() {
        return new ServletContextInitializer() {
            @Override
            public void onStartup(ServletContext servletContext) throws ServletException {
                SessionCookieConfig sessionCookieConfig = servletContext.getSessionCookieConfig();

                // 设置Cookie安全属性
                // 在生产环境中启用Secure属性，开发环境中可以选择性启用
                boolean isProduction = !("zl".equals(activeProfile));
                sessionCookieConfig.setSecure(true); // 只在HTTPS连接中传输
                sessionCookieConfig.setHttpOnly(true); // 防止XSS攻击

                // 设置SameSite属性（如果需要）
                // sessionCookieConfig.setAttribute("SameSite", "Strict");

                // 可选：设置Cookie名称
                // sessionCookieConfig.setName("JSESSIONID");

                // 可选：设置Cookie路径
                // sessionCookieConfig.setPath("/");

                log.info("Session Cookie配置已应用 - Profile: " + activeProfile + ", Secure: " + isProduction);
            }
        };
    }

    /**
     * Cookie安全过滤器 - 为所有cookie添加安全属性
     */
    @Bean
    public FilterRegistrationBean<CookieSecurityFilter> cookieSecurityFilter() {
        FilterRegistrationBean<CookieSecurityFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new CookieSecurityFilter());
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(Integer.MIN_VALUE); // 设置为最高优先级
        return registrationBean;
    }

    /**
     * Cookie安全过滤器实现
     */
    public static class CookieSecurityFilter implements Filter {

        @Override
        public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
                throws IOException, ServletException {

            HttpServletResponse httpResponse = (HttpServletResponse) response;

            // 包装响应以拦截cookie设置和Header设置
            CookieSecurityResponseWrapper wrappedResponse = new CookieSecurityResponseWrapper(httpResponse);

            chain.doFilter(request, wrappedResponse);
        }
    }

    /**
     * 响应包装器 - 为cookie添加安全属性
     */
    public static class CookieSecurityResponseWrapper extends HttpServletResponseWrapper {

        public CookieSecurityResponseWrapper(HttpServletResponse response) {
            super(response);
        }

        @Override
        public void addCookie(Cookie cookie) {
            // 为所有cookie添加安全属性
            if ("zy_token".equals(cookie.getName()) || "vue_token".equals(cookie.getName())) {
                cookie.setSecure(true); // 只在HTTPS连接中传输
                cookie.setHttpOnly(false);
                String cookieValue = String.format("%s=%s; Secure; SameSite=Strict; Path=/",
                        cookie.getName(), cookie.getValue());
                super.setHeader("Set-Cookie", cookieValue);
                return;
            }
            super.addCookie(cookie);
        }

        @Override
        public void setHeader(String name, String value) {
            if ("Set-Cookie".equalsIgnoreCase(name)) {
                // 拦截Set-Cookie头，为zy_token和vue_token添加安全属性
                if (value.contains("zy_token=") || value.contains("vue_token=")) {
                    // 如果cookie值中还没有Secure属性，则添加
                    if (!value.contains("Secure")) {
                        value += "; Secure";
                    }
                    if (!value.contains("SameSite")) {
                        value += "; SameSite=Strict";
                    }
                    if (!value.contains("Path")) {
                        value += "; Path=/";
                    }
                }
            }
            super.setHeader(name, value);
        }

        @Override
        public void addHeader(String name, String value) {
            if ("Set-Cookie".equalsIgnoreCase(name)) {
                // 拦截Set-Cookie头，为zy_token和vue_token添加安全属性
                if (value.contains("zy_token=") || value.contains("vue_token=")) {
                    // 如果cookie值中还没有Secure属性，则添加
                    if (!value.contains("Secure")) {
                        value += "; Secure";
                    }
                    if (!value.contains("SameSite")) {
                        value += "; SameSite=Strict";
                    }
                    if (!value.contains("Path")) {
                        value += "; Path=/";
                    }
                }
            }
            super.addHeader(name, value);
        }
    }
}
