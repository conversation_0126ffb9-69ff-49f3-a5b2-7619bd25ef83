"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[5114],{29724:(a,e,n)=>{n.d(e,{Z:()=>r});var d=n(8081),t=n.n(d),l=n(23645),i=n.n(l)()(t());i.push([a.id,".vRcBarTableWrap[data-v-b13da312] {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n  width: 100%;\n}\n.vRcBarTableWrap .risktrendImg[data-v-b13da312] {\n  display: none;\n  height: 380px;\n  flex: 1;\n}\n.vRcBarTableWrap .vrcTab[data-v-b13da312] {\n  margin-left: 24px;\n  min-width: 380px;\n  height: auto;\n  flex: 1;\n}\n.vRcBarTableWrap .vrcTab th[data-v-b13da312] {\n  white-space: nowrap;\n}\n.vRcBarTableWrap .riskTrendRef[data-v-b13da312] {\n  height: 380px;\n  flex: 1;\n}\n",""]);const r=i},9188:(a,e,n)=>{n.d(e,{Z:()=>r});var d=n(8081),t=n.n(d),l=n(23645),i=n.n(l)()(t());i.push([a.id,".winTabWrap {\n  position: relative;\n  display: flex;\n  margin-top: 60px;\n  padding: 22px;\n  border: 3px solid #fff;\n  border-radius: 0 4px 4px;\n  background: #f1f1f3;\n  box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.06);\n  flex: 1;\n  flex-direction: column;\n}\n.winTabWrap.noTitle {\n  margin-top: 22px;\n}\n.winTabWrap > .header {\n  position: absolute;\n  top: -40px;\n  left: -3px;\n  z-index: 2;\n  padding: 0 30px 0 22px;\n  height: 40px;\n  border-top: 3px solid #fff;\n  border-left: 3px solid #fff;\n  border-radius: 4px 4px 0 0;\n  background: #f1f1f3;\n  box-shadow: 0 -1px 16px 0 rgba(0, 0, 0, 0.06);\n  line-height: 40px;\n}\n.winTabWrap > .header .title {\n  margin-top: 9px;\n  height: 30px;\n  font-size: 14px;\n  line-height: 30px;\n}\n.winTabWrap > .header ::after {\n  position: absolute;\n  top: 30px;\n  right: 0;\n  left: 0;\n  z-index: -1;\n  height: 20px;\n  background: #f1f1f3;\n  content: '';\n}\n.winTabWrap > .header ::before {\n  position: absolute;\n  top: 1px;\n  right: -24px;\n  width: 31px;\n  height: 52px;\n  border-right: 3px solid #fff;\n  border-radius: 2px;\n  background: #f1f1f3;\n  content: '';\n  transform: rotate(-45deg);\n}\n.winTabWrap > .body {\n  padding: 20px;\n  width: 100%;\n  border-radius: 4px;\n  background-color: #fff;\n}\n",""]);const r=i},425:(a,e,n)=>{n.d(e,{Z:()=>W});var d=n(66252),t=n(3577);const l={key:0,class:"header"},i={class:"title"},r={class:"body"},f={class:"footer"},c=(0,d.aZ)({__name:"winTab",props:{title:{}},setup(a){const e=a,n=(0,d.Fl)((()=>e.title||""));return(a,e)=>((0,d.wg)(),(0,d.iD)("div",{class:(0,t.C_)(["winTabWrap",{noTitle:!n.value}])},[n.value?((0,d.wg)(),(0,d.iD)("div",l,[(0,d._)("div",i,(0,t.zw)(n.value),1)])):(0,d.kq)("v-if",!0),(0,d._)("div",r,[(0,d.WI)(a.$slots,"default")]),(0,d._)("div",f,[(0,d.WI)(a.$slots,"footer")])],2))}});var s=n(93379),o=n.n(s),b=n(7795),p=n.n(b),u=n(90569),m=n.n(u),x=n(3565),g=n.n(x),h=n(19216),w=n.n(h),v=n(44589),T=n.n(v),_=n(9188),k={};k.styleTagTransform=T(),k.setAttributes=g(),k.insert=m().bind(null,"head"),k.domAPI=p(),k.insertStyleElement=w(),o()(_.Z,k),_.Z&&_.Z.locals&&_.Z.locals;const W=c},25114:(a,e,n)=>{n.r(e),n.d(e,{default:()=>I});var d=n(66252),t=n(2262),l=n(425),i=(n(57658),n(85827),n(3577)),r=n(69611),f=n(24206),c=n(1869);const s={class:"vRcBarTableWrap"},o=["src"],b=(0,d.aZ)({__name:"ConfigBarTable",props:{name:{},data:{},source:{},exportTypeTemplate:{}},setup(a,{expose:e}){var n,l,b;const p=a,u=(0,c.E)(),{t:m}=(0,f.QT)(),x=(0,t.iH)(null),g=(0,t.iH)(m("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f491f14a8b884d5f62ca21a20ceb4dda1bea3a7054c770bdb52d81c9a6f200f66b333f941c293d01e2cadec8565873095"));let h=null,w=()=>{};const v=(0,t.iH)(""),T=(0,t.qj)([{title:(0,d.Fl)((()=>(0,t.SU)(p.name))),dataIndex:null!==(n=p.data[0])&&void 0!==n&&n.count?"title":"name",minWidth:150,width:150},{title:null!==(l=p.data[0])&&void 0!==l&&l.count?m("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f491f14a8b884d5f62ca21a20ceb4dda170145bfa511c3096ae8399af58d65fe6"):m("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f491f14a8b884d5f62ca21a20ceb4dda14daf9e4e470e5c41ee54c0f26b4fa41c"),dataIndex:null!==(b=p.data[0])&&void 0!==b&&b.count?"count":"host_count",minWidth:100},{title:m("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f491f14a8b884d5f62ca21a20ceb4dda1fe00133a5f7e6053a45aa7eb3f1bfb8e"),dataIndex:"yes",minWidth:100},{title:m("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f491f14a8b884d5f62ca21a20ceb4dda1c7f5290c13b4b4aaf5d1d1f544fac438"),dataIndex:"no",minWidth:100},{title:m("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f491f14a8b884d5f62ca21a20ceb4dda1365e37920aff511099b7c2528678df67"),dataIndex:"all",minWidth:100}]),_=(0,d.Fl)((()=>p.data)),k=()=>{const{result:a,xdata:e}=(a=>{const e=[],n=[],d=[{name:m("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f491f14a8b884d5f62ca21a20ceb4dda1c7f5290c13b4b4aaf5d1d1f544fac438"),data:[],legendColor:"#EC4F4F"}];return a.map((a=>{e.push(a.no),n.push(a.name||a.title||"")})),d[0].data=e,{yes:[],no:e,xdata:(0,t.iH)(n),result:d}})((0,t.SU)(_)),{getInstance:n,resize:d}=(0,r.HC)(x,{grid:{top:"25%",left:"zh"===u.getLocale?30:90,right:"2%",bottom:"10%",containLabel:!0},result:a,xdata:e,yAxisName:g,title:`${m("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f491f14a8b884d5f62ca21a20ceb4dda1bea3a7054c770bdb52d81c9a6f200f66caad9d77be52e61f49a5995080349657")}${(0,t.SU)(p.name)}${m("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f491f14a8b884d5f62ca21a20ceb4dda132399aa6d3a32bc101b7af58ab1e799dce0a1cf88e6707ceb55e58b028ba569f")}`,isShowline:(0,t.iH)(!1),finished(a){v.value=a.getDataURL()},titleOpt:{padding:[10,0]}});h=n(),w=d};return(0,d.m0)((()=>{k()})),e({setEcharts:k,riskTrendRef:x,resize:()=>{var a;null===(a=h)||void 0===a||a.resize()},resizeChart:w}),(a,e)=>{const n=(0,d.up)("a-table-summary-cell"),t=(0,d.up)("a-table-summary-row"),l=(0,d.up)("a-table");return(0,d.wg)(),(0,d.iD)("div",s,[(0,d._)("div",{class:"riskTrendRef",ref_key:"riskTrendRef",ref:x},null,512),(0,d._)("img",{class:"risktrendImg",src:v.value},null,8,o),p.name?((0,d.wg)(),(0,d.j4)(l,{key:0,columns:T,"data-source":_.value,pagination:!1,class:"vrcTab"},{summary:(0,d.w5)((()=>[(0,d.Wm)(t,null,{default:(0,d.w5)((()=>[(0,d.Wm)(n,null,{default:(0,d.w5)((()=>[(0,d._)("b",null,(0,i.zw)(a.$t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f491f14a8b884d5f62ca21a20ceb4dda15a94ece5297f94e7bf6ef095f66b9404")),1)])),_:1}),(0,d.Wm)(n,null,{default:(0,d.w5)((()=>[(0,d._)("b",null,(0,i.zw)(p.data.reduce(((a,e)=>a+e.host_count||e.count),0)),1)])),_:1}),(0,d.Wm)(n,null,{default:(0,d.w5)((()=>[(0,d._)("b",null,(0,i.zw)(p.data.reduce(((a,e)=>a+e.yes),0)),1)])),_:1}),(0,d.Wm)(n,null,{default:(0,d.w5)((()=>[(0,d._)("b",null,(0,i.zw)(p.data.reduce(((a,e)=>a+e.no),0)),1)])),_:1}),(0,d.Wm)(n,null,{default:(0,d.w5)((()=>[(0,d._)("b",null,(0,i.zw)(p.data.reduce(((a,e)=>a+e.all),0)),1)])),_:1})])),_:1})])),_:1},8,["columns","data-source"])):(0,d.kq)("v-if",!0)])}}});var p=n(93379),u=n.n(p),m=n(7795),x=n.n(m),g=n(90569),h=n.n(g),w=n(3565),v=n.n(w),T=n(19216),_=n.n(T),k=n(44589),W=n.n(k),y=n(29724),Z={};Z.styleTagTransform=W(),Z.setAttributes=v(),Z.insert=h().bind(null,"head"),Z.domAPI=x(),Z.insertStyleElement=_(),u()(y.Z,Z),y.Z&&y.Z.locals&&y.Z.locals;const z=(0,n(83744).Z)(b,[["__scopeId","data-v-b13da312"]]),I=(0,d.aZ)({__name:"ConfigRiskCategory",props:{paramsData:{},taskType:{}},setup(a){const e=a,n=(0,d.Fl)((()=>(0,t.SU)(e.paramsData)));return(a,t)=>"image_task"==e.taskType?((0,d.wg)(!0),(0,d.iD)(d.HY,{key:0},(0,d.Ko)(n.value.configsRiskCategory,(a=>((0,d.wg)(),(0,d.j4)(l.Z,{key:a,title:a.name},{default:(0,d.w5)((()=>[(0,d.Wm)(z,{data:a.data,name:a.name},null,8,["data","name"])])),_:2},1032,["title"])))),128)):((0,d.wg)(!0),(0,d.iD)(d.HY,{key:1},(0,d.Ko)(n.value.riskCategories,(a=>((0,d.wg)(),(0,d.j4)(l.Z,{key:a,title:a.name},{default:(0,d.w5)((()=>[(0,d.Wm)(z,{data:a.data,name:a.name},null,8,["data","name"])])),_:2},1032,["title"])))),128))}})}}]);