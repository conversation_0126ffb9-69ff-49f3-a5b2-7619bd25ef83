package com.zy.dam.asset.util;

/**
 * 资产状态工具类
 * 统一管理资产状态的文本映射
 */
public class AssetStatusUtil {

    /**
     * 获取资产状态文本
     * 
     * @param status 状态代码
     * @return 状态文本
     */
    public static String getStatusText(String status) {
        if (status == null || status.isEmpty()) {
            return "";
        }
        switch (status) {
            case "1":
                return "闲置中";
            case "2":
                return "使用中";
            case "3":
                return "已借用";
            case "5":
                return "维修中";
            case "6":
                return "已锁定";
            case "7":
                return "已损坏";
            case "8":
                return "已报废";
            default:
                return "未知";
        }
    }

    /**
     * 获取用户性别文本
     * 
     * @param gender 性别代码
     * @return 性别文本
     */
    public static String getGenderText(String gender) {
        if (gender == null || gender.isEmpty()) {
            return "";
        }
        switch (gender) {
            case "1":
                return "男";
            case "2":
                return "女";
            default:
                return "";
        }
    }

    /**
     * 获取用户状态文本
     * 
     * @param status 用户状态代码
     * @return 用户状态文本
     */
    public static String getUserStatusText(String status) {
        if (status == null || status.isEmpty()) {
            return "";
        }
        switch (status) {
            case "1":
                return "可用";
            case "5":
                return "禁用";
            default:
                return "";
        }
    }
}
