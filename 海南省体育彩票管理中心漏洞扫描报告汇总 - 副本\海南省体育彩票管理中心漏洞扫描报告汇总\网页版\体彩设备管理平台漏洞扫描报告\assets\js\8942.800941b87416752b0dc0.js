"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[8942,83],{83:(e,t,s)=>{s.d(t,{batchExpAttackApi:()=>c,batchWebExpAttackApi:()=>f,correctConfigByHost:()=>k,correctConfigByImage:()=>v,correctImageVulApi:()=>i,correctSiteVulApi:()=>l,correctVulApi:()=>a,doAttackApi:()=>o,doVulConfirmApi:()=>g,getExpAttackProcessApi:()=>u,getSysatkApi:()=>n,getVulConfirmProcessApi:()=>m,getVulConfirmResultApi:()=>_,getWarningInfoApi:()=>p,getWebVulnInfoApi:()=>d,getWebWarningInfoApi:()=>h});var r=s(14068);const n=e=>r.h.get({url:`/interface/report/sys/host-sys-get-atk/${e.task_id}/${e.scan_vuls_id}`}),o=e=>r.h.post({url:"/interface/report/sys/host-sys-do-atk",data:e}),a=e=>r.h.post({url:"/interface/report/sys/host-correct-vul/",data:e},{isReturnNativeResponse:!0}),i=e=>r.h.post({url:"/interface/report/image-scan/image-correct-vul/",data:e},{isReturnNativeResponse:!0}),c=e=>r.h.post({url:"/interface/report/sys/host-sys-atk-batch",data:e}),p=e=>r.h.get({url:`/interface/report/sys/host-sys-atk-warning/${e.expType}`},{isReturnNativeResponse:!0}),u=e=>r.h.get({url:`/interface/report/sys/host-sys-atk-process/${e.task_id}/${e.task_ip_id}?first=${e.first||"no"}`}),l=e=>r.h.post({url:"/interface/report/web-scan/site-correct-vul/",data:e}),h=e=>r.h.get({url:`/interface/report/web-scan/get_warning_info/${e.expType}`}),f=e=>r.h.post({url:"/interface/report/web-scan/vul_confirm_batch_do",data:e}),d=e=>r.h.get({url:`/interface/report/web-scan/vul_confirm/${e.task_id}/${e.web_scan_vuls_id}`}),g=e=>r.h.post({url:"/interface/report/web-scan/vul_confirm_do",data:e}),m=e=>r.h.get({url:`/interface/report/web-scan/vul_confirm_progress/${e.task_id}/${e.site_id}`}),_=e=>r.h.get({url:`/interface/report/web-scan/vul_confirm_result/${e.scan_vuls_id}`}),k=e=>r.h.post({url:"/interface/report/sys/host-correct-config/",data:e},{isReturnNativeResponse:!0}),v=e=>r.h.post({url:"/interface/report/image-scan/image-correct-config/",data:e},{isReturnNativeResponse:!0})},14068:(e,t,s)=>{s.d(t,{h:()=>v});var r=s(61446),n=s(52861),o=s(5255),a=s(22560),i=s(18036);const c=(0,i.H)(),p=(e="")=>e;var u=s(1869),l=s(22702),h=s(947);const f=h.o9.LOCAL;function d(e,t){return(f?l.G.setLocal:l.G.setSession)(e,t,!0)}var g=s(31641);const m=(void 0).VITE_BASE_URL,_=(0,i.H)(),k={transformResponseHook(e,t){const{isReturnNativeResponse:s,isTransformResponse:n}=t;if(_.setPageLoading(!1),s)return e;if(!n)return e.data;const{data:o}=e;o||console.log("出错");const{code:a,message:i}=o;if(a&&200==a)return{...o};let c=i||"";switch(a){case 401:return d(h.B1,""),d(h.Ee,null),l.G.removeSession(h.OT),window.location.reload(),{...o};case 400:case 500:break;default:i&&(c=i)}c&&r.ZP.warning(c)},transformResponseErroHook(e){if(_.setPageLoading(!1),"canceled"!=e.message)return{code:501,data:{message:"系统服务异常，请联系管理员"}}},requestInterceptors(e){const t=(0,u.q)(),s=(r=h.B1,(f?l.G.getLocal:l.G.getSession)(r));var r;if(t.getLocale){const s=g.l.filter((e=>e.event==t.getLocale));e.headers["Accept-Language"]=s[0].serviceKey}return s&&(e.headers.token=s),e}},v=(_.setPageLoading(!0),new class{constructor(e){this.options=e,this.axiosInstance=n.Z.create(e),this.setupInterceptors()}createAxios(e){this.axiosInstance=n.Z.create(e)}getTransform(){const{transform:e}=this.options;return e}getAxios(){return this.axiosInstance}configAxios(e){this.axiosInstance&&this.createAxios(e)}setupInterceptors(){const e=this.getTransform();if(!e)return;const{requestInterceptors:t,responseInterceptorsCatch:s,responseInterceptors:r}=e||{};this.axiosInstance.interceptors.request.use((e=>(t&&(e=t(e,this.options)),e.cancelToken=new n.Z.CancelToken((t=>{c.setCancelFnByUrl(e.url,t)})),e))),this.axiosInstance.interceptors.response.use((e=>(r&&(0,a.mf)(r)&&(e=r(e)),e)),void 0),s&&(0,a.mf)(s)&&this.axiosInstance.interceptors.response.use(void 0,(e=>s(this.axiosInstance,e)))}get(e,t){const s=p(e.url);return this.request({...e,method:"GET",url:s},t||{})}post(e,t){const s=p(e.url);return this.request({...e,method:"POST",url:s},t||{})}put(e,t){const s=p(e.url);return this.request({...e,method:"PUT",url:s},t||{})}delete(e,t){const s=p(e.url);return this.request({...e,method:"DELETE",url:s},t||{})}uploadFile(e,t){const s=p(e.url),r=new window.FormData,n=t.name||"file";return t.filename?r.append(n,t.file,t.filename):r.append(n,t.file),t.data&&Object.keys(t.data).forEach((e=>{const s=t.data[e];Array.isArray(s)?s.forEach((t=>{r.append(`${e}[]`,t)})):r.append(e,t.data[e])})),this.axiosInstance.request({...e,url:s,method:"POST",data:r,headers:{"Content-type":"multipart/form-data;charset=UTF-8",ignoreCancelToken:!0}})}request(e,t){const s=(0,o.p$)(e),{requestOptions:r}=this.options,{transform:n}=this.options,{transformResponseHook:a,transformResponseErroHook:i}=n||{},c=Object.assign({},r,t);return s.requestOptions=c,new Promise(((e,t)=>{this.axiosInstance.request(s).then((s=>{if(a)try{const t=a(s,c);e(t)}catch(e){t(e)}e(s)})).catch((s=>{if(i)try{const t=i(s);e(t)}catch(s){t(s)}t(s)}))}))}}((0,o.RH)({transform:(0,o.p$)(k),baseURL:m,headers:{"Content-Type":"application/json;charset=UTF-8"},requestOptions:{isReturnNativeResponse:!1,isTransformResponse:!0}},undefined)))}}]);