package com.zy.dam;

import com.zy.dam.feign.AmapGisFeign;
import com.zy.model.vo.AmapGisResult;
import com.zy.util.JsonUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
class DamPortApplicationTests {


    @Resource
    private AmapGisFeign amapGisFeign;

    @Value("${amap.key}")
    private String amapKey;

    @Test
    void contextLoads() {
//        String creditCodes = "91460000MA7GWD5B60,91460000MA7H8MEK1D,91460000MA7GGJ5Y3H,91460000MA7J05C072,91460000MA7HPTDT04,91460000MA7GX4BX58,91460000MA7H8QU861,91469028MA7GY11F1Q,91460000MA7J10PA32,91460000MA7J11UH5D,91460000MA7HA39PX9,91460000MA7H1H2BXL,91460000MA7FLL232C,91460107MAA99W5NXN,91460000MA7FLLUN44,91460000MA7GK3MYXP,91460107MAA99W683D,91460000MA7HM5B84B,91460000MA7HM7K95B,91460000MA7H42BF5C,91460106MAA99W771D,91460000MA7H42W49X,91460000MA7FP10534,91460000MA7FP4WC2W,91460000MA7FP5QA05,91460000MA7HX7987M,91460108MAA99W878M,91460000MA7HE3U19R,91460000MA7FR51435,91460000MA7H50RT14,91460000MA7GLX0H8M,91460000MA7FR52M7Q,91460000MA7GLX1B5U,91460000MA7HNUYY7X,91460000MA7HE8GB5D,91460000MA7HNW0M8L,91460000MA7HNW0N6F,91460000MA7GLYJX2W,91460000MA7HE95WX2,91460100MA7HEAAN0T,91460000MA7HECXG59,91460000MA7HP49L1B,91460000MA7GMQX99Q,91460000MA7FU70X6G,91469007MA7HETMU64,91460000MA7HPDWN93,91460000MA7HFEL25X,91460000MA7FUCWX3A,91460000MA7FUDMC0Y,91460000MA7FUDQ75A,91460000MA7HPK751W,91460000MA7HFGAUXP,91460000MA7GNKFJ5R,91460000MA7HPKAT76,91460000MA7FUE2M8C,91460000MA7HGUQ348,91460000MA7J9XXT34,91460000MA7HGUQU54,91460000MA7GP8FAX4,91460000MA7HGUR73Y,91460000MA7GP8FJ3U,91460000MA7GP8G55A,91460000MA7FUK4L9X,91460000MA7GQFKUXN,91460000MA7GQFY2XB,91460000MA7GQG8HX5,91460000MA7GQGC063,91460106MA7FY8FM6P,91460000MA7JAHKT29,91460105MAA99WCJ1N,91460107MA7FYJD702,91460000MA7HWGXG4F,91460000MA7H913Q94,91460000MA7GTTDD0B,91460000MA7HWTDH0H,91460000MA7GU5PJ6U,91460000MA7GUC852F,91460000MA7H9A2X45,91460000MA7HXJ7D82,91460000MA7G4GLY15,91460000MA7J7WBH1P,91460000MA7J7WC17F,91460000MA7GXRXLX7,91460000MA7GYC504N,91460000MA7GYC504N,91460000MA7HC52H62,91460107MA7GYH3L7P,91460107MA7GYH3L7P,91460000MA7HFMJG90,91460000MA7HFMK473,91460000MA7H2N8J5F,91460000MA7G8HLR9Y,91460100MA7JBGU01E,91460000MA7H2PKL31,91460000MA7G8NN902,91460100MA7HG01RXU,91460000MA7HG1MF84,91460000MA7HG3P938,91460000MA7G9JJB6T,91460000MA7JC8F85P,91460000MA7GBJXW8G,91460000MA7GBK412B,91460000MA7H4G9Q00,91460000MA7HGHFT1B,91460000MA7JC9R76R,91460108MAA99WHD4J,91460000MA7H4GFKXN,91460000MA7JC9UP29,91460000MA7GBKDJ7F,91460000MA7GBKEH74,91460000MA7HXKY736,91460000MA7J5T046D,91460000MA7H5WJC91,91460000MA7H5WJJ6X,91460000MA7HXL248T,91460000MA7HXL2B5Q,91460000MA7J5T2E0G,91460000MA7GBKJJ5M,91460000MA7HXL2M53,91460000MA7H5WK074,91460000MA7H5WK15Y,91460000MA7J5T2KXF,91460000MA7HXL2T2Y,91460000MA7H5WK667,91460000MA7J5T2Y4D,91460000MA7GBKKKXN,91460000MA7J5T4N7R,91460000MA7J5T6L3N,91460000MA7H5WRK5K,91460000MA7J5TFC7X,91460000MA7HXLH51B,91460000MA7H5X328M,91460000MA7HXRAM2B,91460000MA7HXRB091,91460000MA7HXRBD52,91460000MA7HXRBM9N,91460000MA7HXRNA7K,91460000MA7GCP5856,91460000MA7HXXLK2Y,91460000MA7GCP681F,91460000MA7J67X84E,91460000MA7H62GT2A,91460000MA7GCP710T,91460000MA7H62HA3J,91460000MA7HXXN87E,91460000MA7HXXNE6H,91469036MA7H62WD3Q,91460107MAA99WHR90,91460400MA7HYACF70,91460000MA7H69UH22,91469001MA7GE1L66T,91460000MA7H6F8XXH,91460000MA7GE2G168,91460000MA7H6FX33D,91460000MA7H6FXRXL,91460000MA7H73WD1W,91460000MA7J01DX29,91460000MA7GFCF47Q,91460000MA7J035H6A,91460000MA7J037XX9,91460000MA7J76925A,91460000MA7J03B64T,91460000MA7J76C916,91460000MA7GFD7U1P,91460000MA7H79TL4D,91460000MA7J76G97F,91460000MA7JBDG8X4,91460000MA7GFDC671,91460000MA7JBDHJ6W,91460000MA7JBDHY9J,91460000MA7H89YQ2R,91460000MA7JJXC23G,91460000MA7JBDK0XW,91460000MA7JBDK508,91460000MA7H8A0F93,91460000MA7JBDKC88,91460000MA7GFDF4X4,91460000MA7JBDKL1T,91460000MA7JJXCY5K,91460000MA7GFDFC52,91460000MA7JBDKT7K,91460000MA7JJXE0XA,91460000MA7JBDYP3J,91460000MA7H8BQL6E,91460000MA7JK2UD98,91460000MA7H9ARY1F,91460000MA7H9CDL1F,91460000MA7GGFQR95,91460000MA7GGL4P9M,91460106MAA99WLB3H,91460000MA7GJ6JX59,91460108MAA99WM857,93460000MA7JKT7Q2K,91460000MA7JF6MPXC,91460108MAA99WN570,91460000MA7HDH5Q7U,91460000MA7HDH8540,91460000MA7J4JBW2N,91460000MA7J4KFM93,91460000MA7HDM84X9,91460000MA7GL82B3K,91460000MA7HDPLH75,91460000MA7JFMMM9Q,91460000MA7J4W989L,91460106MA7JFTQB3W,91460105MAA99WPE37,91460000MA7JFX8P4B,91460000MA7GLK4RXD,91460000MA7J53GC2X,91460000MA7JKWRA7F,91460108MAA99WQ99M,91460000MA7HJN3B08,91460000MA7HJNH854,91460106MAA99WQU0F,91460000MA7JLBHBX4,91460000MA7GNYY173,91469036MA7GP00P5W,91460000MA7J9J8A0K,91469001MA7J9JUX9W,91460000MA7J9LP96Q,91460000MA7GP65E3G,91460000MA7GP78A2H,91460000MA7J9QXJ2L,91460000MA7HK6YY5Q,91460000MA7GQUTR3J,91460000MA7HK7AG89,91460000MA7J9T4B0P,91460000MA7HK7L58N,91460000MA7J9T5G8B,91460000MA7GQXPK9C,91460000MA7J1BN33L,91460000MA7JE65B1N,91460000MA7J1BN92P,91460000MA7GQXQCXQ,91460000MA7JE65Q4D,91460000MA7K675X15,91460000MA7GR0Y08K,91460000MA7JEE7U1C,91460000MA7GRJF5XN,91460000MA7JEGPB6L,91460000MA7JEJNJ7P,91460000MA7GRPDE7L,91460000MA7K6LRD6K,91460105MAA99WWF6X,91460000MA7K6TXT76,91460000MA7K6XC311,91460106MA7GW50H1C,91460000MA7J257X60,91460000MA7J25D556,91460000MA7J25KM95,91460000MA7JFYF90F,91460000MA7K72MJ7T,91460000MA7J27PF94,91460108MAA99X053X,91460000MA7JG24W7P,91460000MA7K7412XB,91460000MA7GWGW73T,91460000MA7GWH3T1H,91460000MA7K74TF49,91460000MA7GWHAX95,91460000MA7GWHD83X,91460108MAA99X176Y,91460000MA7GWHEUXP,91460000MA7JG39T79,91460000MA7J29QY6E,91460000MA7K75B857,91460000MA7JG3GB48,91460000MA7JG3H006,91460000MA7HN3TT00,91460000MA7J389F57,91460000MA7JJ07C3W,91460000MA7JJ07K9N,91460000MA7GWHQX0Q,91460000MA7J38A415,91460000MA7GWHRA9N,91460000MA7HN3WG3A,91460000MA7J38AP3Y,91460000MA7JJ08E6X,91460000MA7GWHRK04,91460000MA7JJ08M1M,91460000MA7GWHRW99,91460000MA7JJ08Q42,91460000MA7GWHT698,91460000MA7J3B2T3A,91460000MA7GYKY6XT,91460000MA7H1PNT2D,91460106MAA99X5JXB,91460000MA7H4JDX1D,91460000MA7H4K2N22,91460000MA7JW7828P,91469027MA7H4L856R,91460000MA7H4LH61T,91460000MA7H4M373R,91469027MA7JW8AB74";
//        TianyanchaResult result = feign.getEnterpriseByKeyword("海南子元科技有限公司");
//        if (result != null) {
//            TianyanchaData tianyanchaData = result.getResult();
//            System.out.println(JsonUtils.toString(tianyanchaData));
//            Map<String, String> industryAll = tianyanchaData.getIndustryAll();
//            System.out.println(industryAll.get("categoryMiddle"));
//            System.out.println(industryAll.get("categoryBig"));
//            System.out.println(industryAll.get("category"));
//            System.out.println(industryAll.get("categorySmall"));
//        }
//        radius=1000&extensions=all&batch=false&roadlevel=0
//        String key = "b4ae4625e7f217801a29af656cbf8ce7";
        String location = "110.267416,20.014027";
        int radius = 1000;
        String extensions = "all";
        boolean batch = false;
        int roadlevel = 0;
        AmapGisResult result = amapGisFeign.getAddressByLocation(amapKey, location, radius, extensions, batch, roadlevel);
        System.out.println(JsonUtils.toString(result));
        System.out.println("========================" + result.getRegeocode().getFormatted_address());

//        Map<String,String> map = new LinkedHashMap<>();
//        for (String creditCode : creditCodes.split(",")) {
//            TianyanchaResult result = feign.getEnterpriseByKeyword(creditCode);
//            String tags = "-";
//            if (result != null) {
//                TianyanchaData tianyanchaData = result.getResult();
//                if (tianyanchaData != null) {
//                    tags = tianyanchaData.getTags();
//                }
//            }
//            map.put(creditCode, tags);
//            try {
//                Thread.sleep(1000L);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//        }
//        for(String key : map.keySet()) {
//
//            System.out.println(key + " , \t" + map.get(key));
//        }
    }

}
