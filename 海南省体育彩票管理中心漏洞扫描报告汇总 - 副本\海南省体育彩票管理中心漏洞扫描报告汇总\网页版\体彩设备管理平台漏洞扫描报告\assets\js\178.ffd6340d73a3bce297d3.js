"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[178],{97081:(e,a,l)=>{l.d(a,{J:()=>c});const n=(0,l(61641).Y)(),c=e=>n.getPromCode[e]},44560:(e,a,l)=>{l.d(a,{Z:()=>u});var n=l(8081),c=l.n(n),t=l(23645),d=l.n(t)()(c());d.push([e.id,".vulnDetailList > p[data-v-10609970] {\n  display: flex;\n  margin-bottom: 0;\n  padding: 16px 24px;\n  font-size: 14px;\n  border-radius: 4px;\n  line-height: 22px;\n}\n.vulnDetailList > p > span[data-v-10609970]:first-child {\n  min-width: 200px;\n  font-weight: 500;\n}\n.vulnDetailList > p > span[data-v-10609970]:first-child::after {\n  content: ':';\n  margin: 0 2px;\n}\n",""]);const u=d},85527:(e,a,l)=>{l.d(a,{Z:()=>u});var n=l(8081),c=l.n(n),t=l(23645),d=l.n(t)()(c());d.push([e.id,".extraFilter[data-v-01933a59] {\n  float: right;\n  clear: both;\n  overflow: hidden;\n  margin-bottom: 12px;\n  height: 23px;\n}\n",""]);const u=d},20178:(e,a,l)=>{l.r(a),l.d(a,{default:()=>C}),l(57658);var n=l(66252),c=l(3577),t=l(2262),d=(l(85827),l(64142)),u=l(39022),o=l(61641),f=l(79414);const i=(0,n.aZ)({__name:"WebVulnList",props:{source:{default:"online"},dataSource:{},loading:{type:Boolean},exportTypeTemplate:{},taskTypeNum:{}},setup(e){const a=e,{t:l}=f.i18n.global,i=(0,o.Y)(),r=[{title:l("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f0662cf8d858ec6d7749131be2a3b64ec7f1a971c581e3b16b9daeef6dae9929b"),dataIndex:"vuln_index",key:"vuln_index"},{title:l("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f0662cf8d858ec6d7749131be2a3b64ec94ae04532b800e32cd25c9e2616660a6"),dataIndex:"i18n_name",key:"i18n_name"},{title:l("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f0662cf8d858ec6d7749131be2a3b64ec32cfd656ab1e88758d73aa2f346fd0f44344100a8daecad8eb811c7634baef9e"),dataIndex:"pages_count",key:"pages_count"},{title:l("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f0662cf8d858ec6d7749131be2a3b64ec09be29c41a0a9e9fe0beaf13c58524b2"),dataIndex:"found_count",key:"found_count"}],s=(0,n.Fl)((()=>a.dataSource.reduce(((e,a)=>e+a.pages_count),0))),b=(0,n.Fl)((()=>a.dataSource.reduce(((e,a)=>e+a.found_count),0)));return(e,o)=>{const f=(0,n.up)("a-table-summary-cell"),v=(0,n.up)("a-table-summary-row"),p=(0,n.up)("a-table");return(0,n.wg)(),(0,n.j4)(p,{columns:r,dataSource:a.dataSource,loading:a.loading,rowKey:"vul_id",pagination:!1,defaultExpandAllRows:"offline"===e.source&&a.exportTypeTemplate&&"html"!=a.exportTypeTemplate},{bodyCell:(0,n.w5)((({column:e,record:a,index:d})=>{var o;return["vuln_index"==e.key?((0,n.wg)(),(0,n.iD)(n.HY,{key:0},[(0,n.Uk)((0,c.zw)(d+1),1)],64)):(0,n.kq)("v-if",!0),"i18n_name"==e.key?((0,n.wg)(),(0,n.iD)(n.HY,{key:1},[(0,n.Wm)(u.Z,{level:a.level_name,isStandard:""},null,8,["level"]),(0,n.Uk)(" "+(0,c.zw)(a.i18n_name)+(0,c.zw)(a.vul_ifconfirm&&1==(null===(o=(0,t.SU)(i).getPromCode)||void 0===o?void 0:o.vuln_verify)?(0,t.SU)(l)("onlineReport.verifiable"):""),1)],64)):(0,n.kq)("v-if",!0)]})),expandedRowRender:(0,n.w5)((({record:e})=>[(0,n.Wm)(d.Z,{paramsData:e,source:a.source,taskTypeNum:a.taskTypeNum},null,8,["paramsData","source","taskTypeNum"])])),summary:(0,n.w5)((()=>[(0,n.Wm)(v,null,{default:(0,n.w5)((()=>[(0,n.Wm)(f,{"col-span":3},{default:(0,n.w5)((()=>[(0,n.Uk)((0,c.zw)(e.$t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f0662cf8d858ec6d7749131be2a3b64ec9747f3c9e7b004288bfcc20713437c9f")),1)])),_:1}),(0,n.Wm)(f,null,{default:(0,n.w5)((()=>[(0,n.Uk)((0,c.zw)(s.value),1)])),_:1}),(0,n.Wm)(f,null,{default:(0,n.w5)((()=>[(0,n.Uk)((0,c.zw)(b.value),1)])),_:1})])),_:1})])),_:1},8,["dataSource","loading","defaultExpandAllRows"])}}});var r=l(93379),s=l.n(r),b=l(7795),v=l.n(b),p=l(90569),m=l.n(p),_=l(3565),w=l.n(_),g=l(19216),y=l.n(g),h=l(44589),k=l.n(h),x=l(44560),T={};T.styleTagTransform=k(),T.setAttributes=w(),T.insert=m().bind(null,"head"),T.domAPI=v(),T.insertStyleElement=y(),s()(x.Z,T),x.Z&&x.Z.locals&&x.Z.locals;var S=l(83744);const U=(0,S.Z)(i,[["__scopeId","data-v-10609970"]]);var Z=l(61446),D=l(97081);const z={class:"extraFilter"},W={class:"bolder_title"},$={key:0,class:"bolder_title"},H={key:1},I=(0,n.aZ)({__name:"WebVulnList",props:{paramsData:{},source:{},taskTypeNum:{},exportTypeTemplate:{}},setup(e){const a=e,{t:l}=f.i18n.global,d=(0,t.iH)([]),o=(0,t.iH)([]),i=(0,t.iH)({vul_confirmed_count:0,vul_ifconfirm_count:0,high:0,low:0,middle:0}),{webvulns_level_count:r,vul_ifconfirm_count:s,vul_confirmed_count:b}=a.paramsData.vulns_info.webvulns_info_list;d.value=a.paramsData.vulns_info.webvulns_info_list.webvuln_info_list.map((e=>({...e,...e.web_vuln_obj}))),o.value=d.value,i.value={...r,vul_ifconfirm_count:s,vul_confirmed_count:b};const v=(0,t.iH)(["high","middle","low"]),p=(0,t.iH)([]);function m(e,a){return a}const _=(0,n.Fl)((()=>{let e=[];return(0,t.SU)(i).high>0&&e.push({label:m(0,`${l("a68595c99d93674665c126f168481901c9d7e8837a8ec3a078b0650f268131b0102979800222f582e63f7c73474fbc561954a22ba35ba1b730c7ed515e177033")}${(0,t.SU)(i).high})`),value:"high"}),(0,t.SU)(i).middle>0&&e.push({label:m(0,`${l("a68595c99d93674665c126f168481901c9d7e8837a8ec3a078b0650f268131b0102979800222f582e63f7c73474fbc56e7a5bc5486fd188c592b53f7f1354230")}${(0,t.SU)(i).middle})`),value:"middle"}),(0,t.SU)(i).low>0&&e.push({label:m(0,`${l("a68595c99d93674665c126f168481901c9d7e8837a8ec3a078b0650f268131b0102979800222f582e63f7c73474fbc5605c5d367172692f52d3f13d734bd12da")}${(0,t.SU)(i).low})`),value:"low"}),e}));function w(){if(console.log((0,t.SU)(d),v.value),0===v.value.length)return Z.ZP.warning(l("a68595c99d93674665c126f168481901c9d7e8837a8ec3a078b0650f268131b0102979800222f582e63f7c73474fbc561c1edebe298cb0feff9adbaec39b9efee719f11184c1e7364823bda9da1c212a1c87afd099563530ba0f7da4efaad65c")),void(v.value=p.value);p.value=v.value,o.value=(0,t.SU)(d).filter((e=>v.value.includes(e.level_name))),console.log(" curDataSource.value ",o.value)}return(e,l)=>{const d=(0,n.up)("a-checkbox"),f=(0,n.up)("a-checkbox-group");return(0,n.wg)(),(0,n.iD)(n.HY,null,[(0,n._)("div",z,[(0,n._)("span",W,(0,c.zw)(e.$t("a68595c99d93674665c126f168481901c9d7e8837a8ec3a078b0650f268131b0102979800222f582e63f7c73474fbc5686cde217784aabbf25e488be2beceee5c400d00afa65c5b3abfee84d45729bf8")),1),(0,n.Wm)(f,{onChange:w,value:v.value,"onUpdate:value":l[0]||(l[0]=e=>v.value=e),name:"checkboxgroup"},{default:(0,n.w5)((()=>[((0,n.wg)(!0),(0,n.iD)(n.HY,null,(0,n.Ko)(_.value,(e=>((0,n.wg)(),(0,n.j4)(d,{value:e.value,key:e.label},{default:(0,n.w5)((()=>[(0,n.Wm)(u.Z,{level:e.value},null,8,["level"]),(0,n.Uk)(" "+(0,c.zw)(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"]),1==(0,t.SU)(D.J)("vuln_verify")?((0,n.wg)(),(0,n.iD)("span",$,(0,c.zw)(e.$t("a68595c99d93674665c126f168481901c9d7e8837a8ec3a078b0650f268131b0102979800222f582e63f7c73474fbc56c2aaf67e95dd1e38ad0cb3ab81b691c4767a29c468ac756e6bf3279066d11ff3")),1)):(0,n.kq)("v-if",!0),1==(0,t.SU)(D.J)("vuln_verify")?((0,n.wg)(),(0,n.iD)("span",H,(0,c.zw)(`[${i.value.vul_confirmed_count}/${i.value.vul_ifconfirm_count}]`),1)):(0,n.kq)("v-if",!0)]),(0,n.Wm)(U,{exportTypeTemplate:e.exportTypeTemplate,source:a.source,dataSource:o.value,taskTypeNum:a.taskTypeNum},null,8,["exportTypeTemplate","source","dataSource","taskTypeNum"])],64)}}});var N=l(85527),A={};A.styleTagTransform=k(),A.setAttributes=w(),A.insert=m().bind(null,"head"),A.domAPI=v(),A.insertStyleElement=y(),s()(N.Z,A),N.Z&&N.Z.locals&&N.Z.locals;const C=(0,S.Z)(I,[["__scopeId","data-v-01933a59"]])}}]);