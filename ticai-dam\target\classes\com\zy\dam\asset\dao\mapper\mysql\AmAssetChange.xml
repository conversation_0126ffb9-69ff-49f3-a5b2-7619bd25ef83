<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.asset.dao.AmAssetChangeDAO">

    <sql id="meta">
			a.ID_
			,a.NO_
			,a.USER_
			,a.TIME_
			,a.MEMO_
			,a.SRC_
			,a.DEST_
			,a.STATUS_
			,a.CDEPT_
			,a.CUSER_
			,a.CTIME_
			,a.CHECK_USER_
			,a.CHECK_TIME_
			,a.CHECK_MEMO_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.asset.orm.AmAssetChange">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into
        AM_ASSET_CHANGE(ID_,NO_,USER_,TIME_,MEMO_,SRC_,DEST_,STATUS_,CDEPT_,CUSER_,CTIME_,CHECK_USER_,CHECK_TIME_,CHECK_MEMO_)
        values(#{id},#{no},#{user},#{time},#{memo},#{src},#{dest},#{status},#{cdept},#{cuser},now(),#{checkUser},#{checkTime},#{checkMemo})
    </insert>

    <update id="updateCheck" parameterType="com.zy.dam.asset.orm.AmAssetChange">
		update AM_ASSET_CHANGE
		set STATUS_=#{status},CHECK_USER_=#{checkUser},CHECK_TIME_=#{checkTime},CHECK_MEMO_=#{checkMemo}
		where ID_=#{id}
	</update>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.asset.vo.AssetChangeVo">
        select
        <include refid="meta"/>
        ,b.NAME_ user_name
        ,c.NO_ src_no,c.NAME_ src_name
        ,d.NO_ dest_no, d.NAME_ dest_name
        from AM_ASSET_CHANGE a left join SYS_USER b on a.USER_=b.ID_, AM_ASSET c,AM_ASSET d
        where a.SRC_=c.ID_ and a.DEST_=d.ID_
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.MEMO_ like
            concat('%',#{keyword},'%'))
        </if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="user != null">and a.USER_=#{user}</if>
        <if test="userKey != null">and (b.NAME_ like concat('%',#{userKey},'%') or b.NO_ like
            concat('%',#{userKey},'%'))
        </if>
        <if test="begin != null">and a.TIME_ &gt;= #{begin}</if>
        <if test="end != null">and a.TIME_ &lt; date_add(#{end}, interval 1 day)</if>
        <if test="deptIds != null">
            <foreach collection="deptIds" item="deptId" open="and a.CDEPT_ in (" separator="," close=")">#{deptId}
            </foreach>
        </if>
        order by a.CTIME_ desc
    </select>

    <!-- 获取唯一的资管-资产变更申请数据 -->
    <select id="findOne" resultType="com.zy.dam.asset.orm.AmAssetChange">
        select
        <include refid="meta"/>
        from AM_ASSET_CHANGE a where a.ID_=#{0}
    </select>

    <select id="findVo" resultType="com.zy.dam.asset.vo.AssetChangeVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        ,c.NO_ src_no,c.NAME_ src_name
        ,d.NO_ dest_no, d.NAME_ dest_name
        from AM_ASSET_CHANGE a, AM_ASSET c,AM_ASSET d
        where a.SRC_=c.ID_ and a.DEST_=d.ID_ and a.ID_=#{0}
    </select>

    <update id="updateForChange">
        update AM_ASSET a,AM_ASSET b set
        b.REGION_=a.REGION_,b.LOCATION_=a.LOCATION_,b.USE_DEPT_=a.USE_DEPT_,b.USE_USER_=a.USE_USER_,b.STATUS_='2',
        b.LNG_=a.LNG_,b.LAT_=a.LAT_,b.LOC_ADDR_=a.LOC_ADDR_,b.LOC_TIME_=a.LOC_TIME_,b.SN_=a.SN_,
        a.REGION_=NULL,a.LOCATION_=NULL,a.USE_DEPT_=NULL,a.USE_USER_=NULL,a.LOC_ADDR_=null,a.SN_=null,a.LNG_=null,a.LAT_=null,a.STATUS_='1'
        where a.ID_=#{0} and b.ID_=#{1}
    </update>

    <select id="findExportByIds" resultType="com.zy.dam.report.vo.AssetChangeExportVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        ,c.NO_ src_no,c.NAME_ src_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=c.TYPE_) src_type
        ,c.SPEC_ src_spec
        ,d.NO_ dest_no, d.NAME_ dest_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=d.TYPE_) dest_type
        ,d.SPEC_ dest_spec
        from AM_ASSET_CHANGE a, AM_ASSET c,AM_ASSET d
        where a.SRC_=c.ID_ and a.DEST_=d.ID_ and a.ID_ in (
        <foreach collection="ids" item="id" separator=",">#{id}</foreach>
        )
        order by a.NO_ desc
    </select>

    <select id="findExportByQuery" resultType="com.zy.dam.report.vo.AssetChangeExportVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        ,c.NO_ src_no,c.NAME_ src_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=c.TYPE_) src_type
        ,c.SPEC_ src_spec
        ,d.NO_ dest_no, d.NAME_ dest_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=d.TYPE_) dest_type
        ,d.SPEC_ dest_spec
        from AM_ASSET_CHANGE a left join SYS_USER b on a.USER_=b.ID_, AM_ASSET c,AM_ASSET d
        where a.SRC_=c.ID_ and a.DEST_=d.ID_
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.MEMO_ like
            concat('%',#{keyword},'%'))
        </if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="user != null">and a.USER_=#{user}</if>
        <if test="userKey != null">and (b.NAME_ like concat('%',#{userKey},'%') or b.NO_ like
            concat('%',#{userKey},'%'))
        </if>
        <if test="begin != null">and a.TIME_ &gt;= #{begin}</if>
        <if test="end != null">and a.TIME_ &lt; date_add(#{end}, interval 1 day)</if>
        <if test="deptIds != null">
            <foreach collection="deptIds" item="deptId" open="and a.CDEPT_ in (" separator="," close=")">#{deptId}
            </foreach>
        </if>
        order by a.NO_ desc
    </select>
</mapper>
