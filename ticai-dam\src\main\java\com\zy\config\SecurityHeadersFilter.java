package com.zy.config;

import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 安全响应头过滤器
 * 用于隐藏服务器版本信息和添加安全响应头
 */
@Component
public class SecurityHeadersFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 初始化方法
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 移除的响应头
        httpResponse.setHeader("Server", "");
        httpResponse.setHeader("X-Powered-By", "");
        
        // 添加安全响应头
        httpResponse.setHeader("X-Content-Type-Options", "nosniff");
        httpResponse.setHeader("X-Frame-Options", "DENY");
        httpResponse.setHeader("X-XSS-Protection", "1; mode=block");
        httpResponse.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
        httpResponse.setHeader("Content-Security-Policy",
                "default-src 'self'; " +
                        "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                        "style-src 'self' 'unsafe-inline'; " +
                        "img-src 'self' data: https:; " +
                        "font-src 'self' data:; " +
                        "connect-src 'self'; " +
                        "frame-ancestors 'none'; " +
                        "base-uri 'self'; " +
                        "form-action 'self'");
        httpResponse.setHeader("X-Permitted-Cross-Domain-Policies", "none");
        httpResponse.setHeader("X-Download-Options", "noopen");
        httpResponse.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");

        // 添加缺失的关键安全响应头
        // 限制特定功能的使用范围
        httpResponse.setHeader("Permissions-Policy",
                "geolocation=(), " +
                "microphone=(), " +
                "camera=(), " +
                "payment=(), " +
                "usb=(), " +
                "magnetometer=(), " +
                "gyroscope=(), " +
                "speaker=(), " +
                "vibrate=(), " +
                "fullscreen=(self), " +
                "sync-xhr=()");

        // 增强跨源资源加载安全性
        httpResponse.setHeader("Cross-Origin-Embedder-Policy", "require-corp");

        // 防止跨源窗口劫持
        httpResponse.setHeader("Cross-Origin-Opener-Policy", "same-origin");

        // 限制跨源资源访问
        httpResponse.setHeader("Cross-Origin-Resource-Policy", "same-origin");

        // 明确允许的跨域请求来源
        String origin = httpRequest.getHeader("Origin");
        if (origin != null ) {
            httpResponse.setHeader("Access-Control-Allow-Origin", origin);
        }

        // 在登出时清除浏览器存储的数据
        String requestURI = httpRequest.getRequestURI();
        if (requestURI != null && (requestURI.contains("/logout") || requestURI.endsWith("/logout"))) {
            httpResponse.setHeader("Clear-Site-Data", "\"cache\", \"cookies\", \"storage\"");
        }

        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {
        // 销毁方法
    }
}
