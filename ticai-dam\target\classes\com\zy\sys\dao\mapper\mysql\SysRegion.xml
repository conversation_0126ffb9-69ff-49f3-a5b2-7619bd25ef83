<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.sys.dao.SysRegionDAO">

    <sql id="meta">
			a.CODE_
			,a.PCODE_
			,a.NAME_
			,a.REGION_NAME_
			,a.FULL_NAME_
			,a.LAT_
			,a.LNG_
			,a.ORD_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.sys.orm.SysRegion">
		insert into SYS_REGION(CODE_,PCODE_,NAME_,REGION_NAME_,FULL_NAME_,LAT_,LNG_,ORD_)
		values(#{code},#{pcode},#{name},#{regionName},#{fullName},#{lat},#{lng},#{ord})
	</insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.sys.orm.SysRegion">
		update SYS_REGION
		set NAME_=#{name},REGION_NAME_=#{regionName},FULL_NAME_=#{fullName},LAT_=#{lat},LNG_=#{lng},ORD_=#{ord}
		where CODE_=#{code}
	</update>

    <select id="countCode" resultType="int">
		select count(0) from SYS_REGION where CODE_=#{0}
	</select>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.sys.orm.SysRegion">
        select
        <include refid="meta"/>
        from SYS_REGION a
        <where>
            <if test="keyword != null">and (a.CODE_=#{keyword} or a.NAME_ like concat('%',#{keyword},'%'))</if>
        </where>
        order by PCODE_,a.ORD_ desc
    </select>

    <!-- 删除 -->
    <delete id="delete">
		delete from SYS_REGION where CODE_=#{0}
	</delete>
</mapper>
