"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[9079],{39079:(e,a,d)=>{d.r(a),d.d(a,{default:()=>p});var t=d(66252),c=d(79414);const p=(0,t.aZ)({__name:"AppInfo",props:{paramsData:{}},setup(e){const a=e,{t:d}=c.i18n.global,p=[{title:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fa7b4722e55e7ed323efe10528103809e144cfb4bf7298516496f419e5c204b324583e18cc63b311806e738c6218bc9d1"),dataIndex:"app",key:"app"},{title:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fa7b4722e55e7ed323efe10528103809e8b7e81f63ddcde1871b6e1a1b724022d"),dataIndex:"appVersion",key:"appVersion"},{title:"CPE",dataIndex:"cpe",key:"cpe"},{title:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fa7b4722e55e7ed323efe10528103809ead481803dbf60557d75eb5042632ecbc"),dataIndex:"port",key:"port"}],n=(0,t.Fl)((()=>{var e;return null===(e=a.paramsData)||void 0===e?void 0:e.appList}));return(e,a)=>{const d=(0,t.up)("a-table");return(0,t.wg)(),(0,t.j4)(d,{dataSource:n.value,columns:p,pagination:!1},null,8,["dataSource"])}}})}}]);