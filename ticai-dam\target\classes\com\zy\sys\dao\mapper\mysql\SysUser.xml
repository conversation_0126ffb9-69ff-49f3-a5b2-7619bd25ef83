<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.sys.dao.SysUserDAO">

    <sql id="meta">
        a.ID_
        ,a.TYPE_
        ,a.DEPT_
        ,a.ACCOUNT_
        ,a.NO_
        ,a.NAME_
        ,a.GENDER_
        ,a.PHONE_
        ,a.STATUS_
        ,a.MP_
    </sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.sys.orm.SysUser">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into SYS_USER(ID_,TYPE_,DEPT_,ACCOUNT_,PASSWORD_,NO_,NAME_,GENDER_,PH<PERSON>E_,STATUS_,MP_,FLAG_)
        values(#{id},#{type},#{dept},#{account},#{password},#{no},#{name},#{gender},#{phone},'1','1','1')
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.sys.orm.SysUser">
        update SYS_USER set TYPE_=#{type},DEPT_=#{dept},NO_=#{no},NAME_=#{name},GENDER_=#{gender},PHONE_=#{phone}
        where ID_ = #{id}
    </update>

    <select id="countAccount" resultType="int">
        select count(0) from SYS_USER where ACCOUNT_=#{0}
    </select>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.sys.vo.UserVo">
        select
        <include refid="meta"/>
        ,(SELECT GROUP_CONCAT(n.NAME_ SEPARATOR ',') FROM SYS_USER_ROLE m,SYS_ROLE n WHERE m.ROLE_=n.ID_ AND m.USER_=a.ID_) roleNames
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        from SYS_USER a
        where a.FLAG_='1'
        <if test='dept != null'>and a.DEPT_=#{dept }</if>
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.ACCOUNT_ like
            concat('%',#{keyword},'%') or a.NAME_ like concat('%',#{keyword},'%'))
        </if>
        <if test="name != null">and a.NAME_ like concat('%',#{name},'%')</if>
        <if test='account != null'>and a.ACCOUNT_ like concat('%',#{account },'%')</if>
        <if test='no != null'>and a.NO_ like concat('%',#{no },'%')</if>
        order by a.ACCOUNT_
    </select>

    <!-- 获取唯一的系统用户数据 -->
    <select id="findOne" resultType="com.zy.sys.vo.UserVo">
        select
        <include refid="meta"/>
        ,(SELECT GROUP_CONCAT(n.NAME_ SEPARATOR ',') FROM SYS_USER_ROLE m,SYS_ROLE n WHERE m.ROLE_=n.ID_ AND
        m.USER_=a.ID_) roleNames
        from SYS_USER a where a.ID_=#{0}
    </select>

    <!-- 删除 -->
    <update id="delete">
        update SYS_USER set FLAG_='9',ACCOUNT0_=ACCOUNT_,ACCOUNT_=uuid()  where ID_=#{0}
    </update>

    <update id="resetPassword">
        update SYS_USER set MP_='1',PASSWORD_=#{1} where ID_=#{0}
    </update>


    <update id="updateStatus">
        update SYS_USER set STATUS_=#{1} where ID_=#{0}
    </update>

    <select id="countByDept" resultType="int">
        select count(0) from SYS_USER where FLAG_='1' and DEPT_=#{0}
    </select>

    <select id="findRoleIdByUser" resultType="String">
        select ROLE_ from SYS_USER_ROLE where USER_=#{0}
    </select>

    <select id="findUserRole" resultType="com.zy.model.ListCheck">
        select a.ID_, a.NAME_, IF(b.USER_ is not null, 1, 0) checked_
        from SYS_ROLE a
        left join SYS_USER_ROLE b on a.ID_=b.ROLE_ and USER_=#{0}
        order by a.ORD_
    </select>

    <insert id="insertUserRole">
        insert into SYS_USER_ROLE(USER_, ROLE_) values (#{0}, #{1})
    </insert>

    <delete id="deleteRoleByUser">
        delete from SYS_USER_ROLE where USER_=#{0}
    </delete>

    <select id="findByDept" resultType="com.zy.model.VueTreeNode">
        select a.ID_, DEPT_ pid, a.NAME_ label_
        from SYS_USER a
        where a.FLAG_='1' and a.DEPT_=#{0}
        order by a.ACCOUNT_
    </select>

    <select id="listBase" resultType="com.zy.sys.vo.UserBaseVo">
        select a.ID_,a.ACCOUNT_,a.NO_,a.NAME_ from SYS_USER a where a.FLAG_='1' order by a.NO_,a.ACCOUNT_,a.NAME_
    </select>

    <select id="listByType" resultType="com.zy.sys.vo.UserBaseVo">
        select a.ID_,a.ACCOUNT_,a.NO_,a.NAME_ from SYS_USER a where a.FLAG_='1' and a.TYPE_=#{0} order by a.NO_,a.ACCOUNT_,a.NAME_
    </select>

    <!-- 维保单位用户分页 -->
    <select id="getMaintenancePage" resultType="com.zy.sys.vo.UserVo">
        select
        <include refid="meta"/>
        ,(SELECT GROUP_CONCAT(n.NAME_ SEPARATOR ',') FROM SYS_USER_ROLE m,SYS_ROLE n WHERE m.ROLE_=n.ID_ AND
        m.USER_=a.ID_) roleNames
        from SYS_USER a
        where a.FLAG_='1' and a.type_='2'
        <if test='dept != null'>and a.DEPT_=#{dept }</if>
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.ACCOUNT_ like
            concat('%',#{keyword},'%') or a.NAME_ like concat('%',#{keyword},'%'))
        </if>
        <if test="name != null">and a.NAME_ like concat('%',#{name},'%')</if>
        <if test='account != null'>and a.ACCOUNT_ like concat('%',#{account },'%')</if>
        <if test='no != null'>and a.NO_ like concat('%',#{no },'%')</if>
        order by a.ACCOUNT_
    </select>

</mapper>
