<!doctype html><html lang="en"><head><meta charset="UTF-8"/><meta name="renderer" content="webkit"/><meta http-equiv="X-UA-Compatible" content="IE=10,chrome=1"/><meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=0"/><script>window.data = {"categories": [{"title": "\u7efc\u8ff0\u4fe1\u606f", "title_key": "\u7efc\u8ff0\u4fe1\u606f", "children": [{"title": "\u4efb\u52a1\u4fe1\u606f", "title_key": "\u4efb\u52a1\u4fe1\u606f", "mark": "task-info", "url": "/interface/report/web-scan/task-info/1029?site_list=&is_filtered=0", "data": {"point": 5.1, "risk": "middle", "taskName": "\u4f53\u5f69\u8bbe\u5907\u7ba1\u7406\u5e73\u53f0\u626b\u63cf", "target": "https://tcsb.hnvlts.com:10443/", "userName": "admin", "taskStatus": "\u626b\u63cf\u5b8c\u6210", "taskTypeName": "WEB\u5e94\u7528\u626b\u63cf", "taskDescription": "", "taskFrom": "\u672c\u5730\u626b\u63cf", "vulnTemplate": "\u81ea\u52a8\u5339\u914d\u626b\u63cf", "sysVersion": "V6.0R04F04SP07", "plg_version": "V6.0R02F00.3713", "timeStat": {"startTime": "2025-06-20 15:38:58", "endTime": "2025-06-20 16:30:39", "deltaTime": "51\u5206 41\u79d2 "}, "sites_count": 1, "high_risk_sites_count": 0, "websiteStat": {"scanned_page_num": 60, "vuln_page_num": 2, "scanned_url_num": 54, "found_url_num": 74}}}, {"title": "\u98ce\u9669\u5206\u5e03", "title_key": "\u98ce\u9669\u5206\u5e03", "mark": "risk-distr", "url": "/interface/report/web-scan/risk-distribution/1029?site_list=&is_filtered=0", "data": {"riskDistributionByPage": {"high": 0, "middle": 1, "low": 1, "safe": 58}, "riskDistributionByVuln": {"high": 0, "middle": 1, "low": 12}}}, {"title": "\u98ce\u9669\u5206\u7c7b\u7edf\u8ba1", "title_key": "\u98ce\u9669\u5206\u7c7b\u7edf\u8ba1", "mark": "risk-classcnt", "url": "/interface/report/web-scan/risk-category/1029?site_list=&is_filtered=0", "data": {"riskCategories": [{"name": "\u5a01\u80c1", "data": [{"title": "\u5ba2\u6237\u7aef\u653b\u51fb\u7c7b\u578b:\u8de8\u7ad9\u811a\u672c\u653b\u51fb", "id": 2000, "high": 0, "middle": 1, "low": 0}, {"title": "\u4fe1\u606f\u6cc4\u9732\u7c7b\u578b:\u4fe1\u606f\u6cc4\u9732", "id": 2014, "high": 0, "middle": 0, "low": 10}, {"title": "\u5ba2\u6237\u7aef\u653b\u51fb\u7c7b\u578b:\u5185\u5bb9\u6b3a\u9a97", "id": 2001, "high": 0, "middle": 0, "low": 1}, {"title": "\u5176\u4ed6", "id": 130, "high": 0, "middle": 0, "low": 1}]}], "highvulnTop10Urls": []}}]}, {"title": "\u7ad9\u70b9\u5217\u8868", "title_key": "\u7ad9\u70b9\u5217\u8868", "mark": "site-list", "url": "/interface/report/web-scan/site-list-risk-level/1029?isJumpToHost=1&site_list=&is_filtered=0", "data": {"sites_info": {"sites_info_list": {"sites_level_count": {"high": 0, "middle": 1, "low": 0, "safe": 0}, "site_info_list": [{"task_id": 1029, "task_site_id": 497, "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "scanned_num": 54, "time_delta": "51\u5206 31\u79d2 ", "vuln_low": 12, "vuln_middle": 1, "vuln_high": 0, "risk_value": 5.1, "risk_level": "middle", "can_confirm": 8, "have_confirm": 0, "not_confirm": 8, "_time_delta": "51\u5206 31\u79d2 ", "jumpTo": "./host/https___tcsb.hnvlts.com_10443_.html", "file_name": "https___tcsb.hnvlts.com_10443_.html"}], "error_site_list": []}}}}, {"title": "\u6f0f\u6d1e\u5217\u8868", "title_key": "\u6f0f\u6d1e\u5217\u8868", "filter": "high-level,middle-level,low-level", "mark": "vul-distr", "url": "/interface/report/web-scan/vuln-distribution/1029?riskLevel=high-level,middle-level,low-level&site_list=&is_filtered=0", "data": {"vulns_info": {"webvulns_info_list": {"webvuln_info_list": [{"vul_id": 1000199, "severity_points": 6, "level_name": "middle", "web_vuln_obj": {"id": 5719, "task_site_id": 497, "task_id": 1029, "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "url": "https://tcsb.hnvlts.com:10443/static/js/chunk-libs.ea33a6a6.js", "vul_id": 1000199, "i18n_name": "\u68c0\u6d4b\u5230\u76ee\u6807\u7ad9\u70b9\u5b58\u5728javascript\u6846\u67b6\u5e93\u6f0f\u6d1e", "i18n_description": "JavaScript \u6846\u67b6\u6216\u5e93\u662f\u4e00\u7ec4\u80fd\u8f7b\u677e\u751f\u6210\u8de8\u6d4f\u89c8\u5668\u517c\u5bb9\u7684 JavaScript \u4ee3\u7801\u7684\u5de5\u5177\u548c\u51fd\u6570\u3002\u5982\u679c\u7f51\u7ad9\u4f7f\u7528\u4e86\u5b58\u5728\u6f0f\u6d1e\u7684 JavaScript \u6846\u67b6\u6216\u5e93\uff0c\u653b\u51fb\u8005\u5c31\u53ef\u4ee5\u5229\u7528\u6b64\u6f0f\u6d1e\u6765\u52ab\u6301\u7528\u6237\u6d4f\u89c8\u5668\uff0c\u8fdb\u884c\u6302\u9a6c\u3001XSS\u3001Cookie\u52ab\u6301\u7b49\u653b\u51fb\u3002", "i18n_solution": "\u5c06\u53d7\u5f71\u54cd\u7684javascript\u6846\u67b6\u5e93\u5347\u7ea7\u5230\u6700\u65b0\u7248\u672c\u3002", "severity_points": 6, "threat_level": 1, "is_dangerous": false, "date_found": "2001-01-01", "plugin_id": 1000000, "cve_id": "", "bugtraq_id": "", "nsfocus_id": "", "cnnvd": "", "cncve": "", "cnvd": "", "cvss": "6.1(CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N)", "exp_desc": "\u6839\u636e\u68c0\u6d4b\u5230\u76ee\u6807\u7ad9\u70b9\u5b58\u5728javascript\u6846\u67b6\u5e93\u6f0f\u6d1e\u539f\u7406\uff0c\u901a\u8fc7\u83b7\u53d6javascript\u6846\u67b6\u5e93\u7248\u672c\u5e76\u67e5\u770b\u8be5\u7248\u672c\u662f\u5426\u5728\u53d7\u5f71\u54cd\u8303\u56f4\u5185\u8fdb\u884c\u6f0f\u6d1e\u9a8c\u8bc1\u3002"}, "found_count": 1, "pages": {"https://tcsb.hnvlts.com:10443/static/js/chunk-libs.ea33a6a6.js": ""}, "pages_count": 1, "sites": {"77a1ec49fd4f40eac81ae90dad31bfe5": [497, "https://tcsb.hnvlts.com:10443/"]}, "vul_ifconfirm": true, "vul_confirmed_count": 0}, {"vul_id": 1000117, "severity_points": 3, "level_name": "low", "web_vuln_obj": {"id": 5708, "task_site_id": 497, "task_id": 1029, "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "url": "https://tcsb.hnvlts.com:10443/", "vul_id": 1000117, "i18n_name": "\u68c0\u6d4b\u5230\u4f1a\u8bddcookie\u4e2d\u7f3a\u5c11Secure\u5c5e\u6027", "i18n_description": "\u4f1a\u8bddcookie\u4e2d\u7f3a\u5c11Secure\u5c5e\u6027\u4f1a\u5bfc\u81f4\u653b\u51fb\u8005\u53ef\u4ee5\u901a\u8fc7\u975eHTTPS\u9875\u9762\u7a83\u53d6\u5230\u7528\u6237\u7684cookie\u4fe1\u606f\uff0c\u9020\u6210\u7528\u6237cookie\u4fe1\u606f\u7684\u6cc4\u9732\u3002\r\n\r\ncookie\u4e2d\u7684Secure\u6307\u7684\u662f\u5b89\u5168\u6027\u3002\u901a\u8fc7\u8bbe\u5b9acookie\u4e2d\u7684Secure\uff0c\u53ef\u4ee5\u6307\u5b9acookie\u662f\u5426\u53ea\u80fd\u901a\u8fc7https\u534f\u8bae\u8bbf\u95ee\u3002\u4e00\u822c\u7684cookie\u4f7f\u7528HTTP\u534f\u8bae\u65e2\u53ef\u8bbf\u95ee\uff0c\u5982\u679c\u542f\u7528Secure\u5c5e\u6027\uff0c\u5219\u6d4f\u89c8\u5668\u4ec5\u4ec5\u4f1a\u5728HTTPS\u8bf7\u6c42\u4e2d\u5411\u670d\u52a1\u7aef\u53d1\u9001cookie\u5185\u5bb9\u3002\r\n\r\n\u5728WEB\u5e94\u7528\u4e2d\uff0c\u5bf9\u4e8e\u654f\u611f\u4e1a\u52a1\uff0c\u5982\uff1a\u767b\u5f55\u6216\u8005\u4ed8\u6b3e\uff0c\u9700\u8981\u4f7f\u7528HTTPS\u6765\u4fdd\u8bc1\u5185\u5bb9\u7684\u4f20\u8f93\u5b89\u5168\uff0c\u800c\u5728\u7528\u6237\u6210\u529f\u83b7\u5f97\u6388\u6743\u4e4b\u540e\uff0c\u83b7\u5f97\u7684\u5ba2\u6237\u7aef\u8eab\u4efdcookie\u5982\u679c\u6ca1\u6709\u8bbe\u7f6e\u4e3aSecure\uff0c\u90a3\u4e48\u5f88\u6709\u53ef\u80fd\u4f1a\u88ab\u975eHTTPS\u9875\u9762\u62ff\u5230\uff0c\u4ece\u800c\u9020\u6210\u91cd\u8981\u7684\u8eab\u4efd\u6cc4\u9732\u3002", "i18n_solution": "\u5411\u6240\u6709\u4f1a\u8bddcookie\u4e2d\u6dfb\u52a0\u201cSecure\u201d\u6807\u8bc6\u3002\r\n\r\n\u793a\u4f8b\uff1a\r\n\u672a\u6dfb\u52a0\u6807\u8bc6\u7684cookie\uff1a\r\nCookie: jsessionid=AS348AF929FK219CKA9FK3B79870H;\r\n\u6dfb\u52a0secure\u6807\u8bc6\uff1a\r\nCookie: jsessionid=AS348AF929FK219CKA9FK3B79870H; secure;", "severity_points": 3, "threat_level": 0, "is_dangerous": false, "date_found": "2004-12-31", "plugin_id": 1000000, "cve_id": "CVE-2004-0462", "bugtraq_id": "", "nsfocus_id": "", "cnnvd": "CNNVD-200412-1189", "cncve": "CNCVE-20040462", "cnvd": "", "cvss": "2.8(CVSS:3.0/AV:L/AC:L/PR:L/UI:R/S:U/C:L/I:N/A:N)", "exp_desc": "\u6839\u636e\u68c0\u6d4b\u5230\u4f1a\u8bddcookie\u4e2d\u7f3a\u5c11Secure\u5c5e\u6027\u6f0f\u6d1e\u539f\u7406\uff0c\u901a\u8fc7\u4ece\u76ee\u6807\u7ad9\u70b9\u83b7\u53d6\u914d\u7f6e\u9519\u8bef\u7684cookie\u4fe1\u606f\u8fdb\u884c\u6f0f\u6d1e\u9a8c\u8bc1\u3002"}, "found_count": 1, "pages": {"https://tcsb.hnvlts.com:10443/": ""}, "pages_count": 1, "sites": {"77a1ec49fd4f40eac81ae90dad31bfe5": [497, "https://tcsb.hnvlts.com:10443/"]}, "vul_ifconfirm": true, "vul_confirmed_count": 0}, {"vul_id": 1000256, "severity_points": 2, "level_name": "low", "web_vuln_obj": {"id": 5713, "task_site_id": 497, "task_id": 1029, "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "url": "https://tcsb.hnvlts.com:10443/", "vul_id": 1000256, "i18n_name": "\u68c0\u6d4b\u5230\u76ee\u6807X-Content-Type-Options\u54cd\u5e94\u5934\u7f3a\u5931", "i18n_description": "X-Content-Type-Options HTTP \u6d88\u606f\u5934\u76f8\u5f53\u4e8e\u4e00\u4e2a\u63d0\u793a\u6807\u5fd7\uff0c\u88ab\u670d\u52a1\u5668\u7528\u6765\u63d0\u793a\u5ba2\u6237\u7aef\u4e00\u5b9a\u8981\u9075\u5faa\u5728 Content-Type \u9996\u90e8\u4e2d\u5bf9  MIME \u7c7b\u578b \u7684\u8bbe\u5b9a\uff0c\u800c\u4e0d\u80fd\u5bf9\u5176\u8fdb\u884c\u4fee\u6539\u3002\u8fd9\u5c31\u7981\u7528\u4e86\u5ba2\u6237\u7aef\u7684 MIME \u7c7b\u578b\u55c5\u63a2\u884c\u4e3a\uff0c\u6362\u53e5\u8bdd\u8bf4\uff0c\u4e5f\u5c31\u662f\u610f\u5473\u7740\u7f51\u7ad9\u7ba1\u7406\u5458\u786e\u5b9a\u81ea\u5df1\u7684\u8bbe\u7f6e\u6ca1\u6709\u95ee\u9898\u3002\r\n\r\nX-Content-Type-Options\u54cd\u5e94\u5934\u7684\u7f3a\u5931\u4f7f\u5f97\u76ee\u6807URL\u66f4\u6613\u906d\u53d7\u8de8\u7ad9\u811a\u672c\u653b\u51fb\u3002", "i18n_solution": "\u5c06\u60a8\u7684\u670d\u52a1\u5668\u914d\u7f6e\u4e3a\u5728\u6240\u6709\u4f20\u51fa\u8bf7\u6c42\u4e0a\u53d1\u9001\u503c\u4e3a\u201cnosniff\u201d\u7684\u201cX-Content-Type-Options\u201d\u5934\u3002\u5bf9\u4e8e Apache\uff0c\u8bf7\u53c2\u9605\uff1a\r\nhttp://httpd.apache.org/docs/2.2/mod/mod_headers.html\r\n\u5bf9\u4e8e IIS\uff0c\u8bf7\u53c2\u9605\uff1a\r\nhttps://technet.microsoft.com/pl-pl/library/cc753133%28v=ws.10%29.aspx\r\n\u5bf9\u4e8e nginx\uff0c\u8bf7\u53c2\u9605\uff1a\r\nhttp://nginx.org/en/docs/http/ngx_http_headers_module.html", "severity_points": 2, "threat_level": 0, "is_dangerous": false, "date_found": "2001-01-01", "plugin_id": 1000000, "cve_id": "", "bugtraq_id": "", "nsfocus_id": "", "cnnvd": "", "cncve": "", "cnvd": "", "cvss": "4.3(CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:N/A:N)", "exp_desc": "\u6839\u636e\u68c0\u6d4b\u5230\u76ee\u6807X-Content-Type-Options\u54cd\u5e94\u5934\u7f3a\u5931\u6f0f\u6d1e\u539f\u7406\uff0c\u901a\u8fc7\u4ece\u76ee\u6807\u7ad9\u70b9\u54cd\u5e94\u5934\u4fe1\u606f\u4e2d\u68c0\u67e5X-Content-Type-Options\u914d\u7f6e\u60c5\u51b5\u8fdb\u884c\u6f0f\u6d1e\u9a8c\u8bc1\u3002"}, "found_count": 1, "pages": {"https://tcsb.hnvlts.com:10443/": ""}, "pages_count": 1, "sites": {"77a1ec49fd4f40eac81ae90dad31bfe5": [497, "https://tcsb.hnvlts.com:10443/"]}, "vul_ifconfirm": true, "vul_confirmed_count": 0}, {"vul_id": 1000258, "severity_points": 2, "level_name": "low", "web_vuln_obj": {"id": 5720, "task_site_id": 497, "task_id": 1029, "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "url": "https://tcsb.hnvlts.com:10443/static/js/chunk-libs.ea33a6a6.js", "vul_id": 1000258, "i18n_name": "\u68c0\u6d4b\u5230\u76ee\u6807URL\u5b58\u5728\u5ba2\u6237\u7aef\uff08JavaScript\uff09Cookie\u5f15\u7528", "i18n_description": "Cookie\u901a\u5e38\u7531Web\u670d\u52a1\u5668\u521b\u5efa\u5e76\u5b58\u50a8\u5728\u5ba2\u6237\u7aef\u6d4f\u89c8\u5668\u4e2d\uff0c\u7528\u6765\u5728\u5ba2\u6237\u7aef\u4fdd\u5b58\u7528\u6237\u7684\u8eab\u4efd\u6807\u8bc6\u3001Session\u4fe1\u606f\uff0c\u751a\u81f3\u6388\u6743\u4fe1\u606f\u7b49\u3002\u5ba2\u6237\u7aefJavaScript\u4ee3\u7801\u53ef\u4ee5\u64cd\u4f5cCookie\u6570\u636e\u3002\r\n\r\n\u5982\u679c\u5728\u5ba2\u6237\u7aef\u4f7f\u7528JavaScript\u521b\u5efa\u6216\u4fee\u6539\u7ad9\u70b9\u7684cookie\uff0c\u90a3\u4e48\u653b\u51fb\u8005\u5c31\u53ef\u4ee5\u67e5\u770b\u5230\u8fd9\u4e9b\u4ee3\u7801\uff0c\u901a\u8fc7\u9605\u8bfb\u4ee3\u7801\u4e86\u89e3\u5176\u903b\u8f91\uff0c\u751a\u81f3\u6839\u636e\u81ea\u5df1\u6240\u4e86\u89e3\u7684\u77e5\u8bc6\u5c06\u5176\u7528\u6765\u4fee\u6539cookie\u3002\u4e00\u65e6cookie\u5305\u542b\u4e86\u5f88\u91cd\u8981\u7684\u4fe1\u606f\uff0c\u8b6c\u5982\u5305\u542b\u4e86\u6743\u9650\u4fe1\u606f\u7b49\uff0c\u653b\u51fb\u8005\u5f88\u5bb9\u6613\u5229\u7528\u8fd9\u4e9b\u6f0f\u6d1e\u8fdb\u884c\u7279\u6743\u5347\u7ea7\u7b49\u653b\u51fb\u3002", "i18n_solution": "1\u3001\u907f\u514d\u5728\u5ba2\u6237\u7aef\u653e\u7f6e\u4e1a\u52a1/\u5b89\u5168\u903b\u8f91\u3002\r\n2\u3001\u67e5\u627e\u5e76\u9664\u53bb\u5ba2\u6237\u7aef\u4e0d\u5b89\u5168\u7684 JavaScript \u4ee3\u7801\uff0c\u8be5\u4ee3\u7801\u53ef\u80fd\u4f1a\u5bf9\u7ad9\u70b9\u9020\u6210\u5b89\u5168\u5a01\u80c1\u3002", "severity_points": 2, "threat_level": 0, "is_dangerous": false, "date_found": "2010-01-01", "plugin_id": 1000000, "cve_id": "", "bugtraq_id": "", "nsfocus_id": "", "cnnvd": "", "cncve": "", "cnvd": "", "cvss": "2.6(CVSS:3.0/AV:N/AC:H/PR:L/UI:R/S:U/C:L/I:N/A:N)", "exp_desc": "\u6839\u636e\u68c0\u6d4b\u5230\u76ee\u6807URL\u5b58\u5728\u5ba2\u6237\u7aef\uff08JavaScript\uff09Cookie\u5f15\u7528\u6f0f\u6d1e\u539f\u7406\uff0c\u901a\u8fc7\u68c0\u67e5\u5ba2\u6237\u7aef\uff08JavaScript\uff09Cookie\u5f15\u7528\u60c5\u51b5\u8fdb\u884c\u6f0f\u6d1e\u9a8c\u8bc1\u3002"}, "found_count": 1, "pages": {"https://tcsb.hnvlts.com:10443/static/js/chunk-libs.ea33a6a6.js": ""}, "pages_count": 1, "sites": {"77a1ec49fd4f40eac81ae90dad31bfe5": [497, "https://tcsb.hnvlts.com:10443/"]}, "vul_ifconfirm": true, "vul_confirmed_count": 0}, {"vul_id": 1200648, "severity_points": 2, "level_name": "low", "web_vuln_obj": {"id": 5712, "task_site_id": 497, "task_id": 1029, "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "url": "https://tcsb.hnvlts.com:10443/", "vul_id": 1200648, "i18n_name": "\u68c0\u6d4b\u5230\u9519\u8bef\u9875\u9762web\u5e94\u7528\u670d\u52a1\u5668\u7248\u672c\u4fe1\u606f\u6cc4\u9732", "i18n_description": "Web\u670d\u52a1\u5668\u672a\u80fd\u6b63\u786e\u5904\u7406\u5f02\u5e38\u8bf7\u6c42\u5bfc\u81f4Web\u670d\u52a1\u5668\u7248\u672c\u4fe1\u606f\u6cc4\u9732\uff0c\u653b\u51fb\u8005\u6536\u96c6\u5230\u670d\u52a1\u5668\u4fe1\u606f\u540e\u53ef\u8fdb\u884c\u8fdb\u4e00\u6b65\u9488\u5bf9\u6027\u653b\u51fb\u3002", "i18n_solution": "\u4e34\u65f6\u4fee\u590d\u5efa\u8bae\u5982\u4e0b\uff1a\r\n1\u3001\u5173\u95edweb\u670d\u52a1\u5668\u9519\u8bef\u63d0\u793a\u3002\r\n2\u3001\u5173\u95ed\u8fd0\u884c\u5e73\u53f0\u7684\u9519\u8bef\u63d0\u793a\u3002\r\n3\u3001\u5efa\u7acb\u9519\u8bef\u673a\u5236\uff0c\u4e0d\u8981\u628a\u771f\u5b9e\u7684\u9519\u8bef\u53cd\u9988\u7ed9\u8bbf\u95ee\u8005\u3002", "severity_points": 2, "threat_level": 0, "is_dangerous": false, "date_found": "2015-03-10", "plugin_id": 1000000, "cve_id": "", "bugtraq_id": "", "nsfocus_id": "", "cnnvd": "", "cncve": "", "cnvd": "", "cvss": "", "exp_desc": "\u6839\u636e\u68c0\u6d4b\u5230\u9519\u8bef\u9875\u9762web\u5e94\u7528\u670d\u52a1\u5668\u7248\u672c\u4fe1\u606f\u6cc4\u9732\u6f0f\u6d1e\u539f\u56e0\uff0c\u901a\u8fc7\u4ece\u76ee\u6807\u7ad9\u70b9\u83b7\u53d6\u670d\u52a1\u5668\u7248\u672c\u4fe1\u606f\u8fdb\u884c\u6f0f\u6d1e\u9a8c\u8bc1\u3002"}, "found_count": 1, "pages": {"https://tcsb.hnvlts.com:10443/": ""}, "pages_count": 1, "sites": {"77a1ec49fd4f40eac81ae90dad31bfe5": [497, "https://tcsb.hnvlts.com:10443/"]}, "vul_ifconfirm": true, "vul_confirmed_count": 0}, {"vul_id": 1000255, "severity_points": 2, "level_name": "low", "web_vuln_obj": {"id": 5711, "task_site_id": 497, "task_id": 1029, "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "url": "https://tcsb.hnvlts.com:10443/", "vul_id": 1000255, "i18n_name": "\u68c0\u6d4b\u5230\u76ee\u6807Content-Security-Policy\u54cd\u5e94\u5934\u7f3a\u5931", "i18n_description": "HTTP \u54cd\u5e94\u5934Content-Security-Policy\u5141\u8bb8\u7ad9\u70b9\u7ba1\u7406\u8005\u63a7\u5236\u7528\u6237\u4ee3\u7406\u80fd\u591f\u4e3a\u6307\u5b9a\u7684\u9875\u9762\u52a0\u8f7d\u54ea\u4e9b\u8d44\u6e90\u3002\u9664\u4e86\u5c11\u6570\u4f8b\u5916\u60c5\u51b5\uff0c\u8bbe\u7f6e\u7684\u653f\u7b56\u4e3b\u8981\u6d89\u53ca\u6307\u5b9a\u670d\u52a1\u5668\u7684\u6e90\u548c\u811a\u672c\u7ed3\u675f\u70b9\u3002\r\n\r\nContent-Security-Policy\u54cd\u5e94\u5934\u7684\u7f3a\u5931\u4f7f\u5f97\u76ee\u6807URL\u66f4\u6613\u906d\u53d7\u8de8\u7ad9\u811a\u672c\u653b\u51fb\u3002", "i18n_solution": "\u5c06\u60a8\u7684\u670d\u52a1\u5668\u914d\u7f6e\u4e3a\u53d1\u9001\u201cContent-Security-Policy\u201d\u5934\u3002\u5bf9\u4e8e Apache\uff0c\u8bf7\u53c2\u9605\uff1a\r\nhttp://httpd.apache.org/docs/2.2/mod/mod_headers.html\r\n\u5bf9\u4e8e IIS\uff0c\u8bf7\u53c2\u9605\uff1a\r\nhttps://technet.microsoft.com/pl-pl/library/cc753133%28v=ws.10%29.aspx\r\n\u5bf9\u4e8e nginx\uff0c\u8bf7\u53c2\u9605\uff1a\r\nhttp://nginx.org/en/docs/http/ngx_http_headers_module.html", "severity_points": 2, "threat_level": 0, "is_dangerous": false, "date_found": "2001-01-01", "plugin_id": 1000000, "cve_id": "", "bugtraq_id": "", "nsfocus_id": "", "cnnvd": "", "cncve": "", "cnvd": "", "cvss": "4.3(CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:N/A:N)", "exp_desc": "\u6839\u636e\u68c0\u6d4b\u5230\u76ee\u6807Content-Security-Policy\u54cd\u5e94\u5934\u7f3a\u5931\u6f0f\u6d1e\u539f\u7406\uff0c\u901a\u8fc7\u4ece\u76ee\u6807\u7ad9\u70b9\u54cd\u5e94\u5934\u4fe1\u606f\u4e2d\u68c0\u67e5Content-Security-Policy\u914d\u7f6e\u60c5\u51b5\u8fdb\u884c\u6f0f\u6d1e\u9a8c\u8bc1\u3002"}, "found_count": 1, "pages": {"https://tcsb.hnvlts.com:10443/": ""}, "pages_count": 1, "sites": {"77a1ec49fd4f40eac81ae90dad31bfe5": [497, "https://tcsb.hnvlts.com:10443/"]}, "vul_ifconfirm": true, "vul_confirmed_count": 0}, {"vul_id": 1000257, "severity_points": 2, "level_name": "low", "web_vuln_obj": {"id": 5709, "task_site_id": 497, "task_id": 1029, "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "url": "https://tcsb.hnvlts.com:10443/", "vul_id": 1000257, "i18n_name": "\u68c0\u6d4b\u5230\u76ee\u6807X-XSS-Protection\u54cd\u5e94\u5934\u7f3a\u5931", "i18n_description": "HTTP X-XSS-Protection \u54cd\u5e94\u5934\u662f Internet Explorer\uff0cChrome \u548c Safari \u7684\u4e00\u4e2a\u7279\u6027\uff0c\u5f53\u68c0\u6d4b\u5230\u8de8\u7ad9\u811a\u672c\u653b\u51fb (XSS)\u65f6\uff0c\u6d4f\u89c8\u5668\u5c06\u505c\u6b62\u52a0\u8f7d\u9875\u9762\u3002\r\n\r\nX-XSS-Protection\u54cd\u5e94\u5934\u7684\u7f3a\u5931\u4f7f\u5f97\u76ee\u6807URL\u66f4\u6613\u906d\u53d7\u8de8\u7ad9\u811a\u672c\u653b\u51fb\u3002", "i18n_solution": "\u5c06\u60a8\u7684\u670d\u52a1\u5668\u914d\u7f6e\u4e3a\u5728\u6240\u6709\u4f20\u51fa\u8bf7\u6c42\u4e0a\u53d1\u9001\u503c\u4e3a\u201c1\u201d\uff08\u4f8b\u5982\u5df2\u542f\u7528\uff09\u7684\u201cX-XSS-Protection\u201d\u5934\u3002\u5bf9\u4e8e Apache\uff0c\u8bf7\u53c2\u9605\uff1a\r\nhttp://httpd.apache.org/docs/2.2/mod/mod_headers.html\r\n\u5bf9\u4e8e IIS\uff0c\u8bf7\u53c2\u9605\uff1a\r\nhttps://technet.microsoft.com/pl-pl/library/cc753133%28v=ws.10%29.aspx\r\n\u5bf9\u4e8e nginx\uff0c\u8bf7\u53c2\u9605\uff1a\r\nhttp://nginx.org/en/docs/http/ngx_http_headers_module.html", "severity_points": 2, "threat_level": 0, "is_dangerous": false, "date_found": "2001-01-01", "plugin_id": 1000000, "cve_id": "", "bugtraq_id": "", "nsfocus_id": "", "cnnvd": "", "cncve": "", "cnvd": "", "cvss": "4.3(CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:N/A:N)", "exp_desc": "\u6839\u636e\u68c0\u6d4b\u5230\u76ee\u6807X-XSS-Protection\u54cd\u5e94\u5934\u7f3a\u5931\u6f0f\u6d1e\u539f\u7406\uff0c\u901a\u8fc7\u4ece\u76ee\u6807\u7ad9\u70b9\u54cd\u5e94\u5934\u4fe1\u606f\u4e2d\u68c0\u67e5X-XSS-Protection\u914d\u7f6e\u60c5\u51b5\u8fdb\u884c\u6f0f\u6d1e\u9a8c\u8bc1\u3002"}, "found_count": 1, "pages": {"https://tcsb.hnvlts.com:10443/": ""}, "pages_count": 1, "sites": {"77a1ec49fd4f40eac81ae90dad31bfe5": [497, "https://tcsb.hnvlts.com:10443/"]}, "vul_ifconfirm": true, "vul_confirmed_count": 0}, {"vul_id": 1000297, "severity_points": 1, "level_name": "low", "web_vuln_obj": {"id": 5715, "task_site_id": 497, "task_id": 1029, "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "url": "https://tcsb.hnvlts.com:10443/", "vul_id": 1000297, "i18n_name": "\u68c0\u6d4b\u5230\u76ee\u6807X-Permitted-Cross-Domain-Policies\u54cd\u5e94\u5934\u7f3a\u5931", "i18n_description": "Web \u670d\u52a1\u5668\u5bf9\u4e8e HTTP \u8bf7\u6c42\u7684\u54cd\u5e94\u5934\u4e2d\u7f3a\u5c11 X-Permitted-Cross-Domain-Policies\uff0c\u8fd9\u5c06\u5bfc\u81f4\u6d4f\u89c8\u5668\u63d0\u4f9b\u7684\u5b89\u5168\u7279\u6027\u5931\u6548\u3002 \u5f53\u4e00\u4e9b\u5728\u7ebf\u7684 Web Flash \u9700\u8981\u52a0\u8f7d\u5176\u4ed6\u57df\u7684\u5185\u5bb9\u65f6\uff0c\u5f88\u591a Web \u4f1a\u901a\u8fc7\u8bbe\u7f6e\u4e00\u4e2a crossdomain.xml \u6587\u4ef6\u7684\u65b9\u5f0f\u6765\u63a7\u5236\u5176\u8de8\u57df\u65b9\u5f0f\u3002\u5f88\u6709\u53ef\u80fd\u6709\u4e9b\u5f00\u53d1\u8005\u5e76\u6ca1\u6709\u4fee\u6539 crossdomain.xml \u6587\u4ef6\u7684\u6743\u9650\uff0c\u4f46\u662f\u53c8\u6709\u548c\u8de8\u57df\u7684 Flash \u5171\u4eab\u6570\u636e\u7684\u9700\u6c42\uff0c\u8fd9\u65f6\u5019\u53ef\u4ee5\u901a\u8fc7\u8bbe\u7f6e X-Permitted-Cross-Domain-Policies \u5934\u7684\u65b9\u5f0f\u6765\u66ff\u4ee3 crossdomain.xml \u6587\u4ef6\uff0c\u5176\u53ef\u9009\u7684\u503c\u6709\uff1a none master-only by-content-type by-ftp-filename all \u6f0f\u6d1e\u5371\u5bb3\uff1a Web \u670d\u52a1\u5668\u5bf9\u4e8e HTTP \u8bf7\u6c42\u7684\u54cd\u5e94\u5934\u4e2d\u7f3a\u5c11 X-Permitted-Cross-Domain-Policies\uff0c\u8fd9\u5c06\u5bfc\u81f4\u6d4f\u89c8\u5668\u63d0\u4f9b\u7684\u5b89\u5168\u7279\u6027\u5931\u6548\uff0c\u66f4\u5bb9\u6613\u906d\u53d7 Web \u524d\u7aef\u9ed1\u5ba2\u653b\u51fb\u7684\u5f71\u54cd\u3002", "i18n_solution": "1\uff09\u4fee\u6539\u670d\u52a1\u7aef\u7a0b\u5e8f\uff0c\u7ed9 HTTP \u54cd\u5e94\u5934\u52a0\u4e0a X-Permitted-Cross-Domain-Policies \u5982\u679c\u662f java \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 response.setHeader(\"X-Permitted-Cross-Domain-Policies\", \"value\") \u5982\u679c\u662f php \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 header(\"X-Permitted-Cross-Domain-Policies: value\") \u5982\u679c\u662f asp \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 Response.AddHeader \"X-Permitted-Cross-Domain-Policies\", \"value\" \u5982\u679c\u662f python django \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 response = HttpResponse() response[\"X-Permitted-Cross-Domain-Policies\"] = \"value\" \u5982\u679c\u662f python flask \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 response = make_response() response.headers[\"X-Permitted-Cross-Domain-Policies\"] = \"value\"\uff1b\r\n2\uff09\u4fee\u6539\u8d1f\u8f7d\u5747\u8861\u6216\u53cd\u5411\u4ee3\u7406\u670d\u52a1\u5668\uff0c\u7ed9 HTTP \u54cd\u5e94\u5934\u52a0\u4e0a X-Permitted-Cross-Domain-Policies \u5982\u679c\u4f7f\u7528 Nginx\u3001Tengine\u3001Openresty \u7b49\u4f5c\u4e3a\u4ee3\u7406\u670d\u52a1\u5668\uff0c\u5728\u914d\u7f6e\u6587\u4ef6\u4e2d\u5199\u5165\u5982\u4e0b\u5185\u5bb9\u5373\u53ef\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934\uff1a add_header X-Permitted-Cross-Domain-Policies value; \u5982\u679c\u4f7f\u7528 Apache \u4f5c\u4e3a\u4ee3\u7406\u670d\u52a1\u5668\uff0c\u5728\u914d\u7f6e\u6587\u4ef6\u4e2d\u5199\u5165\u5982\u4e0b\u5185\u5bb9\u5373\u53ef\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934\uff1a Header add X-Permitted-Cross-Domain-Policies \"value\"\u3002", "severity_points": 1, "threat_level": 0, "is_dangerous": false, "date_found": "2001-01-01", "plugin_id": 1000000, "cve_id": "", "bugtraq_id": "", "nsfocus_id": "", "cnnvd": "", "cncve": "", "cnvd": "", "cvss": "4.3(CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:N/A:N)", "exp_desc": ""}, "found_count": 1, "pages": {"https://tcsb.hnvlts.com:10443/": ""}, "pages_count": 1, "sites": {"77a1ec49fd4f40eac81ae90dad31bfe5": [497, "https://tcsb.hnvlts.com:10443/"]}, "vul_ifconfirm": false, "vul_confirmed_count": 0}, {"vul_id": 1000295, "severity_points": 1, "level_name": "low", "web_vuln_obj": {"id": 5716, "task_site_id": 497, "task_id": 1029, "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "url": "https://tcsb.hnvlts.com:10443/", "vul_id": 1000295, "i18n_name": "\u68c0\u6d4b\u5230\u76ee\u6807Strict-Transport-Security\u54cd\u5e94\u5934\u7f3a\u5931", "i18n_description": "Web \u670d\u52a1\u5668\u5bf9\u4e8e HTTP \u8bf7\u6c42\u7684\u54cd\u5e94\u5934\u4e2d\u7f3a\u5c11 Strict-Transport-Security\uff0c\u8fd9\u5c06\u5bfc\u81f4\u6d4f\u89c8\u5668\u63d0\u4f9b\u7684\u5b89\u5168\u7279\u6027\u5931\u6548\u3002 \u5f53 Web \u670d\u52a1\u5668\u7684 HTTP \u5934\u4e2d\u5305\u542b Strict-Transport-Security \u5934\u65f6\uff0c\u6d4f\u89c8\u5668\u5c06\u6301\u7eed\u4f7f\u7528 HTTPS \u6765\u8bbf\u95ee Web \u7ad9\u70b9\uff0c\u53ef\u4ee5\u7528\u6765\u5bf9\u6297\u534f\u8bae\u964d\u7ea7\u653b\u51fb\u548c Cookie \u52ab\u6301\u653b\u51fb\u3002\r\n\u5176\u53ef\u9009\u7684\u503c\u6709\uff1a max-age=SECONDS\uff0c\u8868\u793a\u672c\u6b21\u547d\u4ee4\u5728\u672a\u6765\u7684\u751f\u6548\u65f6\u95f4 includeSubDomains\uff0c\u53ef\u4ee5\u7528\u6765\u6307\u5b9a\u662f\u5426\u5bf9\u5b50\u57df\u540d\u751f\u6548 \u6f0f\u6d1e\u5371\u5bb3\uff1a Web \u670d\u52a1\u5668\u5bf9\u4e8e HTTP \u8bf7\u6c42\u7684\u54cd\u5e94\u5934\u4e2d\u7f3a\u5c11 Strict-Transport-Security\uff0c\u8fd9\u5c06\u5bfc\u81f4\u6d4f\u89c8\u5668\u63d0\u4f9b\u7684\u5b89\u5168\u7279\u6027\u5931\u6548\uff0c\u66f4\u5bb9\u6613\u906d\u53d7 Web \u524d\u7aef\u9ed1\u5ba2\u653b\u51fb\u7684\u5f71\u54cd\u3002", "i18n_solution": "1\uff09\u4fee\u6539\u670d\u52a1\u7aef\u7a0b\u5e8f\uff0c\u7ed9 HTTP \u54cd\u5e94\u5934\u52a0\u4e0a Strict-Transport-Security \u5982\u679c\u662f java \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 response.setHeader(\"Strict-Transport-Security\", \"value\") \u5982\u679c\u662f php \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 header(\"Strict-Transport-Security: value\") \u5982\u679c\u662f asp \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 Response.AddHeader \"Strict-Transport-Security\", \"value\" \u5982\u679c\u662f python django \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 response = HttpResponse() response[\"Strict-Transport-Security\"] = \"value\" \u5982\u679c\u662f python flask \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 response = make_response() response.headers[\"Strict-Transport-Security\"] = \"value\"\uff1b\r\n2\uff09\u4fee\u6539\u8d1f\u8f7d\u5747\u8861\u6216\u53cd\u5411\u4ee3\u7406\u670d\u52a1\u5668\uff0c\u7ed9 HTTP \u54cd\u5e94\u5934\u52a0\u4e0a Strict-Transport-Security \u5982\u679c\u4f7f\u7528 Nginx\u3001Tengine\u3001Openresty \u7b49\u4f5c\u4e3a\u4ee3\u7406\u670d\u52a1\u5668\uff0c\u5728\u914d\u7f6e\u6587\u4ef6\u4e2d\u5199\u5165\u5982\u4e0b\u5185\u5bb9\u5373\u53ef\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934\uff1a add_header Strict-Transport-Security value; \u5982\u679c\u4f7f\u7528 Apache \u4f5c\u4e3a\u4ee3\u7406\u670d\u52a1\u5668\uff0c\u5728\u914d\u7f6e\u6587\u4ef6\u4e2d\u5199\u5165\u5982\u4e0b\u5185\u5bb9\u5373\u53ef\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934\uff1a Header add Strict-Transport-Security \"value\"\u3002", "severity_points": 1, "threat_level": 0, "is_dangerous": false, "date_found": "2001-01-01", "plugin_id": 1000000, "cve_id": "", "bugtraq_id": "", "nsfocus_id": "", "cnnvd": "", "cncve": "", "cnvd": "", "cvss": "4.3(CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:N/A:N)", "exp_desc": ""}, "found_count": 1, "pages": {"https://tcsb.hnvlts.com:10443/": ""}, "pages_count": 1, "sites": {"77a1ec49fd4f40eac81ae90dad31bfe5": [497, "https://tcsb.hnvlts.com:10443/"]}, "vul_ifconfirm": false, "vul_confirmed_count": 0}, {"vul_id": 1000145, "severity_points": 1, "level_name": "low", "web_vuln_obj": {"id": 5710, "task_site_id": 497, "task_id": 1029, "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "url": "https://tcsb.hnvlts.com:10443/", "vul_id": 1000145, "i18n_name": "\u70b9\u51fb\u52ab\u6301\uff1aX-Frame-Options\u672a\u914d\u7f6e", "i18n_description": "\u70b9\u51fb\u52ab\u6301\uff08ClickJacking\uff09\u662f\u4e00\u79cd\u89c6\u89c9\u4e0a\u7684\u6b3a\u9a97\u624b\u6bb5\u3002\u653b\u51fb\u8005\u4f7f\u7528\u4e00\u4e2a\u900f\u660e\u7684\u3001\u4e0d\u53ef\u89c1\u7684iframe\uff0c\u8986\u76d6\u5728\u4e00\u4e2a\u7f51\u9875\u4e0a\uff0c\u7136\u540e\u8bf1\u4f7f\u7528\u6237\u5728\u8be5\u7f51\u9875\u4e0a\u8fdb\u884c\u64cd\u4f5c\uff0c\u6b64\u65f6\u7528\u6237\u5c06\u5728\u4e0d\u77e5\u60c5\u7684\u60c5\u51b5\u4e0b\u70b9\u51fb\u900f\u660e\u7684iframe\u9875\u9762\u3002\u901a\u8fc7\u8c03\u6574iframe\u9875\u9762\u7684\u4f4d\u7f6e\uff0c\u53ef\u4ee5\u8bf1\u4f7f\u7528\u6237\u6070\u597d\u70b9\u51fb\u5728iframe\u9875\u9762\u7684\u4e00\u4e9b\u529f\u80fd\u6027\u6309\u94ae\u4e0a\u3002\r\nHTTP \u54cd\u5e94\u5934\u4fe1\u606f\u4e2d\u7684X-Frame-Options\uff0c\u53ef\u4ee5\u6307\u793a\u6d4f\u89c8\u5668\u662f\u5426\u5e94\u8be5\u52a0\u8f7d\u4e00\u4e2a iframe \u4e2d\u7684\u9875\u9762\u3002\u5982\u679c\u670d\u52a1\u5668\u54cd\u5e94\u5934\u4fe1\u606f\u4e2d\u6ca1\u6709X-Frame-Options\uff0c\u5219\u8be5\u7f51\u7ad9\u5b58\u5728ClickJacking\u653b\u51fb\u98ce\u9669\u3002\u7f51\u7ad9\u53ef\u4ee5\u901a\u8fc7\u8bbe\u7f6e X-Frame-Options \u963b\u6b62\u7ad9\u70b9\u5185\u7684\u9875\u9762\u88ab\u5176\u4ed6\u9875\u9762\u5d4c\u5165\u4ece\u800c\u9632\u6b62\u70b9\u51fb\u52ab\u6301\u3002", "i18n_solution": "\u4fee\u6539web\u670d\u52a1\u5668\u914d\u7f6e\uff0c\u6dfb\u52a0X-Frame-Options\u54cd\u5e94\u5934\u3002\u8d4b\u503c\u6709\u5982\u4e0b\u4e09\u79cd\uff1a\r\n1\u3001DENY\uff1a\u4e0d\u80fd\u88ab\u5d4c\u5165\u5230\u4efb\u4f55iframe\u6216\u8005frame\u4e2d\u3002\r\n2\u3001SAMEORIGIN:\u9875\u9762\u53ea\u80fd\u88ab\u672c\u7ad9\u9875\u9762\u5d4c\u5165\u5230iframe\u6216\u8005frame\u4e2d\u3002\r\n3\u3001ALLOW-FROM uri\uff1a\u53ea\u80fd\u88ab\u5d4c\u5165\u5230\u6307\u5b9a\u57df\u540d\u7684\u6846\u67b6\u4e2d\u3002\r\n\u4f8b\u5982\uff1a\r\napache\u53ef\u914d\u7f6ehttp.conf\u5982\u4e0b\uff1a\r\n\\〈IfModule headers_module\\〉\r\n\tHeader always append X-Frame-Options \"DENY\"\r\n\\〈/IfModule\\〉\r\n\r\nIIS\u53ef\u914d\u7f6e\u76f8\u5173\u7f51\u7ad9\u7684Web.config\u5982\u4e0b\uff1a\r\n\\〈system.webServer\\〉\r\n  ...\r\n\r\n  \\〈httpProtocol\\〉\r\n    \\〈customHeaders\\〉\r\n      \\〈add name=\"X-Frame-Options\" value=\"deny\" /\\〉\r\n    \\〈/customHeaders\\〉\r\n  \\〈/httpProtocol\\〉\r\n\r\n  ...\r\n\\〈/system.webServer\\〉", "severity_points": 1, "threat_level": 0, "is_dangerous": false, "date_found": "2001-01-01", "plugin_id": 1000000, "cve_id": "", "bugtraq_id": "", "nsfocus_id": "", "cnnvd": "", "cncve": "", "cnvd": "", "cvss": "5.3(CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L)", "exp_desc": "\u6839\u636e\u70b9\u51fb\u52ab\u6301\uff1aX-Frame-Options\u672a\u914d\u7f6e\u6f0f\u6d1e\u539f\u7406\uff0c\u901a\u8fc7\u4ece\u76ee\u6807\u7ad9\u70b9\u54cd\u5e94\u5934\u4fe1\u606f\u4e2d\u68c0\u67e5X-Frame-Options\u914d\u7f6e\u60c5\u51b5\u8fdb\u884c\u6f0f\u6d1e\u9a8c\u8bc1\u3002"}, "found_count": 1, "pages": {"https://tcsb.hnvlts.com:10443/": ""}, "pages_count": 1, "sites": {"77a1ec49fd4f40eac81ae90dad31bfe5": [497, "https://tcsb.hnvlts.com:10443/"]}, "vul_ifconfirm": true, "vul_confirmed_count": 0}, {"vul_id": 1000296, "severity_points": 1, "level_name": "low", "web_vuln_obj": {"id": 5714, "task_site_id": 497, "task_id": 1029, "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "url": "https://tcsb.hnvlts.com:10443/", "vul_id": 1000296, "i18n_name": "\u68c0\u6d4b\u5230\u76ee\u6807Referrer-Policy\u54cd\u5e94\u5934\u7f3a\u5931", "i18n_description": "Web \u670d\u52a1\u5668\u5bf9\u4e8e HTTP \u8bf7\u6c42\u7684\u54cd\u5e94\u5934\u4e2d\u7f3a\u5c11 Referrer-Policy\uff0c\u8fd9\u5c06\u5bfc\u81f4\u6d4f\u89c8\u5668\u63d0\u4f9b\u7684\u5b89\u5168\u7279\u6027\u5931\u6548\u3002 \u5f53\u7528\u6237\u5728\u6d4f\u89c8\u5668\u4e0a\u70b9\u51fb\u4e00\u4e2a\u94fe\u63a5\u65f6\uff0c\u4f1a\u4ea7\u751f\u4e00\u4e2a HTTP \u8bf7\u6c42\uff0c\u7528\u4e8e\u83b7\u53d6\u65b0\u7684\u9875\u9762\u5185\u5bb9\uff0c\u800c\u5728\u8be5\u8bf7\u6c42\u7684\u62a5\u5934\u4e2d\uff0c\u4f1a\u5305\u542b\u4e00\u4e2a Referrer\uff0c\u7528\u4ee5\u6307\u5b9a\u8be5\u8bf7\u6c42\u662f\u4ece\u54ea\u4e2a\u9875\u9762\u8df3\u8f6c\u9875\u6765\u7684\uff0c\u5e38\u88ab\u7528\u4e8e\u5206\u6790\u7528\u6237\u6765\u6e90\u7b49\u4fe1\u606f\u3002\u4f46\u662f\u4e5f\u6210\u4e3a\u4e86\u4e00\u4e2a\u4e0d\u5b89\u5168\u7684\u56e0\u7d20\uff0c\u6240\u4ee5\u5c31\u6709\u4e86 Referrer-Policy\uff0c\u7528\u4e8e\u8fc7\u6ee4 Referrer \u62a5\u5934\u5185\u5bb9\uff0c\u5176\u53ef\u9009\u7684\u9879\u6709\uff1a no-referrer no-referrer-when-downgrade origin origin-when-cross-origin same-origin strict-origin strict-origin-when-cross-origin unsafe-url \u6f0f\u6d1e\u5371\u5bb3\uff1a Web \u670d\u52a1\u5668\u5bf9\u4e8e HTTP \u8bf7\u6c42\u7684\u54cd\u5e94\u5934\u4e2d\u7f3a\u5c11 Referrer-Policy\uff0c\u8fd9\u5c06\u5bfc\u81f4\u6d4f\u89c8\u5668\u63d0\u4f9b\u7684\u5b89\u5168\u7279\u6027\u5931\u6548\uff0c\u66f4\u5bb9\u6613\u906d\u53d7 Web \u524d\u7aef\u9ed1\u5ba2\u653b\u51fb\u7684\u5f71\u54cd\u3002", "i18n_solution": "1\uff09\u4fee\u6539\u670d\u52a1\u7aef\u7a0b\u5e8f\uff0c\u7ed9 HTTP \u54cd\u5e94\u5934\u52a0\u4e0a Referrer-Policy \u5982\u679c\u662f java \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 response.setHeader(\"Referrer-Policy\", \"value\") \u5982\u679c\u662f php \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 header(\"Referrer-Policy: value\") \u5982\u679c\u662f asp \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 Response.AddHeader \"Referrer-Policy\", \"value\" \u5982\u679c\u662f python django \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 response = HttpResponse() response[\"Referrer-Policy\"] = \"value\" \u5982\u679c\u662f python flask \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 response = make_response() response.headers[\"Referrer-Policy\"] = \"value\"\uff1b\r\n2\uff09\u4fee\u6539\u8d1f\u8f7d\u5747\u8861\u6216\u53cd\u5411\u4ee3\u7406\u670d\u52a1\u5668\uff0c\u7ed9 HTTP \u54cd\u5e94\u5934\u52a0\u4e0a Referrer-Policy \u5982\u679c\u4f7f\u7528 Nginx\u3001Tengine\u3001Openresty \u7b49\u4f5c\u4e3a\u4ee3\u7406\u670d\u52a1\u5668\uff0c\u5728\u914d\u7f6e\u6587\u4ef6\u4e2d\u5199\u5165\u5982\u4e0b\u5185\u5bb9\u5373\u53ef\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934\uff1a add_header Referrer-Policy value; \u5982\u679c\u4f7f\u7528 Apache \u4f5c\u4e3a\u4ee3\u7406\u670d\u52a1\u5668\uff0c\u5728\u914d\u7f6e\u6587\u4ef6\u4e2d\u5199\u5165\u5982\u4e0b\u5185\u5bb9\u5373\u53ef\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934\uff1a Header add Referrer-Policy \"value\"\u3002", "severity_points": 1, "threat_level": 0, "is_dangerous": false, "date_found": "2001-01-01", "plugin_id": 1000000, "cve_id": "", "bugtraq_id": "", "nsfocus_id": "", "cnnvd": "", "cncve": "", "cnvd": "", "cvss": "4.3(CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:N/A:N)", "exp_desc": ""}, "found_count": 1, "pages": {"https://tcsb.hnvlts.com:10443/": ""}, "pages_count": 1, "sites": {"77a1ec49fd4f40eac81ae90dad31bfe5": [497, "https://tcsb.hnvlts.com:10443/"]}, "vul_ifconfirm": false, "vul_confirmed_count": 0}, {"vul_id": 1201819, "severity_points": 1, "level_name": "low", "web_vuln_obj": {"id": 5718, "task_site_id": 497, "task_id": 1029, "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "url": "https://tcsb.hnvlts.com:10443/", "vul_id": 1201819, "i18n_name": "HTTP \u7f3a\u5c11\u5b89\u5168\u5934\u6f0f\u6d1e", "i18n_description": "\u76ee\u6807\u672a\u914d\u7f6e\u5173\u952e\u5b89\u5168\u54cd\u5e94\u5934\uff08permissions-policy\u3001clear-site-data\u3001cross-origin-embedder-policy\u3001cross-origin-opener-policy\u3001cross-origin-resource-policy\u3001access-control-allow-origin\uff09\uff0c\u53ef\u80fd\u5bfc\u81f4\u6d4f\u89c8\u5668\u5b89\u5168\u7b56\u7565\u7f3a\u5931\uff0c\u589e\u52a0\u8de8\u7ad9\u70b9\u653b\u51fb\u548c\u8d44\u6e90\u5171\u4eab\u98ce\u9669\u3002", "i18n_solution": "\u5728\u670d\u52a1\u5668\u914d\u7f6e\u4e2d\u6dfb\u52a0\u4ee5\u4e0b\u5b89\u5168\u54cd\u5e94\u5934\uff1a\r\n\r\npermissions-policy\uff1a\u9650\u5236\u7279\u5b9a\u529f\u80fd\u7684\u4f7f\u7528\u8303\u56f4\u3002\r\nclear-site-data\uff1a\u6e05\u9664\u6d4f\u89c8\u5668\u5b58\u50a8\u7684\u6570\u636e\u4ee5\u9632\u4fe1\u606f\u6cc4\u9732\u3002\r\ncross-origin-embedder-policy\uff1a\u589e\u5f3a\u8de8\u6e90\u8d44\u6e90\u52a0\u8f7d\u5b89\u5168\u6027\u3002\r\ncross-origin-opener-policy\uff1a\u9632\u6b62\u8de8\u6e90\u7a97\u53e3\u52ab\u6301\u3002\r\ncross-origin-resource-policy\uff1a\u9650\u5236\u8de8\u6e90\u8d44\u6e90\u8bbf\u95ee\u3002\r\naccess-control-allow-origin\uff1a\u660e\u786e\u5141\u8bb8\u7684\u8de8\u57df\u8bf7\u6c42\u6765\u6e90\u3002\r\n\u786e\u4fdd\u5934\u4fe1\u606f\u5185\u5bb9\u6839\u636e\u5e94\u7528\u9700\u6c42\u6b63\u786e\u914d\u7f6e\u3002", "severity_points": 1, "threat_level": 0, "is_dangerous": false, "date_found": "2001-01-01", "plugin_id": 1000000, "cve_id": "", "bugtraq_id": "", "nsfocus_id": "", "cnnvd": "", "cncve": "", "cnvd": "", "cvss": "", "exp_desc": ""}, "found_count": 1, "pages": {"https://tcsb.hnvlts.com:10443/": ""}, "pages_count": 1, "sites": {"77a1ec49fd4f40eac81ae90dad31bfe5": [497, "https://tcsb.hnvlts.com:10443/"]}, "vul_ifconfirm": false, "vul_confirmed_count": 0}, {"vul_id": 1000298, "severity_points": 1, "level_name": "low", "web_vuln_obj": {"id": 5717, "task_site_id": 497, "task_id": 1029, "seed_url": "https://tcsb.hnvlts.com:10443/", "target_ip": "*************/32", "url_hash": "77a1ec49fd4f40eac81ae90dad31bfe5", "url": "https://tcsb.hnvlts.com:10443/", "vul_id": 1000298, "i18n_name": "\u68c0\u6d4b\u5230\u76ee\u6807X-Download-Options\u54cd\u5e94\u5934\u7f3a\u5931", "i18n_description": "Web \u670d\u52a1\u5668\u5bf9\u4e8e HTTP \u8bf7\u6c42\u7684\u54cd\u5e94\u5934\u4e2d\u7f3a\u5c11 X-Download-Options\uff0c\u8fd9\u5c06\u5bfc\u81f4\u6d4f\u89c8\u5668\u63d0\u4f9b\u7684\u5b89\u5168\u7279\u6027\u5931\u6548\u3002 \u6f0f\u6d1e\u5371\u5bb3: Web \u670d\u52a1\u5668\u5bf9\u4e8e HTTP \u8bf7\u6c42\u7684\u54cd\u5e94\u5934\u4e2d\u7f3a\u5c11 X-Download-Options\uff0c\u8fd9\u5c06\u5bfc\u81f4\u6d4f\u89c8\u5668\u63d0\u4f9b\u7684\u5b89\u5168\u7279\u6027\u5931\u6548\uff0c\u66f4\u5bb9\u6613\u906d\u53d7 Web \u524d\u7aef\u9ed1\u5ba2\u653b\u51fb\u7684\u5f71\u54cd\u3002", "i18n_solution": "1\uff09\u4fee\u6539\u670d\u52a1\u7aef\u7a0b\u5e8f\uff0c\u7ed9 HTTP \u54cd\u5e94\u5934\u52a0\u4e0a X-Download-Options \u5982\u679c\u662f java \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 response.setHeader(\"X-Download-Options\", \"value\") \u5982\u679c\u662f php \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 header(\"X-Download-Options: value\") \u5982\u679c\u662f asp \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 Response.AddHeader \"X-Download-Options\", \"value\" \u5982\u679c\u662f python django \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 response = HttpResponse() response[\"X-Download-Options\"] = \"value\" \u5982\u679c\u662f python flask \u670d\u52a1\u7aef\uff0c\u53ef\u4ee5\u4f7f\u7528\u5982\u4e0b\u65b9\u5f0f\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934 response = make_response() response.headers[\"X-Download-Options\"] = \"value\"\uff1b\r\n2\uff09\u4fee\u6539\u8d1f\u8f7d\u5747\u8861\u6216\u53cd\u5411\u4ee3\u7406\u670d\u52a1\u5668\uff0c\u7ed9 HTTP \u54cd\u5e94\u5934\u52a0\u4e0a X-Download-Options \u5982\u679c\u4f7f\u7528 Nginx\u3001Tengine\u3001Openresty \u7b49\u4f5c\u4e3a\u4ee3\u7406\u670d\u52a1\u5668\uff0c\u5728\u914d\u7f6e\u6587\u4ef6\u4e2d\u5199\u5165\u5982\u4e0b\u5185\u5bb9\u5373\u53ef\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934\uff1a add_header X-Download-Options value; \u5982\u679c\u4f7f\u7528 Apache \u4f5c\u4e3a\u4ee3\u7406\u670d\u52a1\u5668\uff0c\u5728\u914d\u7f6e\u6587\u4ef6\u4e2d\u5199\u5165\u5982\u4e0b\u5185\u5bb9\u5373\u53ef\u6dfb\u52a0 HTTP \u54cd\u5e94\u5934\uff1a Header add X-Download-Options \"value\"\u3002", "severity_points": 1, "threat_level": 0, "is_dangerous": false, "date_found": "2001-01-01", "plugin_id": 1000000, "cve_id": "", "bugtraq_id": "", "nsfocus_id": "", "cnnvd": "", "cncve": "", "cnvd": "", "cvss": "4.3(CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:N/A:N)", "exp_desc": ""}, "found_count": 1, "pages": {"https://tcsb.hnvlts.com:10443/": ""}, "pages_count": 1, "sites": {"77a1ec49fd4f40eac81ae90dad31bfe5": [497, "https://tcsb.hnvlts.com:10443/"]}, "vul_ifconfirm": false, "vul_confirmed_count": 0}], "webvulns_level_count": {"high": 0, "middle": 1, "low": 12, "vulns_count": 13}, "vul_ifconfirm_count": 8, "vul_confirmed_count": 0}}}}, {"title": "\u53c2\u8003\u6807\u51c6", "title_key": "\u53c2\u8003\u6807\u51c6", "mark": "standards", "children": [{"title": "\u5355\u4e00\u6f0f\u6d1e\u98ce\u9669\u7b49\u7ea7\u8bc4\u5b9a\u6807\u51c6", "title_key": "\u5355\u4e00\u6f0f\u6d1e\u98ce\u9669\u7b49\u7ea7\u8bc4\u5b9a\u6807\u51c6", "mark": "vuln-standard", "url": "/interface/report/web-scan/refer-standard-vuln/1029", "data": {"standard": {"title": "\u5355\u4e00\u6f0f\u6d1e\u98ce\u9669\u7b49\u7ea7\u8bc4\u5b9a\u6807\u51c6", "data": [{"level": "\u9ad8", "range": "7 〈= \u6f0f\u6d1e\u98ce\u9669\u503c 〈= 10", "description": "\u653b\u51fb\u8005\u53ef\u4ee5\u8fdc\u7a0b\u64cd\u4f5c\u7cfb\u7edf\u6587\u4ef6\u3001\u8bfb\u5199\u540e\u53f0\u6570\u636e\u5e93\u3001\u6267\u884c\u4efb\u610f\u547d\u4ee4\u6216\u8fdb\u884c\u8fdc\u7a0b\u62d2\u7edd\u670d\u52a1\u653b\u51fb\u3002"}, {"level": "\u4e2d", "range": "4 〈= \u6f0f\u6d1e\u98ce\u9669\u503c 〈 7", "description": "\u653b\u51fb\u8005\u53ef\u4ee5\u5229\u7528Web\u7f51\u7ad9\u653b\u51fb\u5176\u4ed6\u7528\u6237\uff0c\u8bfb\u53d6\u7cfb\u7edf\u6587\u4ef6\u6216\u540e\u53f0\u6570\u636e\u5e93\u3002"}, {"level": "\u4f4e", "range": "0 〈= \u6f0f\u6d1e\u98ce\u9669\u503c 〈 4", "description": "\u653b\u51fb\u8005\u53ef\u4ee5\u83b7\u53d6\u67d0\u4e9b\u7cfb\u7edf\u3001\u6587\u4ef6\u7684\u4fe1\u606f\u6216\u5192\u7528\u8eab\u4efd\u3002"}], "scoreInfo": {"1": "\u53ef\u8fdc\u7a0b\u83b7\u53d6Web\u670d\u52a1\u5668\u7ec4\u4ef6\u7684\u7248\u672c\u4fe1\u606f\u3002", "2": "\u76ee\u6807Web\u670d\u52a1\u5668\u5f00\u653e\u4e86\u4e0d\u5fc5\u8981\u7684\u670d\u52a1\u3002", "3": "\u53ef\u8fdc\u7a0b\u8bbf\u95ee\u5230\u67d0\u4e9b\u4e0d\u5728\u76ee\u5f55\u6811\u4e2d\u7684\u6587\u4ef6\u6216\u8bfb\u53d6\u670d\u52a1\u5668\u52a8\u6001\u811a\u672c\u7684\u6e90\u7801\u3002", "4": "\u53ef\u8fdc\u7a0b\u56e0\u4e3a\u4f1a\u8bdd\u7ba1\u7406\u7684\u95ee\u9898\u5bfc\u81f4\u8eab\u4efd\u5192\u7528\u3002", "5": "\u53ef\u8fdc\u7a0b\u5229\u7528\u53d7\u5f71\u54cd\u7684Web\u670d\u52a1\u5668\u653b\u51fb\u5176\u4ed6\u6d4f\u89c8\u7f51\u7ad9\u7684\u7528\u6237\u3002", "6": "\u53ef\u8fdc\u7a0b\u8bfb\u53d6\u7cfb\u7edf\u6587\u4ef6\u6216\u540e\u53f0\u6570\u636e\u5e93\u3002", "7": "\u53ef\u8fdc\u7a0b\u8bfb\u5199\u7cfb\u7edf\u6587\u4ef6\u3001\u64cd\u4f5c\u540e\u53f0\u6570\u636e\u5e93\u3002", "8": "\u53ef\u8fdc\u7a0b\u4ee5\u666e\u901a\u7528\u6237\u8eab\u4efd\u6267\u884c\u547d\u4ee4\u6216\u8fdb\u884c\u62d2\u7edd\u670d\u52a1\u653b\u51fb\u3002", "9": "\u53ef\u8fdc\u7a0b\u4ee5\u7ba1\u7406\u7528\u6237\u8eab\u4efd\u6267\u884c\u547d\u4ee4\uff08\u53d7\u9650\u3001\u4e0d\u592a\u5bb9\u6613\u5229\u7528\uff09\u3002", "10": "\u53ef\u8fdc\u7a0b\u4ee5\u7ba1\u7406\u7528\u6237\u8eab\u4efd\u6267\u884c\u547d\u4ee4\uff08\u4e0d\u53d7\u9650\u3001\u5bb9\u6613\u5229\u7528\uff09\u3002"}, "descriptions": []}}}, {"title": "\u9875\u9762\u98ce\u9669\u7ea7\u522b\u8bc4\u5b9a\u6807\u51c6", "title_key": "\u9875\u9762\u98ce\u9669\u7ea7\u522b\u8bc4\u5b9a\u6807\u51c6", "mark": "page-standard", "url": "/interface/report/web-scan/refer-standard-page/1029", "data": {"standard": {"title": "\u9875\u9762\u98ce\u9669\u7ea7\u522b\u8bc4\u5b9a\u6807\u51c6", "data": [{"level": "\u9ad8\u98ce\u9669", "description": "\u9875\u9762\u5305\u542b\u7684\u6f0f\u6d1e\u6700\u9ad8\u7ea7\u522b\u4e3a\u9ad8\u5371"}, {"level": "\u4e2d\u98ce\u9669", "description": "\u9875\u9762\u5305\u542b\u7684\u6f0f\u6d1e\u6700\u9ad8\u7ea7\u522b\u4e3a\u4e2d\u5371"}, {"level": "\u4f4e\u98ce\u9669", "description": "\u9875\u9762\u5305\u542b\u7684\u6f0f\u6d1e\u6700\u9ad8\u7ea7\u522b\u4e3a\u4f4e\u5371"}, {"level": "\u65e0\u98ce\u9669", "description": "\u9875\u9762\u4e0d\u5305\u542b\u4efb\u4f55\u6f0f\u6d1e"}], "descriptions": []}}}, {"title": "\u7ad9\u70b9\u98ce\u9669\u7b49\u7ea7\u8bc4\u5b9a\u6807\u51c6", "title_key": "\u7ad9\u70b9\u98ce\u9669\u7b49\u7ea7\u8bc4\u5b9a\u6807\u51c6", "mark": "site-standard", "url": "/interface/report/web-scan/refer-standard-site/1029", "data": {"standard": {"title": "\u7ad9\u70b9\u98ce\u9669\u7b49\u7ea7\u8bc4\u5b9a\u6807\u51c6", "data": [{"risk": "\u975e\u5e38\u5371\u9669", "range": "8.0 〈= \u7ad9\u70b9\u98ce\u9669\u503c 〈= 10.0"}, {"risk": "\u6bd4\u8f83\u5371\u9669", "range": "5.0 〈= \u7ad9\u70b9\u98ce\u9669\u503c 〈 8.0"}, {"risk": "\u6bd4\u8f83\u5b89\u5168", "range": "1.0 〈= \u7ad9\u70b9\u98ce\u9669\u503c 〈 5.0"}, {"risk": "\u975e\u5e38\u5b89\u5168", "range": "0.0 〈= \u7ad9\u70b9\u98ce\u9669\u503c 〈 1.0"}], "descriptions": ["\u6309\u7167\u7f51\u7edc\u5b89\u5168\u6f0f\u6d1e\u626b\u63cf\u7cfb\u7edf\u7684\u7ad9\u70b9\u98ce\u9669\u8bc4\u4f30\u6a21\u578b\u8ba1\u7b97\u6bcf\u4e2a\u7ad9\u70b9\u7684\u7ad9\u70b9\u98ce\u9669\u503c\u3002\u6839\u636e\u5f97\u5230\u7684\u7ad9\u70b9\u98ce\u9669\u503c\u53c2\u8003\u201c\u7ad9\u70b9\u98ce\u9669\u7b49\u7ea7\u8bc4\u5b9a\u6807\u51c6\u201d\u6807\u8bc6\u7ad9\u70b9\u98ce\u9669\u7b49\u7b49\u7ea7\u3002", "\u5c06\u7ad9\u70b9\u98ce\u9669\u7b49\u7ea7\u6309\u7167\u98ce\u9669\u503c\u7684\u9ad8\u4f4e\u8fdb\u884c\u6392\u5e8f\uff0c\u5f97\u5230\u975e\u5e38\u5371\u9669\u3001\u6bd4\u8f83\u5371\u9669\u3001\u6bd4\u8f83\u5b89\u5168\u3001\u975e\u5e38\u5b89\u5168\u56db\u79cd\u7ad9\u70b9\u98ce\u9669\u7b49\u7ea7\u3002"]}}}, {"title": "\u5b89\u5168\u5efa\u8bae", "title_key": "\u5b89\u5168\u5efa\u8bae", "mark": "suggestion", "url": "/interface/report/web-scan/refer-standard-suggest/1029", "data": {"standard": {"title": "\u5b89\u5168\u5efa\u8bae", "header": [], "items": [{"title": "\u968f\u7740\u8d8a\u6765\u8d8a\u591a\u7684\u7f51\u7edc\u8bbf\u95ee\u901a\u8fc7Web\u754c\u9762\u8fdb\u884c\u64cd\u4f5c\uff0cWeb\u5b89\u5168\u5df2\u7ecf\u6210\u4e3a\u4e92\u8054\u7f51\u5b89\u5168\u7684\u4e00\u4e2a\u70ed\u70b9\uff0c\u57fa\u4e8eWeb\u7684\u653b\u51fb\u5e7f\u4e3a\u6d41\u884c\uff0cSQL\u6ce8\u5165\u3001\u8de8\u7ad9\u811a\u672c\u7b49Web\u5e94\u7528\u5c42\u6f0f\u6d1e\u7684\u5b58\u5728\u4f7f\u5f97\u7f51\u7ad9\u6ca6\u9677\u3001\u9875\u9762\u7be1\u6539\u3001\u7f51\u9875\u6302\u9a6c\u7b49\u653b\u51fb\u884c\u4e3a\u56f0\u6270\u7740\u7f51\u7ad9\u7ba1\u7406\u8005\u5e76\u5a01\u80c1\u7740\u7f51\u7ad9\u4ee5\u53ca\u76f4\u63a5\u7528\u6237\u7684\u5b89\u5168\u3002\u57fa\u4e8e\u6b64\uff0c\u6211\u4eec\u53ef\u4ece\u5982\u4e0b\u51e0\u4e2a\u65b9\u9762\u6765\u6d88\u9664\u8fd9\u4e9b\u98ce\u9669\uff0c\u505a\u5230\u9632\u60a3\u4e8e\u672a\u7136\uff1a", "data": ["\u5bf9\u7f51\u7ad9\u7684\u5f00\u53d1\u4eba\u5458\u8fdb\u884c\u5b89\u5168\u7f16\u7801\u65b9\u9762\u7684\u57f9\u8bad\uff0c\u5728\u5f00\u53d1\u8fc7\u7a0b\u907f\u514d\u6f0f\u6d1e\u7684\u5f15\u5165\u80fd\u8d77\u5230\u4e8b\u534a\u529f\u500d\u7684\u6548\u679c\u3002", "\u8bf7\u4e13\u4e1a\u7684\u5b89\u5168\u7814\u7a76\u4eba\u5458\u6216\u5b89\u5168\u516c\u53f8\u5bf9\u67b6\u6784\u7f51\u7ad9\u7684\u7a0b\u5e8f\u548c\u4ee3\u7801\u505a\u5168\u9762\u7684\u6e90\u7801\u5ba1\u8ba1\uff0c\u4fee\u8865\u6240\u6709\u53d1\u73b0\u7684\u5b89\u5168\u6f0f\u6d1e\uff0c\u8fd9\u79cd\u767d\u76d2\u5b89\u5168\u6d4b\u8bd5\u6bd4\u8f83\u5168\u9762\u3001\u6df1\u5165\uff0c\u80fd\u53d1\u73b0\u7edd\u5927\u90e8\u5206\u7684\u5b89\u5168\u95ee\u9898\u3002", "\u5728\u7f51\u7ad9\u4e0a\u7ebf\u524d\uff0c\u4f7f\u7528Web\u5e94\u7528\u6f0f\u6d1e\u626b\u63cf\u7cfb\u7edf\u8fdb\u884c\u5b89\u5168\u8bc4\u4f30\uff0c\u5e76\u4fee\u8865\u53d1\u73b0\u7684\u95ee\u9898\uff1b\u5728\u7f51\u7ad9\u4e0a\u7ebf\u540e\uff0c\u575a\u6301\u66f4\u65b0\u5e76\u4f7f\u7528\u7f51\u7ad9\u5b89\u5168\u76d1\u6d4b\u7cfb\u7edf\uff0c\u5bf9\u6574\u7ad9\u4ee5\u53ca\u5173\u952e\u9875\u9762\u8fdb\u884c\u5468\u671f\u548c\u5b9e\u65f6\u76d1\u6d4b\uff0c\u53ca\u65f6\u6d88\u9664\u53d1\u73b0\u7684\u9690\u60a3\u3002", "\u91c7\u7528\u4e13\u4e1a\u7684Web\u5b89\u5168\u9632\u706b\u5899\u4ea7\u54c1\uff0c\u53ef\u4ee5\u5728\u4e0d\u4fee\u6539\u7f51\u7ad9\u672c\u8eab\u7684\u60c5\u51b5\u4e0b\u5bf9\u5927\u591a\u6570\u7684Web\u653b\u51fb\u8d77\u5230\u6709\u6548\u7684\u963b\u65ad\u4f5c\u7528\uff0c\u7eff\u76df\u79d1\u6280\u63d0\u4f9b\u4e86\u529f\u80fd\u5f3a\u5927\u7684WAF\u4ea7\u54c1\uff0c\u53ef\u4ee5\u6ee1\u8db3\u7528\u6237\u5728\u8fd9\u65b9\u9762\u7684\u9700\u6c42\u3002", "\u5efa\u8bae\u7f51\u7edc\u7ba1\u7406\u5458\u3001\u7cfb\u7edf\u7ba1\u7406\u5458\u3001\u5b89\u5168\u7ba1\u7406\u5458\u5173\u6ce8\u5b89\u5168\u4fe1\u606f\u3001\u5b89\u5168\u52a8\u6001\u53ca\u6700\u65b0\u7684\u4e25\u91cd\u6f0f\u6d1e\uff0c\u7279\u522b\u662f\u5f71\u54cd\u5230Web\u7ad9\u70b9\u6240\u4f7f\u7528\u7684\u7cfb\u7edf\u548c\u8f6f\u4ef6\u7684\u6f0f\u6d1e\uff0c\u5e94\u8be5\u5728\u4e8b\u524d\u8bbe\u8ba1\u597d\u5e94\u5bf9\u89c4\u5212\uff0c\u4e00\u65e6\u53d1\u73b0\u7cfb\u7edf\u53d7\u6f0f\u6d1e\u5f71\u54cd\u53ca\u65f6\u91c7\u53d6\u63aa\u65bd\u3002"]}]}}}]}], "taskType": 8, "printPageHeight": 1459, "exportTypeTemplate": "html", "reportTitle": "\u7eff\u76df\u79d1\u6280\"\u7f51\u7edc\u5b89\u5168\u6f0f\u6d1e\u626b\u63cf\u7cfb\u7edf\"\u5b89\u5168\u8bc4\u4f30\u62a5\u544a", "companyName": "\u7eff\u76df\u79d1\u6280", "createTime": "2025-06-20 17:46:55", "curr_lang": "zh-CN", "is_jump_to_host": "1", "auth_permission": {"dashboard": 1, "task": 1, "task_createTask": 1, "task_web_scan": 1, "task_guess": 1, "task_vul": 1, "task_vul_config": 1, "task_code_audit": 2, "task_image": 2, "task_host_assets": 2, "task_web_assets": 2, "alarm": 1, "assets": 1, "assets_list": 1, "assets_library": 1, "assert_operate": 1, "scanManagement": 1, "scanPolicy": 1, "taskList": 1, "reportCenter": 1, "reportManagement": 1, "reportTemplate": 1, "system_management": 1, "system_status": 1, "system_accounts": 1, "system_config": 1, "system_serve": 1, "system_tools": 1, "knowledge": 1, "knowledge_vulnLibrary": 1, "knowledge_template": 1, "knowledge_vulnTemplate": 1, "knowledge_statusTemplate": 1, "knowledge_defectTemplate": 0, "knowledge_imageTemplate": 0, "knowledge_configtemplate": 1, "knowledge_offlineReinforceTool": 0, "knowledge_offlineCheckTool": 1, "knowledge_ReinforeLib": 0, "knowledge_passwordDictionary": 1, "knowledge_portList": 1, "authentication": 1, "log_management": 0, "log_audit": 0, "log_config": 0, "agent_management": 1, "safe_center": 1, "agent_config": 1, "vuln_verify": 0, "certificatManagement": 1, "web_viewPluginTemplate": 1, "viewPluginTemplate": 1, "viewReportTemplate": 1, "edit_reportTemplate": 1, "errorCorrect": 1}, "is_oem": false, "report_logo": null};</script><title></title><script defer="defer" src="assets/js/main.d4a6e50628e965221154.js"></script></head><body><div id="app"></div></body></html>