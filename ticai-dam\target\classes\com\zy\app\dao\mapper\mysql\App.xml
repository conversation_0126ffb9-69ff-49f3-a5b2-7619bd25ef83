<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.app.dao.AppDAO">
    <!-- 菜单信息 -->
    <sql id="menu">
        a.CODE_
        ,a.PCODE_
        ,a.TYPE_
        ,a.NAME_
        ,a.ICON_
        ,a.PATH_
        ,case a.ORD_ when 0 then 999999 else a.ORD_ end seq_
    </sql>

    <select id="findLogin" resultType="com.zy.app.vo.User">
        select a.ID_,a.ACCOUNT_,a.NAME_,a.GENDER_,a.DEPT_,a.STATUS_,a.MP_
        ,b.PID_ parent_dept, b.NAME_ dept_name, b.TYPE_ dept_type
        from SYS_USER a left join SYS_DEPT b on a.DEPT_=b.ID_
        where a.FLAG_='1' and a.ACCOUNT_=#{0} and a.PASSWORD_=#{1}
    </select>

    <select id="findDeptIdTree" resultType="com.zy.model.TreeNode">
        select ID_,PID_ from SYS_DEPT where FLAG_='1' order by ORD_
    </select>

    <select id="findPassword" resultType="String">
        select PASSWORD_ from SYS_USER where ID_=#{0}
    </select>

    <update id="updatePassword">
        update SYS_USER set PASSWORD_=#{1},MP_='0' where ID_=#{0}
    </update>

    <select id="findAllMenu" resultType="com.zy.app.vo.Menu">
        select
        <include refid="menu"/>
        from SYS_MENU a
        where a.TYPE_ in ('1','2')
        order by seq_,a.CODE_
    </select>

    <select id="findUserMenu" resultType="com.zy.app.vo.Menu">
        select
        <include refid="menu"/>
        from SYS_MENU a
        where (a.TYPE_='1' OR a.TYPE_='2' AND a.CODE_ in (select MENU_ from SYS_ROLE_MENU o,SYS_USER_ROLE p where o.ROLE_=p.ROLE_ and p.USER_=#{0}))
        order by seq_,a.CODE_
    </select>

    <select id="findOpt" resultType="String">
        select a.CODE_
        from SYS_MENU a
        where (a.TYPE_='3' AND a.CODE_ in (select MENU_ from SYS_ROLE_MENU o,SYS_USER_ROLE p where o.ROLE_=p.ROLE_ and p.USER_=#{0}))
    </select>

    <select id="findAllCode" resultType="com.zy.model.TypeKeyValue">
        select DICT_ type_, CODE_ key_, NAME_ value_ from SYS_CODE where STATUS_='1' order by ORD_,CODE_
    </select>

</mapper>
