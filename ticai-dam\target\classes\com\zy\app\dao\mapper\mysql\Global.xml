<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.app.dao.GlobalDAO">
    <select id="getNow" resultType="java.util.Date">
        select now()
    </select>

    <select id="findCode" resultType="com.zy.model.ValueLabel">
        select CODE_ value, NAME_ label from SYS_CODE where DICT_=#{0} order by ORD_ desc, CODE_
    </select>

    <select id="findRegion" resultType="com.zy.model.ValueLabel">
        select CODE_ value_, NAME_ label,IF(LENGTH(CODE_)=12, 1, NULL) leaf from SYS_REGION where CODE_=#{0}
    </select>

    <select id="findRegionChildren" resultType="com.zy.model.ValueLabel">
        select CODE_ value_, NAME_ label,IF(LENGTH(CODE_)=12, 1, NULL) leaf from SYS_REGION where PCODE_=#{0} order by ORD_ desc,CODE_
    </select>

</mapper>
