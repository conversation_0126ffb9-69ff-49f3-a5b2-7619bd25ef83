<?xml version="1.0" encoding="UTF-8"?>
<svg width="59px" height="104px" viewBox="0 0 59 104" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 120</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="56.6144349" height="73.544741"></rect>
        <filter x="-42.4%" y="-32.6%" width="184.8%" height="165.3%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="8" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="73.2828776%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#626262" offset="0%"></stop>
            <stop stop-color="#6A6A6A" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="50%" cy="20.4549154%" fx="50%" fy="20.4549154%" r="129.904575%" gradientTransform="translate(0.500000,0.204549),scale(1.000000,0.769796),rotate(90.000000),translate(-0.500000,-0.204549)" id="radialGradient-5">
            <stop stop-color="#F0F1F3" offset="0%"></stop>
            <stop stop-color="#7E7E7E" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.301245629" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="85.4594624%" id="linearGradient-7">
            <stop stop-color="#7E7E7E" offset="0%"></stop>
            <stop stop-color="#C0C0C0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-8">
            <stop stop-color="#FFFFFF" offset="0.0245847902%"></stop>
            <stop stop-color="#828282" offset="100%"></stop>
        </linearGradient>
        <path d="M28.3177277,18.3861852 C29.0324259,18.3861852 29.621001,18.9752163 29.621001,19.6904684 L29.621001,19.6904684 L29.621001,23.1405077 C36.1373672,23.7505757 41.3294396,28.9466715 41.9390352,35.4680873 L41.9390352,35.4680873 L45.3653825,35.4680873 C46.1011013,35.4680873 46.6686558,36.0571184 46.6686558,36.7723705 C46.6686558,37.4876225 46.0800808,38.0766537 45.3653825,38.0766537 L45.3653825,38.0766537 L41.9390352,38.0766537 C41.3294396,44.5980695 36.1373672,49.7941653 29.621001,50.4042332 L29.621001,50.4042332 L29.621001,53.8542726 C29.621001,54.5695246 29.0324259,55.1585557 28.3177277,55.1585557 C27.6030295,55.1585557 27.0144545,54.5695246 27.0144545,53.8542726 L27.0144545,53.8542726 L27.0144545,50.4042332 C20.4980883,49.7941653 15.3060158,44.5980695 14.6964203,38.0766537 L14.6964203,38.0766537 L11.2490523,38.0766537 C10.5343541,38.0766537 9.9457791,37.4876225 9.9457791,36.7723705 C9.9457791,36.0571184 10.5343541,35.4680873 11.2490523,35.4680873 L11.2490523,35.4680873 L14.6964203,35.4680873 C15.3060158,28.9466715 20.4980883,23.7505757 27.0144545,23.1405077 L27.0144545,23.1405077 L27.0144545,19.6904684 C27.0144545,18.9752163 27.6030295,18.3861852 28.3177277,18.3861852 Z M29.9421308,41.0244463 L26.4454779,41.0244463 L26.4454779,44.3109345 L29.9421308,44.3109345 L29.9421308,41.0244463 Z M29.1562976,28.3901987 C25.586544,28.2957769 23.5184017,29.8674431 22.9457791,33.1021517 L22.9457791,33.1021517 L26.1591666,33.8148841 C26.4911659,32.0543742 27.322687,31.1741192 28.6567758,31.1741192 C29.6558195,31.268541 30.2040752,31.8167967 30.298497,32.8158404 C30.344185,33.5773066 29.7715624,34.4362405 28.583675,35.3865504 C27.3013659,36.2911722 26.6800095,37.4821054 26.7287433,38.956304 L26.7287433,38.956304 L26.7287433,39.7421372 L29.5857646,39.7421372 L29.5857646,39.1695146 C29.4426089,38.3136265 30.0365526,37.4333716 31.3706414,36.5287497 C33.1798852,35.2921285 34.0357732,33.9123517 33.9383055,32.3894193 C33.7007281,29.8643973 32.1077407,28.5333543 29.1562976,28.3901987 Z" id="path-9"></path>
        <filter x="-10.9%" y="-10.9%" width="121.8%" height="121.8%" filterUnits="objectBoundingBox" id="filter-10">
            <feGaussianBlur stdDeviation="3" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.650103802 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="1-仪表盘" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-89备份" transform="translate(1.000000, 2.000000)">
            <g id="路径" transform="translate(0.000000, 28.345369)">
                <mask id="mask-2" fill="white">
                    <use xlink:href="#path-1"></use>
                </mask>
                <g id="蒙版"></g>
                <path d="M28.4527974,-22.599686 L29.8560266,-21.3490375 C35.193959,-16.5858412 43.4879737,-13.5190693 54.5197008,-12.2395278 L56.6144349,-11.9918746 L56.6144349,21.6476831 C56.6144349,32.7920765 47.4065014,42.5000814 29.2373117,50.5116619 L28.2546469,50.9450549 L27.2922015,50.4745139 C8.92486129,41.4723206 2.27373675e-13,32.0449894 2.27373675e-13,21.6476831 L2.27373675e-13,-12.2560379 L2.35758681,-12.2560379 C10.3038272,-12.2560379 18.5735785,-15.3599578 26.9363394,-21.4893743 L28.4527974,-22.599686 Z" fill="#959595" fill-rule="nonzero" opacity="0.854106105" filter="url(#filter-3)" mask="url(#mask-2)"></path>
            </g>
            <path d="M28.4527974,6.89481947 L29.8560266,8.14546803 C35.193959,12.9086643 43.4879737,15.9754362 54.5197008,17.2549777 L56.6144349,17.5026309 L56.6144349,51.1421886 C56.6144349,62.286582 47.4065014,71.9945869 29.2373117,80.0061674 L28.2546469,80.4395604 L27.2922015,79.9690194 C8.92486129,70.9668261 0,61.5394948 0,51.1421886 L0,17.2384675 L2.35758681,17.2384675 C10.3038272,17.2384675 18.5735785,14.1345477 26.9363394,8.00513124 L28.4527974,6.89481947 Z" id="路径" fill="url(#linearGradient-4)" fill-rule="nonzero"></path>
            <path d="M28.4836418,-0.642276791 L30.188925,0.877582013 C35.4667487,5.58714152 43.6752969,8.59899185 54.5784053,9.86361642 L54.5784053,9.86361642 L57.1144349,10.1634424 L57.1144349,44.2473692 C57.1144349,49.938988 54.7760512,55.2704481 50.102097,60.1982454 C45.492333,65.0583663 38.5975763,69.5304531 29.439079,73.5688301 L29.439079,73.5688301 L28.2437987,74.0959948 L27.0721506,73.5231739 C17.8223166,68.989649 10.9548235,64.343652 6.39546755,59.5279254 C1.76400591,54.6360385 -0.5,49.5614475 -0.5,44.2473692 L-0.5,44.2473692 L-0.5,9.84364808 L2.35758681,9.84364808 C10.2109272,9.84364808 18.3785682,6.76273926 26.6409617,0.70688643 L26.6409617,0.70688643 L28.4836418,-0.642276791 Z" id="路径" stroke="#FFFFFF" fill="url(#radialGradient-5)" fill-rule="nonzero"></path>
            <path d="M28.1576457,6.00429583 L29.0575375,6.80437172 C33.6882627,10.9263782 40.8755176,13.6006935 50.4500682,14.7084978 L50.4500682,14.7084978 L51.8022789,14.8679732 L51.8022789,43.1572652 C51.8022789,47.7973046 49.8676168,52.1316105 46.0530074,56.1435345 C42.1738219,60.223375 36.363775,63.9651391 28.6588437,67.354249 L28.6588437,67.354249 L28.0296011,67.6310891 L27.414282,67.3309962 C19.6170492,63.5187862 13.8192832,59.6219902 9.97507931,55.5715803 C6.20311629,51.5972862 4.32529965,47.4823888 4.32529965,43.1572652 L4.32529965,43.1572652 L4.32529965,14.6978368 L5.84401956,14.6978368 C12.7408802,14.6978368 19.9239104,12.0236082 27.1848597,6.71479911 L27.1848597,6.71479911 L28.1576457,6.00429583 Z" id="路径" stroke="url(#linearGradient-6)" fill-rule="nonzero"></path>
            <g id="形状结合" fill-rule="nonzero">
                <use fill="url(#linearGradient-7)" xlink:href="#path-9"></use>
                <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                <path stroke="url(#linearGradient-8)" stroke-width="1" d="M28.3177277,17.8861852 C28.7791392,17.8861852 29.2029944,18.0627632 29.5236828,18.3526011 C29.8124055,18.6135484 30.0167751,18.9660306 30.09064,19.3617763 L30.1141718,19.5338897 L30.1201532,22.6923887 C33.2576294,23.0880918 36.0768155,24.511938 38.2293774,26.6160606 C40.3070434,28.6469726 41.7634686,31.3115213 42.2860536,34.2961942 L42.3488953,34.6872891 L45.3901871,34.9682497 C45.885612,34.9747431 46.3237958,35.175506 46.6415066,35.493463 C46.968854,35.8210641 47.1686558,36.2743554 47.1686558,36.7723705 C47.1686558,37.2339447 46.992242,37.6579226 46.7027594,37.9787144 C46.4419088,38.2677774 46.0895355,38.4723363 45.6939408,38.5462666 L45.5218948,38.5698189 L42.3872347,38.5757994 C41.9919,41.7156614 40.5691662,44.5369722 38.4667208,46.691153 C36.4373903,48.7704198 33.7749351,50.2279602 30.7926266,50.7510385 L30.4018417,50.813941 L30.1208322,53.8789853 C30.1142073,54.3639783 29.9128975,54.8059697 29.5910665,55.1280501 C29.2636365,55.4557338 28.8122173,55.6585557 28.3177277,55.6585557 C27.8563162,55.6585557 27.432461,55.4819778 27.1117726,55.1921399 C26.8230499,54.9311925 26.6186804,54.5787104 26.5448154,54.1829646 L26.5212836,54.0108513 L26.5153022,50.8523522 C23.3778261,50.4566492 20.55864,49.0328029 18.406078,46.9286804 C16.3284616,44.8978168 14.8720563,42.2333468 14.3494392,39.2487603 L14.2865931,38.8576768 L11.2242979,38.5764841 C10.7395097,38.5698399 10.2976717,38.3683556 9.97571359,38.046148 C9.64842442,37.7186052 9.4457791,37.2670496 9.4457791,36.7723705 C9.4457791,36.3107963 9.62219294,35.8868184 9.91167549,35.5660266 C10.1725266,35.2769629 10.5249009,35.0724037 10.9204967,34.9984739 L11.0925431,34.9749218 L14.2482214,34.9689365 C14.643557,31.8290766 16.0662904,29.0077676 18.1687346,26.853588 C20.1980651,24.7743212 22.8605203,23.3167808 25.8428288,22.7937025 L26.2336138,22.7308 L26.5146232,19.6657556 C26.5212481,19.1807627 26.722558,18.7387713 27.044389,18.4166909 C27.3718189,18.0890072 27.8232382,17.8861852 28.3177277,17.8861852 Z M29.4421308,41.5244463 L26.9454779,41.5244463 L26.9454779,43.8109345 L29.4421308,43.8109345 L29.4421308,41.5244463 Z M28.8210506,28.8862112 L28.5124789,28.8919383 C27.1939731,28.9373591 26.1179022,29.2517788 25.2895312,29.8802682 C24.455417,30.513115 23.8842259,31.459379 23.5484372,32.7236706 L23.5484372,32.7236706 L25.7816074,33.2189898 L25.8611061,32.9471447 C26.0576136,32.3317468 26.3306659,31.8483875 26.6691628,31.4904891 C27.1783294,30.9521385 27.8380263,30.6741192 28.7038222,30.6763375 C29.340488,30.7365102 29.8239851,30.9557553 30.170423,31.3021932 C30.5168608,31.648631 30.736106,32.1321281 30.7975994,32.7858942 C30.8496239,33.6529693 30.2808667,34.6691095 28.8719023,35.795115 C27.7970954,36.5533517 27.2485112,37.5393758 27.2287122,39.2421372 L27.2287122,39.2421372 L29.0857646,39.2081893 L29.0677937,39.0309633 C29.0406531,38.5572286 29.1806999,38.0745993 29.5102974,37.5869525 C29.8360289,37.1050255 30.358667,36.6108416 31.0900294,36.1149178 C32.6984924,35.0155264 33.5241598,33.8020717 33.4398319,32.4294144 C33.3344837,31.3230222 32.9466011,30.4675498 32.2445403,29.8804123 C31.5182989,29.2730525 30.4771997,28.95567 29.1382152,28.8899113 L29.1382152,28.8899113 L28.8210506,28.8862112 Z"></path>
            </g>
            <g id="编组-119" transform="translate(22.884942, 29.443014)"></g>
        </g>
    </g>
</svg>