<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.sys.dao.SysMenuDAO">

    <sql id="meta">
        a.CODE_
        ,a.PCODE_
        ,a.TYPE_
        ,a.NAME_
        ,a.ICON_
        ,a.PATH_
        ,a.ORD_
    </sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.sys.orm.SysMenu">
        insert into SYS_MENU(CODE_, PCODE_, TYPE_, NAME_, ICON_, PATH_, ORD_)
        values (#{code}, #{pcode}, #{type}, #{name}, #{icon}, #{path}, #{ord})
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.sys.orm.SysMenu">
        update SYS_MENU
        set TYPE_=#{type},
        NAME_=#{name},
        ICON_=#{icon},
        PATH_=#{path},
        ORD_=#{ord}
        where CODE_ = #{code}
    </update>

    <select id="findAll" resultType="com.zy.sys.vo.MenuVo">
        select
        <include refid="meta"/>
        from SYS_MENU a
        order by a.ORD_,a.CODE_
    </select>

    <!-- 获取唯一的系统菜单信息数据 -->
    <select id="findOne" resultType="com.zy.sys.orm.SysMenu">
        select
        <include refid="meta"/>
        from SYS_MENU a
        where a.CODE_=#{0}
    </select>

    <!-- 删除 -->
    <delete id="delete">
        delete
        from SYS_MENU
        where CODE_ = #{0}
    </delete>
</mapper>
