<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.patrol.dao.AmPatrolPathDAO">

    <sql id="meta">
			a.ID_
			,a.NO_
			,a.NAME_
			,a.MEMO_
			,a.DISTANCE_
			,a.RADIUS_
			,a.ORD_
			,a.FLAG_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.patrol.orm.AmPatrolPath">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into AM_PATROL_PATH(ID_,NO_,NAME_,MEMO_,DISTANCE_,RADIUS_,ORD_,FLAG_)
        values(#{id},#{no},#{name},#{memo},#{distance},#{radius},#{ord},'1')
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.dam.patrol.orm.AmPatrolPath">
		update AM_PATROL_PATH
		set NO_=#{no},NAME_=#{name},MEMO_=#{memo},DISTANCE_=#{distance},RADIUS_=#{radius},ORD_=#{ord}
		where ID_=#{id}
	</update>

    <select id="findIdByNo" resultType="String">
        select ID_ from AM_PATROL_PATH where FLAG_='1' and NO_=#{0}
    </select>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.patrol.vo.PatrolPathVo">
        select
        <include refid="meta"/>
        ,(select count(0) from AM_PATROL_PATH_POINT m where m.PATH_=a.ID_) point_count
        from AM_PATROL_PATH a
        where a.FLAG_='1'
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.NAME_ like concat('%',#{keyword},'%'))</if>
        order by a.ORD_,a.NO_
    </select>

    <select id="listSimple" resultType="com.zy.model.IdNoName">
        select a.ID_,a.NO_,a.NAME_
        from AM_PATROL_PATH a
        where a.FLAG_='1'
        order by a.ORD_,a.NO_
    </select>

    <!-- 获取唯一的资管-巡检路线数据 -->
    <select id="findOne" resultType="com.zy.dam.patrol.orm.AmPatrolPath">
        select
        <include refid="meta"/>
        from AM_PATROL_PATH a where a.ID_=#{0}
    </select>

    <!-- 删除 -->
    <update id="delete">
		update AM_PATROL_PATH set FLAG_='9' where ID_=#{0}
	</update>

    <select id="findVo" resultType="com.zy.dam.patrol.vo.PatrolPathVo">
        select
        <include refid="meta"/>
        ,(select count(0) from AM_PATROL_PATH_POINT m where m.PATH_=a.ID_) point_count
        from AM_PATROL_PATH a where a.ID_=#{0}
    </select>
</mapper>
