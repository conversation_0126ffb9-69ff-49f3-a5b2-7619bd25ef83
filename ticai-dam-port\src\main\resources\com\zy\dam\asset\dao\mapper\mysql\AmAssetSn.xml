<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.asset.dao.AmAssetSnDAO">
    <!-- 根据资产ID查询终端号 -->
    <select id="findOne" resultType="com.zy.dam.asset.orm.AmAssetSn">
        SELECT
            ID_ AS id,
            ASSET_ AS asset,
            LAST_SN_ AS lastSn,
            LAST_ASSET_ AS lastAsset,
            NOW_SN_ AS nowSn,
            USER_ AS user,
            TIME_ AS time,
            FLAG_ AS flag
        FROM AM_ASSET_SN
        WHERE ASSET_ = #{asset} AND FLAG_ = '1'
        ORDER BY TIME_ DESC
        LIMIT 1
    </select>
</mapper>
