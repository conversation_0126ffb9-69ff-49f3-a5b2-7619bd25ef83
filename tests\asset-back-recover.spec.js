const { test, expect } = require('@playwright/test');

test.describe('资产退库和还原功能测试', () => {
  let page;
  
  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    
    // 登录系统
    await page.goto('http://localhost:8080/login');
    await page.fill('input[placeholder="请输入用户名"]', 'admin');
    await page.fill('input[placeholder="请输入密码"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // 等待登录成功
    await page.waitForURL('**/home');
    
    // 导航到资产退库页面
    await page.goto('http://localhost:8080/#/asset/back');
    await page.waitForLoadState('networkidle');
  });

  test('资产退库和还原流程测试', async () => {
    // 1. 首先创建一个退库申请
    await page.click('button:has-text("新增")');
    
    // 等待退库申请对话框打开
    await page.waitForSelector('.el-dialog__title:has-text("退库申请")');
    
    // 填写退库信息
    await page.fill('input[placeholder="请选择退库日期"]', '2024-01-15');
    await page.click('input[placeholder="请选择退库人"]');
    await page.waitForTimeout(1000);
    await page.click('.el-select-dropdown__item:first-child');
    
    // 选择退至部门/区域
    await page.click('input[placeholder="请选择退至部门/区域"]');
    await page.waitForTimeout(1000);
    await page.click('.el-select-dropdown__item:first-child');
    
    // 填写退库说明
    await page.fill('input[placeholder="请输入退库说明"]', '测试退库申请');
    
    // 添加退库资产 - 这里需要根据实际的资产选择组件进行操作
    await page.click('button:has-text("选择资产")');
    await page.waitForTimeout(1000);
    
    // 选择第一个资产
    await page.click('.el-table__row:first-child .el-checkbox');
    await page.click('button:has-text("确定")');
    
    // 提交退库申请
    await page.click('button:has-text("保存")');
    
    // 等待成功提示
    await page.waitForSelector('.el-message--success');
    
    // 2. 验证退库记录已创建
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // 查找刚创建的退库记录
    const firstRow = page.locator('.el-table__row').first();
    await expect(firstRow).toBeVisible();
    
    // 获取退库记录的状态，应该是"已通过"
    const statusCell = firstRow.locator('td').nth(4); // 假设状态在第5列
    await expect(statusCell).toContainText('已通过');
    
    // 3. 记录退库前的资产信息
    // 点击退库单号查看详情
    await firstRow.locator('.el-link').click();
    
    // 等待详情对话框打开
    await page.waitForSelector('.el-dialog__title:has-text("退库申请信息")');
    
    // 获取资产详情信息
    const assetTable = page.locator('.el-table tbody tr').first();
    const assetName = await assetTable.locator('td').nth(1).textContent(); // 资产名称
    const assetNo = await assetTable.locator('td').nth(2).textContent(); // 资产编码
    const originalDept = await assetTable.locator('td').nth(5).textContent(); // 原使用部门
    const originalRegion = await assetTable.locator('td').nth(6).textContent(); // 原所在区域
    
    console.log('退库前资产信息:', {
      name: assetName,
      no: assetNo,
      dept: originalDept,
      region: originalRegion
    });
    
    // 关闭详情对话框
    await page.click('.el-dialog__headerbtn');
    
    // 4. 执行还原操作
    // 选择退库记录
    await firstRow.locator('.el-checkbox').click();
    
    // 点击还原按钮
    await page.click('button:has-text("还原")');
    
    // 确认还原操作
    await page.waitForSelector('.el-message-box');
    await page.click('button:has-text("确定")');
    
    // 等待还原成功提示
    await page.waitForSelector('.el-message--success:has-text("还原记录成功")');
    
    // 5. 验证还原后的状态
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // 查找还原后的记录，状态应该变为"已还原"
    const restoredRow = page.locator('.el-table__row').first();
    const restoredStatusCell = restoredRow.locator('td').nth(4);
    await expect(restoredStatusCell).toContainText('已还原');
    
    // 6. 验证资产信息是否正确还原
    // 导航到资产台账页面查看资产状态
    await page.goto('http://localhost:8080/#/asset/account');
    await page.waitForLoadState('networkidle');
    
    // 搜索刚才退库的资产
    await page.fill('input[placeholder="请输入关键字"]', assetNo.trim());
    await page.click('button:has-text("查询")');
    await page.waitForTimeout(2000);
    
    // 查看资产详情
    const assetRow = page.locator('.el-table__row').first();
    await assetRow.locator('.el-link').click();
    
    // 等待资产详情对话框
    await page.waitForSelector('.el-dialog__title:has-text("资产详情")');
    
    // 验证使用部门和所在区域是否正确还原
    const currentDept = await page.locator('input[readonly]:has-value("' + originalDept.trim() + '")').count();
    const currentRegion = await page.locator('input[readonly]:has-value("' + originalRegion.trim() + '")').count();
    
    // 验证资产信息已正确还原
    expect(currentDept).toBeGreaterThan(0);
    expect(currentRegion).toBeGreaterThan(0);
    
    console.log('验证完成：资产退库和还原功能正常');
  });

  test('验证退库后使用部门和区域被正确清空', async () => {
    // 创建一个新的退库申请来测试退库逻辑
    await page.click('button:has-text("新增")');
    await page.waitForSelector('.el-dialog__title:has-text("退库申请")');
    
    // 填写基本信息
    await page.fill('input[placeholder="请选择退库日期"]', '2024-01-16');
    await page.click('input[placeholder="请选择退库人"]');
    await page.waitForTimeout(1000);
    await page.click('.el-select-dropdown__item:first-child');
    
    await page.click('input[placeholder="请选择退至部门/区域"]');
    await page.waitForTimeout(1000);
    await page.click('.el-select-dropdown__item:first-child');
    
    await page.fill('input[placeholder="请输入退库说明"]', '测试退库清空字段');
    
    // 选择资产
    await page.click('button:has-text("选择资产")');
    await page.waitForTimeout(1000);
    await page.click('.el-table__row:first-child .el-checkbox');
    await page.click('button:has-text("确定")');
    
    // 提交申请
    await page.click('button:has-text("保存")');
    await page.waitForSelector('.el-message--success');
    
    // 刷新页面查看结果
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // 验证退库成功，状态为"已通过"
    const newRow = page.locator('.el-table__row').first();
    const statusCell = newRow.locator('td').nth(4);
    await expect(statusCell).toContainText('已通过');
    
    console.log('退库测试完成：使用部门和区域应该被正确清空');
  });

  test.afterEach(async () => {
    await page.close();
  });
});
