<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.asset.dao.AmAssetConsumingDAO">

    <sql id="meta">
			a.ID_
			,a.NO_
			,a.USER_
			,a.TIME_
			,a.BACK_TIME_
			,a.DEPT_
			,a.RECEIVER_
			,a.REGION_
			,a.LOCATION_
			,a.MEMO_
			,a.STATUS_
			,a.CDEPT_
			,a.CUSER_
			,a.CTIME_
			,a.CHECK_USER_
			,a.CHECK_TIME_
			,a.CHECK_MEMO_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.asset.orm.AmAssetConsuming">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into
        AM_ASSET_CONSUMING(ID_,NO_,USER_,TIME_,BACK_TIME_,DEPT_,RECEIVER_,REGION_,LOCATION_,MEMO_,STATUS_,CDEPT_,CUSER_,CTIME_,CHECK_USER_,CHECK_TIME_,CHECK_MEMO_,FLAG_)
        values(#{id},#{no},#{user},#{time},#{backTime},#{dept},#{receiver},#{region},#{location},#{memo},#{status},#{cdept},#{cuser},now(),#{checkUser},#{checkTime},#{checkMemo},'1')
    </insert>

    <!-- 更新数据 -->
    <update id="updateCheck" parameterType="com.zy.dam.asset.orm.AmAssetConsuming">
		update AM_ASSET_CONSUMING
		set STATUS_=#{status},CHECK_USER_=#{checkUser},CHECK_TIME_=now(),CHECK_MEMO_=#{checkMemo}
		where ID_=#{id}
	</update>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.asset.vo.AssetConsumingVo">
        select
        <include refid="meta"/>
        ,(select count(0) from AM_ASSET_CONSUMING_DETAIL m where m.BILL_=a.ID_) asset_count
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.RECEIVER_) receiver_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,(select m.CODE_ from AM_LOCATION m where m.ID_=a.LOCATION_) location_code
        ,b.NAME_ location_name, b.CONTACT_ location_contact, b.PHONE_ location_phone
        from AM_ASSET_CONSUMING a left join AM_LOCATION b on a.LOCATION_=b.ID_
        where a.FLAG_='1'
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.MEMO_ like
            concat('%',#{keyword},'%'))
        </if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="user != null">and a.USER_=#{user}</if>
        <if test="receiver != null">and a.RECEIVER_=#{receiver}</if>
        <if test="locationCode != null">and b.CODE_=#{locationCode}</if>
        <if test="locationContact != null">and b.CONTACT_=#{locationContact}</if>
        <if test="locationPhone != null">and b.PHONE_=#{locationPhone}</if>
        <if test="locationKey != null">and (b.NAME_ like concat('%',#{locationKey},'%') or b.CONTACT_ like
            concat('%',#{locationKey},'%'))
        </if>
        <if test="begin != null">and a.TIME_ &gt;= #{begin}</if>
        <if test="end != null">and a.TIME_ &lt; date_add(#{end}, interval 1 day)</if>
        <if test="deptIds != null">
            <foreach collection="deptIds" item="deptId" open="and a.CDEPT_ in (" separator="," close=")">#{deptId}
            </foreach>
        </if>
        <if test="assetNo != null">and a.ID_ in (select a.BILL_ from AM_ASSET_CONSUMING_DETAIL a left join AM_ASSET b on
            a.ASSET_=b.ID_ where
            b.NO_
            =#{assetNo})
        </if>
        order by a.CTIME_ desc
    </select>

    <!-- 获取唯一的资管-资产领用退库数据 -->
    <select id="findOne" resultType="com.zy.dam.asset.orm.AmAssetConsuming">
        select
        <include refid="meta"/>
        from AM_ASSET_CONSUMING a where a.ID_=#{0}
    </select>

    <insert id="insertDetail">
		insert into AM_ASSET_CONSUMING_DETAIL(BILL_, ASSET_, ORD_, DEPT_, REGION_, LOCATION_, USE_DEPT_, USE_USER_, ASSET_STATUS_, DEPOSIT_STATUS_,FLAG_)
		values(#{id}, #{asset}, #{ord}, #{dept}, #{region}, #{location}, #{useDept}, #{useUser}, #{assetStatus}, '0','1')
	</insert>

    <!-- 更新资产领用，置为使用中 -->
    <update id="updateUse">
        update AM_ASSET a,AM_ASSET_CONSUMING_DETAIL b, AM_ASSET_CONSUMING c
        set a.STATUS_='2',a.USE_DEPT_=c.DEPT_,a.USE_USER_=c.RECEIVER_,a.REGION_=c.REGION_,a.LOCATION_=c.LOCATION_,a.DEPT_=c.DEPT_
        where a.ID_=b.ASSET_ and b.BILL_=c.ID_ and c.ID_=#{0}
    </update>

    <select id="findVo" resultType="com.zy.dam.asset.vo.AssetConsumingVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,(select m.NAME_ from AM_LOCATION m where m.ID_=a.LOCATION_) location_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.RECEIVER_) receiver_name
        ,(select m.CODE_ from AM_LOCATION m where m.ID_=a.LOCATION_) location_code
        ,(select m.CONTACT_ from AM_LOCATION m where m.ID_=a.LOCATION_) location_contact
        ,(select m.PHONE_ from AM_LOCATION m where m.ID_=a.LOCATION_) location_phone
        ,(select m.ADDRESS_ from AM_LOCATION m where m.ID_=a.LOCATION_) location_address
        from AM_ASSET_CONSUMING a where a.ID_=#{0}
    </select>

    <select id="findDetail" resultType="com.zy.dam.asset.vo.AssetDetailVo">
        select a.ID_,a.TYPE_,a.NAME_,a.NO_,a.BO_DATE_,a.STATUS_
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.TYPE_) type_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.USE_DEPT_) use_dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USE_USER_) use_user_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=b.DEPT_) when_dept_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=b.REGION_) when_region_name
        ,(select m.NAME_ from AM_LOCATION m where m.ID_=b.LOCATION_) when_location_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=b.USE_DEPT_) when_use_dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=b.USE_USER_) when_use_user_name
        ,b.ASSET_STATUS_ when_status
        from AM_ASSET a,AM_ASSET_CONSUMING_DETAIL b
        where a.ID_=b.ASSET_ and b.BILL_=#{0}
        order by b.ORD_
    </select>

    <select id="pageDeposit" resultType="com.zy.dam.asset.vo.DepositDetailVo">
        select a.NO_ asset_no,a.NAME_ asset_name,d.NO_,d.TIME_,b.NAME_ location_name, b.CONTACT_ location_contact,
        b.PHONE_ location_phone, b.ADDRESS_ location_address
        ,c.BILL_, c.ASSET_,c.DEPOSIT_ID_, c.DEPOSIT_NO_,c.DEPOSIT_AMOUNT_,c.DEPOSIT_DATE_, c.DEPOSIT_STATUS_,
        c.DEPOSIT_TIME_
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.TYPE_) type_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=d.RECEIVER_) receiver_name
        from AM_ASSET a,AM_LOCATION b,AM_ASSET_CONSUMING_DETAIL c,AM_ASSET_CONSUMING d
        where a.LOCATION_=b.ID_ and a.ID_=c.ASSET_ and c.BILL_=d.ID_
        <if test="assetNo != null">and a.NO_ like concat('%', #{assetNo}, '%')</if>
        <if test="locationContact != null">and b.CONTACT_ like concat('%', #{locationContact}, '%')</if>
        <if test="locationName != null">and b.NAME_ like concat('%', #{locationName}, '%')</if>
        <if test="depositStatus != null">and c.DEPOSIT_STATUS_=#{depositStatus}</if>
        <if test="begin != null">and d.TIME_ &gt;= #{begin}</if>
        <if test="end != null">and d.TIME_ &lt; date_add(#{end}, interval 1 day)</if>
        order by d.TIME_ desc
    </select>

    <select id="getDeposit" resultType="com.zy.dam.asset.vo.DepositDetailVo">
    select a.NO_ asset_no,a.NAME_ asset_name,d.NO_,d.TIME_,b.NAME_ location_name, b.CONTACT_ location_contact, b.PHONE_ location_phone, b.ADDRESS_ location_address
    ,c.BILL_, c.ASSET_,c.DEPOSIT_ID_, c.DEPOSIT_NO_,c.DEPOSIT_AMOUNT_,c.DEPOSIT_DATE_, c.DEPOSIT_STATUS_, c.DEPOSIT_TIME_
    ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.TYPE_) type_name
    ,(select m.NAME_ from SYS_USER m where m.ID_=d.RECEIVER_) receiver_name
    from AM_ASSET a,AM_LOCATION b,AM_ASSET_CONSUMING_DETAIL c,AM_ASSET_CONSUMING d
    where a.LOCATION_=b.ID_ and a.ID_=c.ASSET_ and c.BILL_=d.ID_
    and c.BILL_=#{0} and c.ASSET_=#{1}
    </select>

    <update id="updateDeposit">
        update AM_ASSET_CONSUMING_DETAIL set DEPOSIT_ID_=#{depositId},DEPOSIT_NO_=#{depositNo},DEPOSIT_AMOUNT_=#{depositAmount},DEPOSIT_DATE_=#{depositDate},DEPOSIT_STATUS_=#{depositStatus},
        DEPOSIT_TIME_=now(),DEPOSIT_USER_=#{user}
        where BILL_=#{bill} and ASSET_=#{asset}
    </update>

    <select id="findExportByIds" resultType="com.zy.dam.report.vo.AssetConsumingExportVo">
        select
        <include refid="meta"/>
        ,(select count(0) from AM_ASSET_CONSUMING_DETAIL m where m.BILL_=a.ID_) asset_count
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.RECEIVER_) receiver_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,(select m.CODE_ from AM_LOCATION m where m.ID_=a.LOCATION_) location_code
        ,b.NAME_ location_name, b.CONTACT_ location_contact, b.PHONE_ location_phone
        ,(SELECT GROUP_CONCAT(c.NAME_ SEPARATOR ',') FROM AM_ASSET_CONSUMING_DETAIL m,AM_ASSET n, AM_ASSET_TYPE c
        WHERE m.ASSET_=n.ID_ AND m.BILL_=a.ID_ AND n.TYPE_=c.CODE_ AND n.FLAG_!='9'AND c.FLAG_!='9') type_name
        ,(SELECT GROUP_CONCAT(n.NO_ SEPARATOR ',') FROM AM_ASSET_CONSUMING_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_ AND
        m.BILL_=a.ID_ AND n.FLAG_!='9') asset_no
        ,(SELECT GROUP_CONCAT(n.NAME_ SEPARATOR ',') FROM AM_ASSET_CONSUMING_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_
        AND m.BILL_=a.ID_ AND n.FLAG_!='9') asset_name
        ,(SELECT GROUP_CONCAT(n.SPEC_ SEPARATOR ',') FROM AM_ASSET_CONSUMING_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_
        AND m.BILL_=a.ID_ AND n.FLAG_!='9') asset_spec
        ,ELT(a.STATUS_ ,'申请','已通过','','','已拒绝','','','','已作废') status
        from AM_ASSET_CONSUMING a left join AM_LOCATION b on a.LOCATION_=b.ID_
        where
        <foreach collection="ids" item="id" open=" a.ID_ in (" separator="," close=")">#{id}</foreach>
    </select>

    <select id="findExportByQuery" resultType="com.zy.dam.report.vo.AssetConsumingExportVo">
        select
        <include refid="meta"/>
        ,(select count(0) from AM_ASSET_CONSUMING_DETAIL m where m.BILL_=a.ID_) asset_count
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.RECEIVER_) receiver_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,(select m.CODE_ from AM_LOCATION m where m.ID_=a.LOCATION_) location_code
        ,b.NAME_ location_name, b.CONTACT_ location_contact, b.PHONE_ location_phone
        ,(SELECT GROUP_CONCAT(c.NAME_ SEPARATOR ',') FROM AM_ASSET_CONSUMING_DETAIL m,AM_ASSET n, AM_ASSET_TYPE c
        WHERE m.ASSET_=n.ID_ AND m.BILL_=a.ID_ AND n.TYPE_=c.CODE_ AND n.FLAG_!='9'AND c.FLAG_!='9') type_name
        ,(SELECT GROUP_CONCAT(n.NO_ SEPARATOR ',') FROM AM_ASSET_CONSUMING_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_ AND
        m.BILL_=a.ID_ AND n.FLAG_!='9') asset_no
        ,(SELECT GROUP_CONCAT(n.NAME_ SEPARATOR ',') FROM AM_ASSET_CONSUMING_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_
        AND m.BILL_=a.ID_ AND n.FLAG_!='9') asset_name
        ,(SELECT GROUP_CONCAT(n.SPEC_ SEPARATOR ',') FROM AM_ASSET_CONSUMING_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_
        AND m.BILL_=a.ID_ AND n.FLAG_!='9') asset_spec
        ,ELT(a.STATUS_ ,'申请','已通过','','','已拒绝','','','','已作废') status
        from AM_ASSET_CONSUMING a left join AM_LOCATION b on a.LOCATION_=b.ID_
        where a.FLAG_='1'
            <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.MEMO_ like
                concat('%',#{keyword},'%'))
            </if>
            <if test="status != null">and a.STATUS_=#{status}</if>
            <if test="user != null">and a.USER_=#{user}</if>
            <if test="receiver != null">and a.RECEIVER_=#{receiver}</if>
            <if test="locationCode != null">and b.CODE_=#{locationCode}</if>
            <if test="locationContact != null">and b.CONTACT_=#{locationContact}</if>
            <if test="locationPhone != null">and b.PHONE_=#{locationPhone}</if>
            <if test="locationKey != null">and (b.NAME_ like concat('%',#{locationKey},'%') or b.CONTACT_ like
                concat('%',#{locationKey},'%'))
            </if>
            <if test="begin != null">and a.TIME_ &gt;= #{begin}</if>
            <if test="end != null">and a.TIME_ &lt; date_add(#{end}, interval 1 day)</if>
            <if test="deptIds != null">
                <foreach collection="deptIds" item="deptId" open="and a.CDEPT_ in (" separator="," close=")">#{deptId}
                </foreach>
            </if>
        order by a.CTIME_ desc
    </select>

    <update id="deleteBatch">
        update AM_ASSET_CONSUMING set FLAG_='9' where ID_ in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
    </update>

    <update id="delete">
        update AM_ASSET_CONSUMING_DETAIL set FLAG_='9' where BILL_ in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
    </update>

    <select id="findDepositExportByIds" resultType="com.zy.dam.report.vo.AssetDepositExportVo">
        select a.NO_ asset_no, a.NAME_ asset_name, d.NO_, d.TIME_, b.NAME_ location_name, b.CONTACT_ location_contact,
        b.PHONE_ location_phone, b.ADDRESS_ location_address, c.BILL_, c.ASSET_, c.DEPOSIT_ID_, c.DEPOSIT_NO_,
        c.DEPOSIT_AMOUNT_, c.DEPOSIT_DATE_, c.DEPOSIT_STATUS_,
        (select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.TYPE_) type_name,
        (select m.NAME_ from SYS_USER m where m.ID_=d.RECEIVER_) receiver_name
        from AM_ASSET a, AM_LOCATION b, AM_ASSET_CONSUMING_DETAIL c, AM_ASSET_CONSUMING d
        where a.LOCATION_=b.ID_ and a.ID_=c.ASSET_ and c.BILL_=d.ID_
        and c.ASSET_ in (
        <foreach collection="ids" item="id" separator=",">#{id}</foreach>
        )
        order by d.NO_ desc
    </select>

    <select id="findDepositExportByQuery" resultType="com.zy.dam.report.vo.AssetDepositExportVo">
        select a.NO_ asset_no, a.NAME_ asset_name, d.NO_, d.TIME_, b.NAME_ location_name, b.CONTACT_ location_contact,
        b.PHONE_ location_phone, b.ADDRESS_ location_address, c.BILL_, c.ASSET_, c.DEPOSIT_ID_, c.DEPOSIT_NO_,
        c.DEPOSIT_AMOUNT_, c.DEPOSIT_DATE_, c.DEPOSIT_STATUS_,
        (select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.TYPE_) type_name,
        (select m.NAME_ from SYS_USER m where m.ID_=d.RECEIVER_) receiver_name
        from AM_ASSET a, AM_LOCATION b, AM_ASSET_CONSUMING_DETAIL c, AM_ASSET_CONSUMING d
        where a.LOCATION_=b.ID_ and a.ID_=c.ASSET_ and c.BILL_=d.ID_
        <if test="assetNo != null">and a.NO_ like concat('%', #{assetNo}, '%')</if>
        <if test="locationContact != null">and b.CONTACT_ like concat('%', #{locationContact}, '%')</if>
        <if test="locationName != null">and b.NAME_ like concat('%', #{locationName}, '%')</if>
        <if test="depositStatus != null">and c.DEPOSIT_STATUS_=#{depositStatus}</if>
        <if test="begin != null">and d.TIME_ &gt;= #{begin}</if>
        <if test="end != null">and d.TIME_ &lt; date_add(#{end}, interval 1 day)</if>
        order by d.NO_ desc
    </select>
</mapper>
