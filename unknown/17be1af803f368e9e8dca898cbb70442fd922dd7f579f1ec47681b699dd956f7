<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.asset.dao.AmAssetTraceDAO">

    <!-- 资产管理查询分页 -->
    <select id="page" resultType="com.zy.dam.asset.vo.AssetTraceVo">
        SELECT
            a.ID_,
            a.NO_,
            a.NAME_,
            a.TYPE_,
            (SELECT m.NAME_ FROM AM_ASSET_TYPE m WHERE m.FLAG_ = '1' AND m.CODE_ = a.TYPE_) AS typeName,
            a.SPEC_ AS spec,
            date_format(a.CTIME_,'%Y-%m-%d') AS inDate,
            a.DEPT_ AS dept,
            (SELECT d.NAME_ FROM SYS_DEPT d WHERE d.ID_ = a.DEPT_ AND d.FLAG_ = '1') AS deptName,
            a.USE_DEPT_ AS useDept,
            (SELECT d.NAME_ FROM SYS_DEPT d WHERE d.ID_ = a.USE_DEPT_ AND d.FLAG_ = '1') AS useDeptName,
            a.USE_USER_ AS useUser,
            (SELECT u.NAME_ FROM SYS_USER u WHERE u.ID_ = a.USE_USER_ AND u.FLAG_ = '1') AS useUserName,
            (SELECT MIN(s.NOW_SN_) FROM AM_ASSET_SN s WHERE a.ID_ = s.ASSET_ AND s.FLAG_ != '9') AS terminalNo,
            a.LOCATION_ AS location,
            l.CODE_ AS locationCode,
            l.NAME_ AS locationName,
            a.LOC_ADDR_ AS locAddr,
            a.STATUS_ AS status
        FROM
            AM_ASSET a
        LEFT JOIN
            AM_LOCATION l ON a.LOCATION_ = l.ID_ AND l.FLAG_ = '1'
        WHERE
            a.FLAG_ = '1'
        <if test="keyword != null and keyword != ''">
            AND (
                a.NO_ LIKE CONCAT('%', #{keyword}, '%')
                OR a.NAME_ LIKE CONCAT('%', #{keyword}, '%')
                OR EXISTS (
                    SELECT 1 FROM AM_ASSET_SN s
                    WHERE a.ID_ = s.ASSET_ AND s.FLAG_ != '9'
                    AND s.NOW_SN_ LIKE CONCAT('%', #{keyword}, '%')
                )
                OR l.NAME_ LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="terminalNo != null and terminalNo != ''">
            AND EXISTS (
                SELECT 1 FROM AM_ASSET_SN s
                WHERE a.ID_ = s.ASSET_ AND s.FLAG_ != '9'
                AND s.NOW_SN_ LIKE CONCAT('%', #{terminalNo}, '%')
            )
        </if>
        <if test="no != null and no != ''">
            AND a.NO_ LIKE CONCAT('%', #{no}, '%')
        </if>
        <if test="assetId != null and assetId != ''">
            AND a.ID_ = #{assetId}
        </if>
        <if test="locationName != null and locationName != ''">
            AND l.NAME_ LIKE CONCAT('%', #{locationName}, '%')
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            AND a.DEPT_ IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        ORDER BY a.CTIME_ DESC
    </select>



    <!-- 根据资产ID查询资产历史记录 -->
    <select id="findDeviceHistoryByAssetId" resultType="com.zy.dam.asset.vo.AssetTraceVo">
        SELECT
            t.ASSET_ID_ AS ID_,
            a.NO_,
            a.NAME_,
            a.TYPE_,
            (SELECT m.NAME_ FROM AM_ASSET_TYPE m WHERE m.FLAG_ = '1' AND m.CODE_ = a.TYPE_) AS typeName,
            NULL AS terminalNo,
            t.OPERATION_TYPE_ AS operationType,
            CASE t.OPERATION_TYPE_
                WHEN '1' THEN '领用'
                WHEN '2' THEN '维修'
                WHEN '3' THEN '绑定'
                WHEN '4' THEN '定位'
                WHEN '5' THEN '退机'
                WHEN '6' THEN '报废'
                WHEN '7' THEN '已损坏'
                ELSE '其他'
            END AS operationTypeName,
            t.OPERATION_TIME_ AS operationTime,
            t.OPERATOR_ AS operator,
            t.OPERATOR_NAME_ AS operatorName,
            t.LOCATION_CODE_ AS locationCode,
            t.LOCATION_NAME_ AS locationName,
            COALESCE(t.REF_NO_, t.REF_ID_) AS operationNo,
            t.MEMO_ AS updateInfo,
            t.MEMO_ AS memo
        FROM
            AM_ASSET_TRACE t
        JOIN
            AM_ASSET a ON t.ASSET_ID_ = a.ID_ AND a.FLAG_ = '1'
        WHERE
            t.ASSET_ID_ = #{assetId}
            AND t.FLAG_ = '1'
        ORDER BY t.OPERATION_TIME_ DESC
    </select>
</mapper>
