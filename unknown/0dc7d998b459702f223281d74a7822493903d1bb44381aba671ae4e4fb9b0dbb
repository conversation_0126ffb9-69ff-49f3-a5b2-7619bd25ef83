<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.dp.dao.DpDAO">

    <select id="listHomeCity" resultType="com.zy.dam.dp.vo.HomeCityVo">
        select c.CODE_,c.NAME_,c.LNG_,c.LAT_, b.value_ as asset_count
        from AM_REGION a, (select REGION_,count(0) value_ from AM_ASSET where FLAG_='1' and TYPE_=#{type} and REGION_ is not null group by REGION_) b
           , SYS_REGION c
        where b.REGION_=a.CODE_ and a.REGION_ !='460000' and a.REGION_=c.CODE_
        order by a.CODE_ desc
    </select>

    <select id="listAssetTypeCount" resultType="com.zy.dam.dp.vo.PieItem">
        select a.CODE_ code, a.NAME_, b.value
            from AM_ASSET_TYPE a left join (
            select TYPE_,count(0) value from AM_ASSET where FLAG_='1' group by TYPE_
            ) b on a.CODE_=b.TYPE_
        order by b.value desc
    </select>

    <select id="listAssetMonitor" resultType="com.zy.dam.dp.vo.AssetMonitorVo">
        select count(0) as total_
             ,sum(case when a.STATUS_ = '1' then 1 else 0 end) as  stock_
             ,sum(case when EX_DATE_ &lt;= b.near then 1 else 0 end) expiring_
             ,sum(case when EX_DATE_ &lt;= b.curr then 1 else 0 end) expired_
        from AM_ASSET a, (select current_date() curr, date_add(current_date(), interval 30 day) near) b
        where a.FLAG_='1'
        <if test="type != null">and a.TYPE_ like concat(#{type},'%')</if>
    </select>

    <select id="listAssetCity" resultType="com.zy.dam.dp.vo.AssetCityVo">
        select a.CODE_,a.NAME_,a.LNG_,a.LAT_, RAND() * 1000 value_, '张三' user_name
        from SYS_REGION a
        where (a.PCODE_='460000' and CODE_!='460300' and CODE_!='469000' or PCODE_='469000')
        order by CODE_
    </select>

    <select id="findAssetByRegion" resultType="com.zy.dam.dp.vo.AssetMapPoint">
        select a.ID_,a.NO_,a.NAME_,a.LNG_,a.LAT_,a.STATUS_
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.TYPE_) type_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USE_USER_) use_user_name
        ,b.CODE_ region_code
        from AM_ASSET a, AM_REGION b
        where a.FLAG_='1' and a.REGION_=b.ID_ and a.LAT_&gt;0 and a.LNG_&gt;0
        <if test="regionLike != null">and b.CODE_ like #{regionLike}</if>
    </select>

    <select id="findAllPoint" resultType="com.zy.dam.dp.vo.PointMapPoint">
        select a.ID_,a.NO_,a.NAME_,a.LNG_,a.LAT_,a.ADDRESS_
        ,(select m.NAME_ from AM_PATROL_POINT_GROUP m where m.ID_=a.GROUP_) group_name
        from AM_PATROL_POINT a
        where a.FLAG_='1' and a.LAT_&gt;0 and a.LNG_&gt;0
        <if test="regionLike != null">and b.CODE_ like #{regionLike}</if>
    </select>

    <select id="listAssetDept" resultType="com.zy.dam.dp.vo.AssetDeptVo">
        select a.NAME_ as dept_name_, b.DEPT_ as dept_code_ ,b.value_ as total_ from SYS_DEPT a, (
            select DEPT_,count(0) value_ from am_asset where FLAG_='1' and type_=#{type} group by DEPT_) b
        where a.ID_=b.DEPT_
        order by b.value_ desc
    </select>

    <select id="countOrderTrendByMonth" resultType="com.zy.model.NameInt">
        select name_,count(0) value_ from (
          select date_format(a.REPORT_TIME_, '%m') name_
          from wo_bill a
          where a.FLAG_!='9'
            and a.REPORT_TIME_&gt;=#{0} and a.REPORT_TIME_ &lt;date_add(#{1},interval 1 month)
        ) b group by name_
        order by name_
    </select>

    <select id="countOrderTrendByStatus" resultType="com.zy.model.NameInt">
          select a.STATUS_ as name_,count(0) value_
          from wo_bill a
          where a.FLAG_!='9'
            and a.REPORT_TIME_&gt;=#{0} and a.REPORT_TIME_ &lt;#{1}
        group by a.STATUS_
    </select>

    <select id="countChildren" resultType="int">
        select count(0) from SYS_PRIV where CODE_ like concat(#{0}, '___')
    </select>

    <select id="countOrderByToday" resultType="int">
        select count(0)
        from wo_bill a
        where a.FLAG_!='9'
            and a.REPORT_TIME_&gt;=#{0} and a.REPORT_TIME_ &lt;#{1}
    </select>

    <select id="countOrderOnlineByToday" resultType="int">
        select count(0)
        from wo_bill a
        where a.FLAG_!='9' and a.STATUS_ in ('1','2')
            and a.REPORT_TIME_&gt;=#{0} and a.REPORT_TIME_ &lt;#{1}
    </select>

</mapper>
