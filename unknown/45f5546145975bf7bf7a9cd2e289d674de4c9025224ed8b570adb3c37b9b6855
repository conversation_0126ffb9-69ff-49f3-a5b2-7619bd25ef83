<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.asset.dao.AmInventoryDAO">

    <sql id="meta">
			a.ID_
			,a.DATE_
			,a.NO_
			,a.NAME_
			,a.MEMO_
			,a.USER_
			,a.START_TIME_
			,a.END_TIME_
			,a.STATUS_
	</sql>
    <insert id="insert" parameterType="com.zy.dam.asset.orm.AmInventory">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into AM_INVENTORY(ID_,DATE_,NO_,NAME_,MEMO_,USER_,START_TIME_,END_TIME_,STATUS_,FLAG_,CTIME_,MTIME_)
        values(#{id},#{date},#{no},#{name},#{memo},#{user},#{startTime},#{endTime},'1','1',now(),now())
    </insert>

    <update id="update" parameterType="com.zy.dam.asset.orm.AmInventory">
		update AM_INVENTORY
		set DATE_=#{date},NAME_=#{name},USER_=#{user},START_TIME_=#{startTime},END_TIME_=#{endTime},MTIME_=now()
		where ID_=#{id}
	</update>

    <select id="page" resultType="com.zy.dam.asset.vo.InventoryVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        from AM_INVENTORY a
        where a.FLAG_='1'
        <if test="keyword != null">and a.NAME_ like concat('%',#{keyword},'%')</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="begin != null">and a.DATE_&gt;=#{begin}</if>
        <if test="end != null">and a.DATE_&lt; date_add(#{end}, interval 1 day)</if>
        order by a.DATE_ desc
    </select>

    <select id="queryMine" resultType="com.zy.dam.asset.vo.InventoryVo">
        select
        <include refid="meta"/>
        from AM_INVENTORY a
        where a.FLAG_='1' and (a.USER_=#{user} or a.ID_ in (select TASK_ from AM_INVENTORY_USER where USER_=#{user}))
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="begin != null">and a.DATE_&gt;=#{begin}</if>
        <if test="end != null">and a.DATE_&lt; date_add(#{end}, interval 1 day)</if>
        order by a.DATE_ desc
        limit #{offset},#{limit}
    </select>

    <select id="findOne" resultType="com.zy.dam.asset.orm.AmInventory">
        select
        <include refid="meta"/>
        from AM_INVENTORY a where a.ID_=#{0}
    </select>

    <update id="delete">
		update AM_INVENTORY set FLAG_='9' where ID_=#{0}
	</update>

    <select id="findVo" resultType="com.zy.dam.asset.vo.InventoryVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name

        from AM_INVENTORY a where a.ID_=#{0}
    </select>

    <delete id="deleteUser">
        delete from AM_INVENTORY_USER where TASK_=#{0}
    </delete>

    <delete id="deleteAsset">
        delete from AM_INVENTORY_ASSET where TASK_=#{0}
    </delete>

    <delete id="deleteRegion">
        delete from AM_INVENTORY_REGION where TASK_=#{0}
    </delete>

    <insert id="insertUser">
        insert into AM_INVENTORY_USER(TASK_,USER_,STATUS_) values(#{0},#{1},'1')
    </insert>

    <insert id="insertAsset">
        insert into AM_INVENTORY_ASSET(TASK_,ASSET_,STATUS_) values(#{0},#{1},'1')
    </insert>

    <insert id="insertAssetBatch">
        insert into AM_INVENTORY_ASSET(TASK_,ASSET_,STATUS_) values
        <foreach collection="assetIds" item="assetId" separator=",">
            (#{taskId},#{assetId},'1')
        </foreach>
    </insert>

    <insert id="insertRegion">
        insert into AM_INVENTORY_REGION(ID_,TASK_,DEPT_,REGION_,OWNER_DEPT_) values(uuid(),#{0},#{1},#{2},#{3})
    </insert>

    <select id="findAssetByStatus" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select
            a.ID_,a.PROP_,a.TYPE_,a.NO_,a.NAME_,a.SN_,a.BC1_,a.IMP_,a.CORP_,a.DEPT_,a.REGION_,a.LOCATION_,a.MA_DATE_
            ,a.GU_DATE_,a.EX_DATE_,a.BO_DATE_,a.BO_AMOUNT_,a.EX_DAY_,a.EX_NAME_,a.USE_DEPT_,a.USE_USER_,a.STATUS_,a.LEND_STATUS_
            ,b.NAME_ use_user_name
            ,c.NAME_ region_name
            ,d.NAME_ location_name
            ,o.STATUS_ inventory_status
            ,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=a.TYPE_) type_name
            ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
            ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.USE_DEPT_) use_dept_name
        from AM_INVENTORY_ASSET o, AM_ASSET a
            left join SYS_USER b on a.USE_USER_=b.ID_
            left join AM_REGION c on a.REGION_=c.ID_
            left join AM_LOCATION d on a.LOCATION_=d.ID_
        where a.FLAG_='1' and a.ID_=o.ASSET_ and o.TASK_=#{0} and o.STATUS_=#{1}
        order by c.CODE_,d.CODE_,a.NO_
    </select>

    <insert id="insertLog">
        insert into AM_INVENTORY_LOG(ID_,TASK_,ASSET_,RFID_,TYPE_,STATUS_,USER_,TIME_)
        values(uuid(),#{0},#{1},#{2},#{3},#{4},#{5},now())
    </insert>

    <update id="updateStart">
        update AM_INVENTORY set STATUS_='2',START_TIME_=ifnull(START_TIME_,now()),MTIME_=now() where ID_=#{0}
    </update>

    <update id="updateEnd">
        update AM_INVENTORY set STATUS_='3',END_TIME_=ifnull(END_TIME_,now()),MTIME_=now() where ID_=#{0}
    </update>

    <select id="findAssetId" resultType="String">
        select ASSET_ from AM_INVENTORY_ASSET where TASK_=#{0}
    </select>

    <update id="updateAsset">
        update AM_INVENTORY_ASSET set STATUS_=#{2},USER_=#{3},TYPE_=#{4},DEVICE_=#{5},TIME_=now() where TASK_=#{0} and ASSET_=#{1}
    </update>

    <insert id="insertOtherAsset">
        insert into AM_INVENTORY_ASSET(TASK_,ASSET_,STATUS_,USER_,TYPE_,DEVICE_,TIME_)
        values(#{0}, #{1}, #{2}, #{3}, #{4}, #{5}, now())
    </insert>

    <select id="findAsset" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select
            a.ID_,a.PROP_,a.TYPE_,a.NO_,a.NAME_,a.SN_,a.BC1_,a.IMP_,a.CORP_,a.DEPT_,a.REGION_,a.LOCATION_,a.MA_DATE_
            ,a.GU_DATE_,a.EX_DATE_,a.BO_DATE_,a.BO_AMOUNT_,a.EX_DAY_,a.EX_NAME_,a.USE_DEPT_,a.USE_USER_,a.STATUS_,a.LEND_STATUS_
            ,b.NAME_ use_user_name
            ,c.NAME_ region_name
            ,d.NAME_ location_name
            ,o.STATUS_ inventory_status
            ,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=a.TYPE_) type_name
            ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
            ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.USE_DEPT_) use_dept_name
        from AM_INVENTORY_ASSET o, AM_ASSET a
            left join SYS_USER b on a.USE_USER_=b.ID_
            left join AM_REGION c on a.REGION_=c.ID_
            left join AM_LOCATION d on a.LOCATION_=d.ID_
        where a.FLAG_='1' and a.ID_=o.ASSET_ and o.STATUS_ in ('0', '1') and o.TASK_=#{0}
        order by c.CODE_,d.CODE_,a.NO_
    </select>

    <select id="findUser" resultType="com.zy.dam.asset.vo.InventoryUserVo">
        select a.USER_, a.STATUS_, a.START_TIME_, a.END_TIME_
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        from AM_INVENTORY_USER a
        where a.TASK_=#{0}
    </select>

    <select id="findRegion" resultType="com.zy.dam.asset.vo.InventoryRegionVo">
        select a.DEPT_,a.REGION_,a.OWNER_DEPT_
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.OWNER_DEPT_) owner_dept_name
        from AM_INVENTORY_REGION a
        where a.TASK_=#{0}
    </select>

    <select id="findExportByIds" resultType="com.zy.dam.report.vo.InventoryExportVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        ,(select count(0) from AM_INVENTORY_ASSET m where m.TASK_=a.ID_ and m.STATUS_='1') doing_count
        ,(select count(0) from AM_INVENTORY_ASSET m where m.TASK_=a.ID_ and m.STATUS_='2') done_count
        ,(select count(0) from AM_INVENTORY_ASSET m where m.TASK_=a.ID_ and m.STATUS_='3') other_count
        ,(select GROUP_CONCAT(u.NAME_ SEPARATOR ', ') from AM_INVENTORY_USER iu, SYS_USER u
          where iu.TASK_=a.ID_ and iu.USER_=u.ID_) assistant_names
        ,(select GROUP_CONCAT(CONCAT(d.NAME_, '/', IFNULL(r.NAME_, '')) SEPARATOR ', ')
          from AM_INVENTORY_REGION ir
          left join SYS_DEPT d on ir.DEPT_=d.ID_
          left join AM_REGION r on ir.REGION_=r.ID_
          where ir.TASK_=a.ID_) region_names
        from AM_INVENTORY a
        where a.ID_ in (
        <foreach collection="ids" item="id" separator=",">#{id}</foreach>
        )
    </select>

    <select id="findExportByQuery" resultType="com.zy.dam.report.vo.InventoryExportVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        ,(select count(0) from AM_INVENTORY_ASSET m where m.TASK_=a.ID_ and m.STATUS_='1') doing_count
        ,(select count(0) from AM_INVENTORY_ASSET m where m.TASK_=a.ID_ and m.STATUS_='2') done_count
        ,(select count(0) from AM_INVENTORY_ASSET m where m.TASK_=a.ID_ and m.STATUS_='3') other_count
        ,(select GROUP_CONCAT(u.NAME_ SEPARATOR ', ') from AM_INVENTORY_USER iu, SYS_USER u
          where iu.TASK_=a.ID_ and iu.USER_=u.ID_) assistant_names
        ,(select GROUP_CONCAT(CONCAT(d.NAME_, '/', IFNULL(r.NAME_, '')) SEPARATOR ', ')
          from AM_INVENTORY_REGION ir
          left join SYS_DEPT d on ir.DEPT_=d.ID_
          left join AM_REGION r on ir.REGION_=r.ID_
          where ir.TASK_=a.ID_) region_names
        from AM_INVENTORY a
        where a.FLAG_='1'
        <if test="keyword != null">and a.NAME_ like concat('%',#{keyword},'%')</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="begin != null">and a.DATE_&gt;=#{begin}</if>
        <if test="end != null">and a.DATE_&lt; date_add(#{end}, interval 1 day)</if>
    </select>
</mapper>
