<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.sys.dao.SysDeptDAO">

    <sql id="meta">
        a.ID_
        ,a.PID_
        ,a.NAME_
        ,a.NO_
        ,a.TYPE_
        ,a.REGION_
        ,a.ORD_
        ,a.ADDRESS_
        ,a.MANAGER_
        ,a.MOBILE_
        ,a.FLAG_
    </sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.sys.orm.SysDept">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into SYS_DEPT(ID_,PID_,NAME_,NO_,TYPE_,REG<PERSON>_,ORD_,ADDRESS_,<PERSON>NAGER_,MOBILE_,FLAG_)
        values(#{id},#{pid},#{name},#{no},#{type},#{region},#{ord},#{address},#{manager},#{mobile},'1')
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.sys.orm.SysDept">
        update SYS_DEPT
        set PID_=#{pid},NAME_=#{name},NO_=#{no},TYPE_=#{type},REGION_=#{region},ORD_=#{ord},ADDRESS_=#{address},MANAGER_=#{manager},MOBILE_=#{mobile}
        where ID_=#{id}
    </update>

    <select id="countChildren" resultType="int">
        select count(0) from SYS_DEPT where FLAG_='1' and PID_=#{0}
    </select>

    <select id="findAll" resultType="com.zy.sys.vo.DeptVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.PID_) parent_name
        from SYS_DEPT a
        where a.FLAG_='1'
        order by a.TYPE_,a.ORD_
    </select>

    <select id="findByType" resultType="com.zy.model.VueTreeNode">
        select a.ID_, a.PID_, a.NAME_ label_
        from SYS_DEPT a
        where a.FLAG_='1' and (a.PID_='0' or a.TYPE_=#{0})
        order by a.ORD_,a.NO_
    </select>

    <select id="page" resultType="com.zy.sys.vo.DeptVo">
        select
        <include refid="meta"/>
        <if test='type == "3"'>
            ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.PID_) parent_name
        </if>
        from SYS_DEPT a
        where a.FLAG_='1'
        <if test='type != null'>and a.TYPE_=#{type }</if>
        <if test='keyword != null'>and a.NAME_ like concat('%',#{keyword },'%')</if>
        <if test="ignoreTypeRoot != null">and a.PID_!='A'</if>
        order by a.ORD_,a.NO_
    </select>

    <!-- 获取唯一的系统组织机构信息数据 -->
    <select id="findOne" resultType="com.zy.sys.orm.SysDept">
        select
        <include refid="meta"/>
        from SYS_DEPT a where a.ID_=#{0}
    </select>

    <!-- 删除 -->
    <update id="delete">
        update SYS_DEPT set FLAG_='9' where ID_=#{0}
    </update>

    <update id="updateStatus">
        update SYS_DEPT set STATUS_=#{1} where ID_=#{0}
    </update>

    <select id="listChildren" resultType="com.zy.model.ValueText">
        select ID_ value_,NAME_ text_ from SYS_DEPT a where a.PID_=#{0} and a.FLAG_='1' order by a.ORD_,a.NO_
    </select>

    <select id="findName" resultType="String">
        select NAME_ from SYS_DEPT where ID_=#{0}
    </select>

</mapper>
