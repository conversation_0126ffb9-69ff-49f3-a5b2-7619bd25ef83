/**
 * 全局时间处理工具类
 * 统一处理时区问题，避免前端时间显示不一致
 */

/**
 * 格式化时间 - 统一的时间格式化方法
 * @param {string|Date} time 时间字符串或Date对象
 * @param {string} format 格式化模板，默认为 'yyyy-MM-dd HH:mm:ss'
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(time, format = 'yyyy-MM-dd HH:mm:ss') {
	if (!time) return '';

	// 如果后端返回的时间字符串已经是正确格式，直接返回
	if (typeof time === 'string' && /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(time)) {
		return time;
	}

	// 兼容iOS的日期格式处理
	let dateStr = time;
	if (typeof time === 'string' && time.includes(' ') && time.includes('-')) {
		dateStr = time.replace(/-/g, '/');
	}

	const date = new Date(dateStr);

	// 检查日期是否有效
	if (isNaN(date.getTime())) {
		return time;
	}

	// 使用UTC时间来避免时区转换问题
	const year = date.getUTCFullYear();
	const month = String(date.getUTCMonth() + 1).padStart(2, '0');
	const day = String(date.getUTCDate()).padStart(2, '0');
	const hours = String(date.getUTCHours()).padStart(2, '0');
	const minutes = String(date.getUTCMinutes()).padStart(2, '0');
	const seconds = String(date.getUTCSeconds()).padStart(2, '0');

	// 根据格式模板返回
	switch (format) {
		case 'yyyy-MM-dd':
			return `${year}-${month}-${day}`;
		case 'HH:mm:ss':
			return `${hours}:${minutes}:${seconds}`;
		case 'yyyy-MM-dd HH:mm':
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		default:
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
	}
}

/**
 * 格式化时间范围 - 用于巡检任务等时间范围显示
 * @param {object} item 包含startTime和endTime的对象
 * @returns {string} 格式化后的时间范围字符串
 */
export function formatTimeRange(item) {
	if (!item || !item.startTime || !item.endTime) return '';
	
	const st = item.startTime.replace(/:00$/, '');
	const et = item.endTime.replace(/(^.*\s)|(:00$)/g, '');
	return (st.length < 12 ? st + ' 00:00' : st) + '~' + et;
}

/**
 * 获取当前时间字符串
 * @param {string} format 格式化模板
 * @returns {string} 当前时间字符串
 */
export function getCurrentTime(format = 'yyyy-MM-dd HH:mm:ss') {
	return formatTime(new Date(), format);
}

/**
 * 时间比较
 * @param {string|Date} time1 时间1
 * @param {string|Date} time2 时间2
 * @returns {number} 比较结果：-1(time1<time2), 0(相等), 1(time1>time2)
 */
export function compareTime(time1, time2) {
	const date1 = new Date(time1);
	const date2 = new Date(time2);
	
	if (date1.getTime() < date2.getTime()) return -1;
	if (date1.getTime() > date2.getTime()) return 1;
	return 0;
}

/**
 * 计算时间差（天数）
 * @param {string|Date} startTime 开始时间
 * @param {string|Date} endTime 结束时间
 * @returns {number} 相差天数
 */
export function dateDiff(startTime, endTime) {
	const start = new Date(startTime);
	const end = new Date(endTime);
	const diffTime = Math.abs(end - start);
	return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * 检查是否为有效时间
 * @param {string|Date} time 时间
 * @returns {boolean} 是否有效
 */
export function isValidTime(time) {
	if (!time) return false;
	const date = new Date(time);
	return !isNaN(date.getTime());
}
