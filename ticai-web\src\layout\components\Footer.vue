<template>
  <footer class="app-footer">
    <div class="footer-content">
      <div>
        <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link">
          琼ICP备16002127号-6</a>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'Footer'
}
</script>

<style lang="scss" scoped>
.app-footer {
  background-color: #ffffff;
  // border-top: 1px solid #e4e7ed;
  padding: 10px 0;
  text-align: center;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
  margin-top: auto;
}

.footer-content {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.beian-link {
  color: #666;
  text-decoration: none;
  transition: color 0.3s ease;

  &:hover {
    color: #409eff;
  }

  &:visited {
    color: #666;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .footer-content {
    font-size: 11px;
    padding: 0 5px;
  }
}
</style>
