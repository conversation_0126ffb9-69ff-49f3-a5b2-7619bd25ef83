"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[4854],{69952:(e,t,s)=>{s.d(t,{Z:()=>o});var a=s(8081),r=s.n(a),n=s(23645),l=s.n(n)()(r());l.push([e.id,"",""]);const o=l},94854:(e,t,s)=>{s.r(t),s.d(t,{default:()=>Z}),s(57658);var a=s(66252);const r={class:"siteTree"},n=(0,a.aZ)({__name:"SiteTree",props:{paramsData:{},source:{}},setup(e){const t=e,s=(0,a.Fl)((()=>{var e,s;const a=(null===(e=t.paramsData)||void 0===e||null===(e=e.site_tree)||void 0===e?void 0:e.site_tree)||(null===(s=t.paramsData)||void 0===s||null===(s=s.site_tree)||void 0===s?void 0:s.data.site_tree)||{};return console.log("siteTreeData",a),function e(t,s=0,a){const r=[];return Object.keys(t).forEach(((n,l)=>{r.push({title:n,key:a?`${a}_${s}_${l}`:`${s}_${l}`}),Object.keys(t[n]).length>0&&(r[l].children=e(t[n],s+1,`${s}_${l}`))})),r}(a)}));return(e,t)=>{const n=(0,a.up)("a-directory-tree");return(0,a.wg)(),(0,a.iD)("div",r,[(0,a.Wm)(n,{treeData:s.value},null,8,["treeData"])])}}});var l=s(93379),o=s.n(l),i=s(7795),u=s.n(i),c=s(90569),d=s.n(c),_=s(3565),p=s.n(_),v=s(19216),b=s.n(v),h=s(44589),m=s.n(h),k=s(69952),D={};D.styleTagTransform=m(),D.setAttributes=p(),D.insert=d().bind(null,"head"),D.domAPI=u(),D.insertStyleElement=b(),o()(k.Z,D),k.Z&&k.Z.locals&&k.Z.locals;const Z=(0,s(83744).Z)(n,[["__scopeId","data-v-69b781a4"]])}}]);