"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[9107],{32143:(e,a,d)=>{d.d(a,{Z:()=>f});var n=d(8081),t=d.n(n),c=d(23645),l=d.n(c)()(t());l.push([e.id,".vRcBarTableWrap[data-v-3057d03e] {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n  width: 100%;\n}\n.vRcBarTableWrap .risktrendImg[data-v-3057d03e] {\n  display: none;\n  height: 380px;\n  flex: 1;\n}\n.vRcBarTableWrap .vrcTab[data-v-3057d03e] {\n  margin-left: 24px;\n  min-width: 380px;\n  height: auto;\n  flex: 1;\n}\n.vRcBarTableWrap .vrcTab th[data-v-3057d03e] {\n  white-space: nowrap;\n}\n.vRcBarTableWrap .riskTrendRef[data-v-3057d03e] {\n  height: 380px;\n  flex: 1;\n}\n",""]);const f=l},71335:(e,a,d)=>{d.d(a,{Z:()=>f});var n=d(8081),t=d.n(n),c=d(23645),l=d.n(c)()(t());l.push([e.id,".title[data-v-05d32f7e] {\n  margin-top: 10px;\n  font-size: 14px;\n}\n",""]);const f=l},9188:(e,a,d)=>{d.d(a,{Z:()=>f});var n=d(8081),t=d.n(n),c=d(23645),l=d.n(c)()(t());l.push([e.id,".winTabWrap {\n  position: relative;\n  display: flex;\n  margin-top: 60px;\n  padding: 22px;\n  border: 3px solid #fff;\n  border-radius: 0 4px 4px;\n  background: #f1f1f3;\n  box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.06);\n  flex: 1;\n  flex-direction: column;\n}\n.winTabWrap.noTitle {\n  margin-top: 22px;\n}\n.winTabWrap > .header {\n  position: absolute;\n  top: -40px;\n  left: -3px;\n  z-index: 2;\n  padding: 0 30px 0 22px;\n  height: 40px;\n  border-top: 3px solid #fff;\n  border-left: 3px solid #fff;\n  border-radius: 4px 4px 0 0;\n  background: #f1f1f3;\n  box-shadow: 0 -1px 16px 0 rgba(0, 0, 0, 0.06);\n  line-height: 40px;\n}\n.winTabWrap > .header .title {\n  margin-top: 9px;\n  height: 30px;\n  font-size: 14px;\n  line-height: 30px;\n}\n.winTabWrap > .header ::after {\n  position: absolute;\n  top: 30px;\n  right: 0;\n  left: 0;\n  z-index: -1;\n  height: 20px;\n  background: #f1f1f3;\n  content: '';\n}\n.winTabWrap > .header ::before {\n  position: absolute;\n  top: 1px;\n  right: -24px;\n  width: 31px;\n  height: 52px;\n  border-right: 3px solid #fff;\n  border-radius: 2px;\n  background: #f1f1f3;\n  content: '';\n  transform: rotate(-45deg);\n}\n.winTabWrap > .body {\n  padding: 20px;\n  width: 100%;\n  border-radius: 4px;\n  background-color: #fff;\n}\n",""]);const f=l},49107:(e,a,d)=>{d.r(a),d.d(a,{default:()=>D});var n=d(66252),t=d(2262),c=d(425),l=(d(57658),d(85827),d(3577)),f=d(69611),r=d(24206);const i={class:"vRcBarTableWrap"},s=(0,n.aZ)({__name:"RiskTypeBarTable",props:{name:{},data:{},source:{},exportTypeTemplate:{}},setup(e,{expose:a}){const d=e,{t:c}=(0,r.QT)(),s=(0,t.iH)(null),o=(0,t.iH)(c("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f3408fde93319cd265de29fc1050fe2e074787045727295c9d9125eca0d134691aec90fe15abc513c1b58aba31e317221"));let p=null,b=()=>{};(0,t.iH)("");const u=(0,t.qj)([{title:(0,n.Fl)((()=>`${(0,t.SU)(d.name)}${c("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f3408fde93319cd265de29fc1050fe2e0431206f5a988a00f809a0f0a03b8d635")}`)),dataIndex:"title",minWidth:200,width:200},{title:c("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f3408fde93319cd265de29fc1050fe2e03343334ad05f87c67e36a7f25d302a73"),dataIndex:"high",minWidth:100},{title:c("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f3408fde93319cd265de29fc1050fe2e075ff6bb169ba5a4334376af86570dd6b"),dataIndex:"middle",minWidth:100},{title:c("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f3408fde93319cd265de29fc1050fe2e091bb14835155fcdb72ac165c9edb99d9"),dataIndex:"low",minWidth:100},{title:c("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f3408fde93319cd265de29fc1050fe2e065b64dd6c210f868ba784d3f381bf740"),dataIndex:"all",minWidth:100}]),m=(0,n.Fl)((()=>d.data.map((e=>({...e,all:Number(e.high+e.middle+e.low)}))))),h=()=>{const{result:e,ydata:a}=(e=>{const a=[],d=[],n=[],l=[],f=[{name:c("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f3408fde93319cd265de29fc1050fe2e028a75d89ca31d00cb512c8295b7257d9"),data:[],legendColor:"#EC4F4F"},{name:c("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f3408fde93319cd265de29fc1050fe2e0a508adcb3c02ead7c34b773c407ca120"),data:[],legendColor:"#FA8C16"},{name:c("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f3408fde93319cd265de29fc1050fe2e0f266e72f1d531f60de67c94aa9b398c7"),data:[],legendColor:"#0072EE"}];return e.map((e=>{a.push(e.high),d.push(e.middle),n.push(e.low),l.push(e.title||"")})),f[0].data=a,f[1].data=d,f[2].data=n,{ydata:(0,t.iH)(l),result:f}})((0,t.SU)(m)),{getInstance:n,resize:l}=(0,f.y8)(s,{title:`${c("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f3408fde93319cd265de29fc1050fe2e0940c8e234566197b338a1ef78c2873f2147b59d4c13f178aaf630381c8350d9e")}${(0,t.SU)(d.name)}）`,grid:{top:"25%",left:10,right:110,bottom:"10%",containLabel:!0},result:e,ydata:a,xAxisName:o});p=n(),b=l};return(0,n.m0)((()=>{h()})),a({setEcharts:h,riskTrendRef:s,resize:()=>{var e;null===(e=p)||void 0===e||e.resize()},resizeChart:b}),(e,a)=>{const t=(0,n.up)("a-table-summary-cell"),c=(0,n.up)("a-table-summary-row"),f=(0,n.up)("a-table");return(0,n.wg)(),(0,n.iD)("div",i,[(0,n._)("div",{class:"riskTrendRef",ref_key:"riskTrendRef",ref:s},null,512),d.name?((0,n.wg)(),(0,n.j4)(f,{key:0,columns:u,"data-source":m.value,pagination:!1,class:"vrcTab"},{summary:(0,n.w5)((()=>[(0,n.Wm)(c,null,{default:(0,n.w5)((()=>[(0,n.Wm)(t,null,{default:(0,n.w5)((()=>[(0,n._)("b",null,(0,l.zw)(e.$t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467f3408fde93319cd265de29fc1050fe2e00bbabe0d92f34ba74318c045ce78d8cd")),1)])),_:1}),(0,n.Wm)(t,null,{default:(0,n.w5)((()=>[(0,n._)("b",null,(0,l.zw)(d.data.reduce(((e,a)=>e+a.high),0)),1)])),_:1}),(0,n.Wm)(t,null,{default:(0,n.w5)((()=>[(0,n._)("b",null,(0,l.zw)(d.data.reduce(((e,a)=>e+a.middle),0)),1)])),_:1}),(0,n.Wm)(t,null,{default:(0,n.w5)((()=>[(0,n._)("b",null,(0,l.zw)(d.data.reduce(((e,a)=>e+a.low),0)),1)])),_:1}),(0,n.Wm)(t,null,{default:(0,n.w5)((()=>[(0,n._)("b",null,(0,l.zw)(d.data.reduce(((e,a)=>e+a.high+a.middle+a.low),0)),1)])),_:1})])),_:1})])),_:1},8,["columns","data-source"])):(0,n.kq)("v-if",!0)])}}});var o=d(93379),p=d.n(o),b=d(7795),u=d.n(b),m=d(90569),h=d.n(m),x=d(3565),g=d.n(x),w=d(19216),v=d.n(w),T=d(44589),_=d.n(T),y=d(32143),W={};W.styleTagTransform=_(),W.setAttributes=g(),W.insert=h().bind(null,"head"),W.domAPI=u(),W.insertStyleElement=v(),p()(y.Z,W),y.Z&&y.Z.locals&&y.Z.locals;var k=d(83744);const Z=(0,k.Z)(s,[["__scopeId","data-v-3057d03e"]]);var I=d(79414);const z=(0,n.aZ)({__name:"RiskTypeCategoryComp",props:{paramsData:{},exportTypeTemplate:{},source:{}},setup(e){const a=e,{t:d}=I.i18n.global,l=[{title:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fa55a9e02e02370a89e7576cddb9cf7ec292b3bbfb9acf99106b5327b3e6012a9598cd6415dd8d9c09c7962f9bae3cecf"),dataIndex:"url"},{title:d("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fa55a9e02e02370a89e7576cddb9cf7ec292b3bbfb9acf99106b5327b3e6012a9242e33a25341c5c66b4ac309c75a5c5c2e8a10df2e184eaea0abefdf175d3a26"),dataIndex:"high",width:150}],f=(0,n.Fl)((()=>(0,t.SU)(a.paramsData)));return(e,d)=>{const t=(0,n.up)("a-table");return(0,n.wg)(),(0,n.iD)(n.HY,null,[((0,n.wg)(!0),(0,n.iD)(n.HY,null,(0,n.Ko)(f.value.riskCategories,(e=>((0,n.wg)(),(0,n.j4)(c.Z,{key:e,title:e.name},{default:(0,n.w5)((()=>[(0,n.Wm)(Z,{data:e.data,name:e.name,source:a.source,exportTypeTemplate:a.exportTypeTemplate},null,8,["data","name","source","exportTypeTemplate"])])),_:2},1032,["title"])))),128)),a.paramsData.highvulnTop10Urls?((0,n.wg)(),(0,n.j4)(c.Z,{key:0,title:e.$t("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fa55a9e02e02370a89e7576cddb9cf7ec292b3bbfb9acf99106b5327b3e6012a954c7b5a2fd0704e1ef87bbf939d482e6e73b95ee914359e7724c3a67c80b2902e094022c709b368cda716cc8d4cd7ac0")},{default:(0,n.w5)((()=>[(0,n.Wm)(t,{columns:l,"data-source":a.paramsData.highvulnTop10Urls,pagination:!1},null,8,["data-source"])])),_:1},8,["title"])):(0,n.kq)("v-if",!0)],64)}}});var C=d(71335),R={};R.styleTagTransform=_(),R.setAttributes=g(),R.insert=h().bind(null,"head"),R.domAPI=u(),R.insertStyleElement=v(),p()(C.Z,R),C.Z&&C.Z.locals&&C.Z.locals;const D=(0,k.Z)(z,[["__scopeId","data-v-05d32f7e"]])},425:(e,a,d)=>{d.d(a,{Z:()=>W});var n=d(66252),t=d(3577);const c={key:0,class:"header"},l={class:"title"},f={class:"body"},r={class:"footer"},i=(0,n.aZ)({__name:"winTab",props:{title:{}},setup(e){const a=e,d=(0,n.Fl)((()=>a.title||""));return(e,a)=>((0,n.wg)(),(0,n.iD)("div",{class:(0,t.C_)(["winTabWrap",{noTitle:!d.value}])},[d.value?((0,n.wg)(),(0,n.iD)("div",c,[(0,n._)("div",l,(0,t.zw)(d.value),1)])):(0,n.kq)("v-if",!0),(0,n._)("div",f,[(0,n.WI)(e.$slots,"default")]),(0,n._)("div",r,[(0,n.WI)(e.$slots,"footer")])],2))}});var s=d(93379),o=d.n(s),p=d(7795),b=d.n(p),u=d(90569),m=d.n(u),h=d(3565),x=d.n(h),g=d(19216),w=d.n(g),v=d(44589),T=d.n(v),_=d(9188),y={};y.styleTagTransform=T(),y.setAttributes=x(),y.insert=m().bind(null,"head"),y.domAPI=b(),y.insertStyleElement=w(),o()(_.Z,y),_.Z&&_.Z.locals&&_.Z.locals;const W=i}}]);