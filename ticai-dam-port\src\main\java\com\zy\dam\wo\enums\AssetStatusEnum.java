package com.zy.dam.wo.enums;

/**
 * 资产状态枚举
 * 用于标记资产的特殊状态，特别是退库资产
 */
public enum AssetStatusEnum {
    
    /**
     * 退库资产 - 网点ID标识
     */
    RETURNED_ASSET_LOCATION_ID("RETURNED_ASSET", "退库资产"),
    
    /**
     * 退库资产 - 网点名称标识
     */
    RETURNED_ASSET_LOCATION_NAME("无网点信息", "退库资产无网点信息"),
    
    /**
     * 退库资产 - 网点地址标识
     */
    RETURNED_ASSET_LOCATION_ADDRESS("无网点地址", "退库资产无网点地址");
    
    private final String code;
    private final String description;
    
    AssetStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 检查是否为退库资产的标识
     */
    public static boolean isReturnedAsset(String value) {
        if (value == null || value.trim().isEmpty()) {
            return true;
        }
        return RETURNED_ASSET_LOCATION_ID.getCode().equals(value) ||
               RETURNED_ASSET_LOCATION_NAME.getCode().equals(value) ||
               RETURNED_ASSET_LOCATION_ADDRESS.getCode().equals(value);
    }
}
