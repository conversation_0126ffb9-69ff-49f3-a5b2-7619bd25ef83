"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[1168],{23775:(e,a,n)=>{n.d(a,{g:()=>r});var l=n(59305),u=n(94116);const t=(0,l.Q_)({id:"onlineReport",state:()=>({curTab:"VulnSummary",curExpandVulnId:-1}),getters:{getCurTab(){return this.curTab},getCurExpandVulnId(){return this.curExpandVulnId}},actions:{setCurTab(e){this.curTab=e},setCurExpandVulnId(e){this.curExpandVulnId=e}}});function r(){return t(u.h)}},23059:(e,a,n)=>{n.d(a,{Z:()=>s});var l=n(8081),u=n.n(l),t=n(23645),r=n.n(t)()(u());r.push([e.id,".vulnDetailList > p[data-v-467c1f7a] {\n  display: flex;\n  margin-bottom: 0;\n  padding: 16px 24px;\n  font-size: 14px;\n  border-radius: 4px;\n  line-height: 22px;\n}\n.vulnDetailList > p > span[data-v-467c1f7a]:first-child {\n  min-width: 200px;\n  font-weight: 500;\n}\n.vulnDetailList > p > span[data-v-467c1f7a]:first-child::after {\n  content: ':';\n  margin: 0 2px;\n}\n",""]);const s=r},51168:(e,a,n)=>{n.r(a),n.d(a,{default:()=>C}),n(57658);var l=n(66252),u=n(3577),t=n(2262),r=n(64142),s=n(39022),d=n(24206),i=n(23775),v=n(61641);const o={class:"vulnDetail"},p=["id"],c=(0,l.aZ)({__name:"VulnDetail",props:{paramsData:{},source:{},taskType:{},taskTypeNum:{},exportTypeTemplate:{}},setup(e){const a=e,n=(0,v.Y)(),c=(0,i.g)(),{t:m}=(0,d.QT)(),f=(0,l.Fl)((()=>{var e;return 8==a.taskTypeNum?null===(e=a.paramsData)||void 0===e||null===(e=e.risk_distribution)||void 0===e?void 0:e.web_scan_vuls_list.map((e=>({...e,...e.web_vuln_obj,vulnId:e.vul_id,vulnName:e.i18n_name,vulnLevel:e.risk_level}))):a.paramsData.vulns||a.paramsData.solve_items.map((e=>({...e.vul_msg,...e,vulnId:e.vul_id,vulnName:e.vul_msg.i18n_name,vulnLevel:(null==e?void 0:e.vul_level)||(null==e?void 0:e.vulnLevel)})))})),b=[{title:m("a68595c99d93674665c126f16848190166dec0dfa5ee7e4a3e52d9492127467fcc55bfe21ad520b9babeaa99745572b45b87f5fe02c49c1bce83d20b309605d4"),dataIndex:"vulnName",key:"vulnName"}],_=(0,t.iH)([]);function x(e,a){e?_.value.push(a.vulnId):_.value=_.value.filter((e=>e!==a.vulnId))}return"offline"==a.source&&"html"!=a.exportTypeTemplate&&(_.value=f.value.map((e=>e.vulnId))),(0,l.YP)((()=>c.getCurExpandVulnId),(e=>{e&&_.value.push(e)})),(e,d)=>{const i=(0,l.up)("a-table");return(0,l.wg)(),(0,l.iD)("div",o,[(0,l.Wm)(i,{columns:b,dataSource:f.value,rowKey:"vulnId",showHeader:!1,pagination:!1,expandedRowKeys:_.value,onExpand:x},{bodyCell:(0,l.w5)((({column:e,record:a})=>{var r;return["vulnName"==e.key?((0,l.wg)(),(0,l.iD)("span",{key:0,id:a.vulnId},[(0,l.Wm)(s.Z,{level:a.vulnLevel},null,8,["level"]),(0,l.Uk)(" "+(0,u.zw)(a.vulnName)+(0,u.zw)((a.vul_ifconfirm||a.vul_confirmed)&&1==(null===(r=(0,t.SU)(n).getPromCode)||void 0===r?void 0:r.vuln_verify)?(0,t.SU)(m)("onlineReport.verifiable"):""),1)],8,p)):(0,l.kq)("v-if",!0)]})),expandedRowRender:(0,l.w5)((({record:e})=>[(0,l.Wm)(r.Z,{paramsData:e,source:a.source,taskTypeNum:a.taskTypeNum},null,8,["paramsData","source","taskTypeNum"])])),_:1},8,["dataSource","expandedRowKeys"])])}}});var m=n(93379),f=n.n(m),b=n(7795),_=n.n(b),x=n(90569),h=n.n(x),y=n(3565),w=n.n(y),g=n(19216),T=n.n(g),k=n(44589),I=n.n(k),D=n(23059),N={};N.styleTagTransform=I(),N.setAttributes=w(),N.insert=h().bind(null,"head"),N.domAPI=_(),N.insertStyleElement=T(),f()(D.Z,N),D.Z&&D.Z.locals&&D.Z.locals;const C=(0,n(83744).Z)(c,[["__scopeId","data-v-467c1f7a"]])}}]);