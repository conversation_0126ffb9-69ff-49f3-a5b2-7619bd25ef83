<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.patrol.dao.AmPatrolPointDAO">

    <sql id="meta">
			a.ID_
			,a.GROUP_
			,a.NO_
			,a.NAME_
			,a.LNG_
			,a.LAT_
			,a.ADDRESS_
			,a.STATUS_
			,a.MEMO_
			,a.FLAG_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.patrol.orm.AmPatrolPoint">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into AM_PATROL_POINT(ID_,GROUP_,NO_,NAME_,LNG_,LAT_,ADDRESS_,STATUS_,MEMO_,FLAG_)
        values(#{id},#{group},#{no},#{name},#{lng},#{lat},#{address},'1',#{memo},'1')
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.dam.patrol.orm.AmPatrolPoint">
		update AM_PATROL_POINT
		set NO_=#{no},NAME_=#{name},LNG_=#{lng},LAT_=#{lat},ADDRESS_=#{address},MEMO_=#{memo}
		where ID_=#{id}
	</update>

    <select id="findIdByNo" resultType="String">
        select ID_ from AM_PATROL_POINT where FLAG_='1' and NO_=#{0}
    </select>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.patrol.vo.PatrolPointVo">
        select
        <include refid="meta"/>
        ,b.NAME_ group_name
        from AM_PATROL_POINT a left join AM_PATROL_POINT_GROUP b on a.GROUP_=b.ID_
        where a.FLAG_='1'
        <if test="group != null">and a.GROUP_=#{group}</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.NAME_ like
            concat('%',#{keyword},'%'))
        </if>
        order by a.NO_
    </select>

    <select id="listAll" resultType="com.zy.dam.patrol.vo.PatrolPointVo">
		select a.ID_,a.NO_,a.NAME_,a.LNG_,a.LAT_,a.ADDRESS_,b.NAME_ group_name
		from AM_PATROL_POINT a left join AM_PATROL_POINT_GROUP b on a.GROUP_=b.ID_
        where a.FLAG_='1' and a.LNG_ &gt; 0 and a.LAT_ &gt; 0
        order by a.NO_
    </select>

    <select id="listSimple" resultType="com.zy.model.IdNoName">
		select a.ID_,a.NO_,a.NAME_
		from AM_PATROL_POINT a
        where a.FLAG_='1'
        order by a.NO_
    </select>

    <!-- 获取唯一的资管-巡检点位数据 -->
    <select id="findOne" resultType="com.zy.dam.patrol.orm.AmPatrolPoint">
        select
        <include refid="meta"/>
        from AM_PATROL_POINT a where a.ID_=#{0}
    </select>

    <update id="updateStatus">
		update AM_PATROL_POINT set STATUS_=#{1} where ID_=#{0}
	</update>

    <!-- 删除 -->
    <update id="delete">
		update AM_PATROL_POINT set FLAG_='9' where ID_=#{0}
	</update>

    <insert id="insertGroup">
		insert into AM_PATROL_POINT_GROUP(ID_, NAME_, ORD_, FLAG_) values(uuid(), #{name}, #{ord}, '1')
	</insert>

    <update id="updateGroup">
		update AM_PATROL_POINT_GROUP set NAME_=#{name}, ORD_=#{ord} where ID_=#{id}
	</update>

    <update id="deleteGroup">
		update AM_PATROL_POINT_GROUP set FLAG_='9' where ID_=#{0}
	</update>

    <select id="listGroup" resultType="com.zy.dam.patrol.vo.PatrolPointGroup">
		select ID_,NAME_,ORD_ from AM_PATROL_POINT_GROUP where FLAG_='1' order by ORD_
	</select>

    <select id="findKpiIdByPoint" resultType="String">
        select KPI_ from AM_POINT_NORM where POINT_=#{0}
    </select>

    <insert id="insertPointTarget">
        insert into AM_POINT_NORM(POINT_, KPI_) values (#{0}, #{1})
    </insert>

    <delete id="deleteTargetBy">
        delete from AM_POINT_NORM where POINT_=#{0}
    </delete>

</mapper>
