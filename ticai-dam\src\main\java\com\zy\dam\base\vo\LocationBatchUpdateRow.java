package com.zy.dam.base.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zy.excel.ExcelField;
import com.zy.excel.ExcelHeader;
import com.zy.excel.ExcelRow;

/**
 * 网点批量修改导入导出行数据
 */
@ExcelHeader
public class LocationBatchUpdateRow extends ExcelRow {

    @ExcelField("网点编码")
    @ExcelProperty("网点编码")
    public String code;

    @ExcelField("网点名称")
    @ExcelProperty("网点名称")
    public String name;

    @ExcelField("网点备注")
    @ExcelProperty("网点备注")
    public String memo;

    public void appendMsg(String msg) {
        if (this.rowMsg == null) this.rowMsg = msg;
        else this.rowMsg += ";" + msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}
