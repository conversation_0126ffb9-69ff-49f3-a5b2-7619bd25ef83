<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.report.dao.RpWoDAO">

    <select id="countForType" resultType="com.zy.model.NameInt">
        select FAULT_ name_, count(0) value_
        from wo_bill a, wo_bill_detail b
        where a.ID_=b.BILL_ and a.STATUS_!='1' and a.FLAG_!='9'
        and a.REPORT_TIME_&gt;=#{0} and a.REPORT_TIME_ &lt; date_add(#{0},interval 1 month)
        group by b.FAULT_
    </select>

    <select id="countForRegion" resultType="com.zy.model.NameInt">
        select d.NAME_, count(0) value_
        from wo_bill a, wo_bill_detail b, am_location c,am_region d
        where a.ID_=b.BILL_ and a.LOCATION_ID_=c.ID_ and c.REGION_=d.ID_ and a.STATUS_!='1' and a.FLAG_!='9'
        and a.REPORT_TIME_&gt;=#{0} and a.REPORT_TIME_ &lt; date_add(#{0},interval 1 month)
        group by d.NAME_
    </select>

    <select id="listFaultMonth" resultType="com.zy.dam.report.vo.WoFaultDetailVo">
        select
        date_format(a.REPORT_TIME_, '%Y-%m-%d %H:%i:%s') report_time,
        d.NAME_ region_name, c.CODE_, a.PHONE_, b.SPEC_, b.FAULT_, b.SOLVE_, date_format(a.SOLVE_TIME_, '%Y-%m-%d %H:%i:%s') solve_time,TIMESTAMPDIFF(MINUTE, a.REPORT_TIME_,a.SOLVE_TIME_) TAKE_MINUTE_,a.LOCATION_ADDRESS_
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
        ,(select s.NAME_ FROM AM_ASSET n, SYS_DEPT s WHERE n.DEPT_=s.ID_ AND n.ID_= b.ASSET_) deviceUnit
       ,(select n.NAME_ from SYS_USER n where n.FLAG_='1' and n.ID_=a.USER_) service_staff
        from wo_bill a, wo_bill_detail b, am_location c,am_region d
        where a.ID_=b.BILL_ and a.LOCATION_ID_=c.ID_ and c.REGION_=d.ID_ and a.STATUS_!='1' and a.FLAG_!='9'
        and a.REPORT_TIME_&gt;=#{0} and a.REPORT_TIME_ &lt; date_add(#{0},interval 1 month)
        order by REPORT_TIME_
    </select>

    <select id="countTrend" resultType="com.zy.model.NameInt">
        select name_,count(0) value_ from (
            select date_format(a.REPORT_TIME_, '%Y-%m') name_
            from wo_bill a
            where a.STATUS_!='1' and a.FLAG_!='9'
            and a.REPORT_TIME_&gt;=#{0} and a.REPORT_TIME_ &lt;date_add(#{1},interval 1 month)
        ) b group by name_
        order by name_
    </select>

</mapper>
