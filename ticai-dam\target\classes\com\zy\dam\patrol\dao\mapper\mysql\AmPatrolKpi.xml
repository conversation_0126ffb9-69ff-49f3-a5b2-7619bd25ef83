<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.patrol.dao.AmPatrolKpiDAO">

    <sql id="meta">
			a.ID_
			,a.NO_
			,a.NAME_
			,a.CATEGORY_
			,a.MODE_
			,a.CONTENT_
			,a.POINT_
			,a.ORD_
			,a.STATUS_
			,a.FLAG_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.patrol.orm.AmPatrolKpi">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into AM_PATROL_KPI(ID_,NO_,NAME_,CATEGORY_,MODE_,CONTENT_,POINT_,ORD_,STATUS_,FLAG_)
        values(#{id},#{no},#{name},#{category},#{mode},#{content},#{point},#{ord},'1','1')
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.dam.patrol.orm.AmPatrolKpi">
		update AM_PATROL_KPI
		set NAME_=#{name},MODE_=#{mode},CONTENT_=#{content},POINT_=#{point},ORD_=#{ord}
		where ID_=#{id}
	</update>

    <update id="updateStatus">
		update AM_PATROL_KPI set STATUS_=#{1} where ID_=#{0}
	</update>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.patrol.vo.PatrolKpiVo">
        select
        <include refid="meta"/>
        ,(select count(0) from AM_PATROL_KPI_ITEM m where m.FLAG_='1' and m.KPI_=a.ID_) item_count
        from AM_PATROL_KPI a
        where a.FLAG_='1' and a.CATEGORY_=#{category}
        <if test="keyword != null">and a.NAME_ like concat('%',#{keyword},'%')</if>
        order by a.ORD_,a.NO_
    </select>

    <!-- 获取唯一的资管-巡检指标项数据 -->
    <select id="findOne" resultType="com.zy.dam.patrol.orm.AmPatrolKpi">
        select
        <include refid="meta"/>
        from AM_PATROL_KPI a where a.ID_=#{0}
    </select>

    <select id="findVo" resultType="com.zy.dam.patrol.vo.PatrolKpiVo">
        select
        <include refid="meta"/>
        from AM_PATROL_KPI a where a.ID_=#{0}
    </select>

    <!-- 删除 -->
    <update id="delete">
		update AM_PATROL_KPI set FLAG_='9' where ID_=#{0}
	</update>

    <insert id="insertCategory">
		insert into AM_PATROL_CATEGORY(ID_, NAME_, ORD_, FLAG_) values(uuid(), #{name}, #{ord}, '1')
	</insert>

    <update id="updateCategory">
		update AM_PATROL_CATEGORY set NAME_=#{name}, ORD_=#{ord} where ID_=#{id}
	</update>

    <update id="deleteCategory">
		update AM_PATROL_CATEGORY set FLAG_='9' where ID_=#{0}
	</update>

    <select id="listCategory" resultType="com.zy.dam.patrol.vo.PatrolKpiCategory">
		select ID_,NAME_,ORD_ from AM_PATROL_CATEGORY where FLAG_='1' order by ORD_
	</select>

    <insert id="insertItem">
		insert into AM_PATROL_KPI_ITEM(ID_, KPI_,NO_,CONTENT_,POINT_,ORD_,FLAG_) values(uuid(), #{kpi},#{no},#{content},#{point}, #{ord}, '1')
	</insert>

    <update id="deleteItem">
		update AM_PATROL_KPI_ITEM set FLAG_='9' where FLAG_='1' and KPI_=#{0}
	</update>

    <select id="listItem" resultType="com.zy.dam.patrol.vo.PatrolKpiItem">
		select ID_, KPI_,NO_,CONTENT_,POINT_,ORD_,FLAG_ from AM_PATROL_KPI_ITEM where FLAG_='1' and KPI_=#{0} order by ORD_,NO_
	</select>

    <select id="findNodeList" resultType="com.zy.model.VueTreeNode">
        select * from (
        select a.ID_,null PID_,a.NAME_ label, null leaf,a.ORD_ from AM_PATROL_CATEGORY a where a.FLAG_='1'
        union all
        select a.ID_,a.CATEGORY_ PID_,a.NAME_ label, 1 leaf,a.ORD_ from AM_PATROL_KPI a where a.FLAG_='1'
        ) p order by ORD_
    </select>

    <select id="pageAsset" resultType="com.zy.dam.patrol.vo.AssetKpiVo">
        select
        a.ID_
        ,a.NO_
        ,a.NAME_
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,(select m.NAME_ from AM_LOCATION m where m.ID_=a.LOCATION_) location_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=a.TYPE_) type_name
        ,(select count(0) from AM_ASSET_KPI m where m.ASSET_=a.ID_) kpi_count
        from AM_ASSET a
        where a.FLAG_='1'
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.NAME_ like
            concat('%',#{keyword},'%'))
        </if>
        <if test="type != null">and a.TYPE_=#{type}</if>
        <if test="region != null">and a.REGION_=#{region}</if>
        <if test="location != null">and a.LOCATION_=#{location}</if>
        order by a.NO_ desc
    </select>

    <insert id="insertAssetKpi">
        insert into AM_ASSET_KPI(ASSET_,KPI_,ORD_) values(#{0}, #{1}, #{2})
    </insert>

    <insert id="deleteAssetKpi">
        delete from AM_ASSET_KPI where ASSET_=#{0}
    </insert>

    <select id="findAssetKpi" resultType="String">
        select KPI_ from AM_ASSET_KPI where ASSET_=#{0} order by ORD_
    </select>

    <select id="pageAssetType" resultType="com.zy.dam.patrol.vo.AssetTypeKpiVo">
        select a.CODE_,a.NAME_
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=a.PCODE_) pname
        ,(select count(0) from AM_ASSET_TYPE_KPI m where m.TYPE_=a.CODE_) kpi_count
        from AM_ASSET_TYPE a
        where a.FLAG_='1'
        <if test="name != null">and a.NAME_ like concat('%', #{name}, '%')</if>
        order by a.ORD_,a.CODE_
    </select>

    <insert id="insertAssetTypeKpi">
        insert into AM_ASSET_TYPE_KPI(TYPE_,KPI_,ORD_) values(#{0}, #{1}, #{2})
    </insert>

    <insert id="deleteAssetTypeKpi">
        delete from AM_ASSET_TYPE_KPI where TYPE_=#{0}
    </insert>

    <select id="findAssetTypeKpi" resultType="String">
        select KPI_ from AM_ASSET_TYPE_KPI where TYPE_=#{0} order by ORD_
    </select>

    <select id="findAll" resultType="com.zy.dam.patrol.orm.AmPatrolKpi">
        select
        <include refid="meta"/>
        from AM_PATROL_KPI a
        where a.STATUS_ != '9'
        order by a.ORD_
    </select>

</mapper>
