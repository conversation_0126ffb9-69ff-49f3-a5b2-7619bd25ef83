package com.zy.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Cookie安全拦截器
 */
@Component
@Slf4j
public class CookieSecurityInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 在请求处理前，包装响应对象
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 在响应完成后，检查并修复cookie安全属性
        String[] cookieHeaders = response.getHeaders("Set-Cookie").toArray(new String[0]);
        
        for (String cookieHeader : cookieHeaders) {
            if (cookieHeader.contains("zy_token=") || cookieHeader.contains("vue_token=")) {
                
                // 移除原有的Set-Cookie头
                response.setHeader("Set-Cookie", null);
                
                // 重新设置带有安全属性的cookie
                String securedCookie = addSecurityAttributes(cookieHeader);
                response.addHeader("Set-Cookie", securedCookie);

            }
        }
    }

    /**
     * 为cookie添加安全属性
     */
    private String addSecurityAttributes(String cookieValue) {
        StringBuilder secured = new StringBuilder(cookieValue);
        
        // 添加Secure属性
        if (!cookieValue.contains("Secure")) {
            secured.append("; Secure");
        }
        
        // 添加SameSite属性
        if (!cookieValue.contains("SameSite")) {
            secured.append("; SameSite=Strict");
        }
        
        // 添加Path属性
        if (!cookieValue.contains("Path")) {
            secured.append("; Path=/");
        }
        
        return secured.toString();
    }
}
