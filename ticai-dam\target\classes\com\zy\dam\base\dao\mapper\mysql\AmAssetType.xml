<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.base.dao.AmAssetTypeDAO">

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.base.orm.AmAssetType">
        insert into AM_ASSET_TYPE(CODE_,PCODE_,NAME_,ORD_,NO_TYPE_,FLAG_)
        values(#{code},#{pcode},#{name},#{ord},'0','1')
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.dam.base.orm.AmAssetType">
        update AM_ASSET_TYPE set NAME_=#{name},ORD_=#{ord} where CODE_=#{code}
    </update>

    <select id="list" resultType="com.zy.dam.base.vo.AssetTypeVo">
        select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname
        from AM_ASSET_TYPE a
        where a.FLAG_='1'
        order by a.ORD_,a.CODE_
    </select>

    <select id="findOne" resultType="com.zy.dam.base.orm.AmAssetType">
        select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_
        from AM_ASSET_TYPE a where a.CODE_=#{0} and a.FLAG_='1'
    </select>

    <!-- 获取唯一的基础-资产类型数据 -->
    <select id="findVo" resultType="com.zy.dam.base.vo.AssetTypeVo">
        select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname
        from AM_ASSET_TYPE a where a.CODE_=#{0} and a.FLAG_='1'
    </select>

    <!-- 删除 -->
    <delete id="delete">
        delete from AM_ASSET_TYPE where CODE_=#{0}
    </delete>

    <update id="updateNoType">
        update AM_ASSET_TYPE set NO_TYPE_=#{1} where CODE_=#{0} and FLAG_='1'
    </update>

    <select id="listNode" resultType="com.zy.model.VueTreeNode">
        select a.CODE_ id,a.PCODE_ pid,a.NAME_ label
        from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
    </select>

</mapper>
