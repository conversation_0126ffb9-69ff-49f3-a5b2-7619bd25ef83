<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.base.dao.AmRegionDAO">

    <sql id="meta">
			a.ID_
			,a.CODE_
			,a.NAME_
			,a.DEPT_
			,a.REGION_
			,a.SCOPE_
			,a.USER_
			,a.ORD_
			,a.FLAG_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.base.orm.AmRegion">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into AM_REGION(ID_,CODE_,NAME_,DEPT_,REGION_,SCOPE_,USER_,ORD_,FLAG_)
        values(#{id},#{code},#{name},#{dept},#{region},#{scope},#{user},#{ord},'1')
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.dam.base.orm.AmRegion">
		update AM_REGION
		set CODE_=#{code},NAME_=#{name},DEPT_=#{dept},REGION_=#{region},SCOPE_=#{scope},USER_=#{user},ORD_=#{ord}
		where ID_=#{id}
	</update>

    <select id="findIdByCode" resultType="String">
		select ID_ from AM_REGION where FLAG_='1' and CODE_=#{0}
	</select>

    <select id="list" resultType="com.zy.dam.base.vo.RegionVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.FULL_NAME_ from SYS_REGION m where m.CODE_=a.REGION_) region_name
        ,b.NAME_ user_name,b.PHONE_ user_phone
        from AM_REGION a left join SYS_USER b on a.USER_=b.ID_ and b.STATUS_!='9'
        where a.FLAG_='1'
        order by a.ORD_,a.CODE_
    </select>

    <!-- 删除 -->
    <update id="delete">
		update AM_REGION set FLAG_='9' where ID_=#{0}
	</update>

    <select id="listDept" resultType="com.zy.model.VueTreeNode">
        select a.ID_,'0' pid,a.NAME_ label_
        from SYS_DEPT a
        where a.FLAG_='1' and a.TYPE_='1' and a.PID_ != '0'
        order by a.ORD_,a.NO_
    </select>

    <select id="listOption" resultType="com.zy.model.VueTreeNode">
        select a.ID_, a.DEPT_ pid,a.NAME_ label_, 1 leaf_
        from AM_REGION a
        where a.FLAG_='1'
        order by a.ORD_
    </select>

</mapper>
