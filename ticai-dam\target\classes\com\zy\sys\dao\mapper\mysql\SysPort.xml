<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.sys.dao.SysPortDAO">

    <sql id="meta">
			a.ID_
			,a.CODE_
			,a.NAME_
			,a.LABEL_
			,a.SECRET_
			,a.FLAG_
			,a.CTIME_
			,a.MTIME_
	</sql>

    <insert id="insert" parameterType="com.zy.sys.orm.SysPort">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into SYS_PORT(ID_,CODE_,NAME_,LABEL_,SECRET_,FLAG_,CTIME_,MTIME_)
        values(#{id},#{code},#{name},#{label},#{secret},'1', now(), now())
    </insert>

    <update id="update" parameterType="com.zy.sys.orm.SysPort">
		update SYS_PORT
		set NAME_=#{name},LABEL_=#{label},SECRET_=#{secret},MTIME_=now()
		where ID_=#{id}
	</update>

    <select id="findIdByCode" resultType="String">
        select ID_ from SYS_PORT where CODE_=#{0} and FLAG_='1'
    </select>

    <update id="updateSecret">
		update SYS_PORT set SECRET_=#{1},MTIME_=now() where ID_=#{0}
	</update>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.sys.orm.SysPort">
        select
        <include refid="meta"/>
        from SYS_PORT a
        where a.FLAG_='1'
        <if test="keyword != null">and (a.CODE_ like concat('%',#{keyword},'%') or a.NAME_ like concat('%',#{keyword},'%'))</if>
        <choose>
            <when test="sort != null">
                order by ${sortColumnName}
                <if test="order == 'desc'">desc</if>
            </when>
            <otherwise>order by a.CODE_</otherwise>
        </choose>
    </select>

    <!-- 获取唯一的林长制-应用平台数据 -->
    <select id="findOne" resultType="com.zy.sys.orm.SysPort">
        select
        <include refid="meta"/>
        from SYS_PORT a where a.ID_=#{0}
    </select>

    <!-- 删除 -->
    <delete id="delete">
		update SYS_PORT set FLAG_='9' where ID_=#{0}
	</delete>

    <select id="findAllInfo" resultType="com.zy.model.ValueText">
        select CODE_ value_, concat(SECRET_, ';', ID_) text_ from SYS_PORT where FLAG_='1'
    </select>

    <select id="findAll" resultType="com.zy.sys.orm.SysPort">
        select ID_,CODE_,SECRET_,NAME_ from SYS_PORT where FLAG_='1' order by CODE_
    </select>

    <select id="findAllForOption" resultType="com.zy.model.ValueLabel">
        select CODE_ value_,NAME_ label_ from SYS_PORT where FLAG_='1' order by CODE_
    </select>


</mapper>
