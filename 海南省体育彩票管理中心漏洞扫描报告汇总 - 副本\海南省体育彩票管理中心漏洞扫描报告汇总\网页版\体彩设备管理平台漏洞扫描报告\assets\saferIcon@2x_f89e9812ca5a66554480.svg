<?xml version="1.0" encoding="UTF-8"?>
<svg width="60px" height="104px" viewBox="0 0 60 104" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 27</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="56.6144349" height="73.544741"></rect>
        <filter x="-42.4%" y="-32.6%" width="184.8%" height="165.3%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="8" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="73.2828776%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#00498E" offset="0%"></stop>
            <stop stop-color="#0A88FF" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="50%" cy="15.3645833%" fx="50%" fy="15.3645833%" r="123.746582%" gradientTransform="translate(0.500000,0.153646),scale(1.000000,0.769796),rotate(90.000000),translate(-0.500000,-0.153646)" id="radialGradient-5">
            <stop stop-color="#CCE6FF" offset="0%"></stop>
            <stop stop-color="#0A88FF" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#0A88FF" stop-opacity="0.31" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="85.4594624%" id="linearGradient-7">
            <stop stop-color="#0061AF" offset="0%"></stop>
            <stop stop-color="#046FC6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-8">
            <stop stop-color="#DEEEFF" offset="0.0245847902%"></stop>
            <stop stop-color="#388FE4" stop-opacity="0.125136582" offset="100%"></stop>
        </linearGradient>
        <path d="M28.3580864,18.3929053 C29.0727846,18.3929053 29.6613596,18.9819364 29.6613596,19.6971885 L29.6613596,19.6971885 L29.6613596,23.1472278 C36.1777258,23.7572958 41.3697983,28.9533916 41.9793938,35.4748074 L41.9793938,35.4748074 L45.4057412,35.4748074 C46.14146,35.4748074 46.7090145,36.0638385 46.7090145,36.7790906 C46.7090145,37.4943426 46.1204394,38.0833737 45.4057412,38.0833737 L45.4057412,38.0833737 L41.9793938,38.0833737 C41.3697983,44.6047896 36.1777258,49.8008854 29.6613596,50.4109533 L29.6613596,50.4109533 L29.6613596,53.8609927 C29.6613596,54.5762447 29.0727846,55.1652758 28.3580864,55.1652758 C27.6433881,55.1652758 27.0548131,54.5762447 27.0548131,53.8609927 L27.0548131,53.8609927 L27.0548131,50.4109533 C20.5384469,49.8008854 15.3463745,44.6047896 14.7367789,38.0833737 L14.7367789,38.0833737 L11.289411,38.0833737 C10.5747128,38.0833737 9.98613777,37.4943426 9.98613777,36.7790906 C9.98613777,36.0638385 10.5747128,35.4748074 11.289411,35.4748074 L11.289411,35.4748074 L14.7367789,35.4748074 C15.3463745,28.9533916 20.5384469,23.7572958 27.0548131,23.1472278 L27.0548131,23.1472278 L27.0548131,19.6971885 C27.0548131,18.9819364 27.6433881,18.3929053 28.3580864,18.3929053 Z M36.6556937,31.5284947 C35.9998058,30.8683199 34.9364901,30.8683199 34.2819522,31.5288472 L34.2819522,31.5288472 L26.4050889,39.4466196 L22.4126915,35.4330263 L22.2931526,35.3236419 C21.6347678,34.7767196 20.6570392,34.8131705 20.0398069,35.4328359 C19.3860303,36.090894 19.3860303,37.1571494 20.0394568,37.8148555 L20.0394568,37.8148555 L25.2191838,43.0225785 L25.3438571,43.1357144 C25.6454834,43.3834061 26.0193238,43.5161995 26.4057279,43.5161995 C26.8464634,43.5161995 27.2728168,43.3424653 27.5917257,43.0231266 L27.5917257,43.0231266 L36.6555614,33.9089857 L36.7641113,33.7888359 C37.3068609,33.1272665 37.270685,32.147514 36.6556937,31.5284947 Z" id="path-9"></path>
        <filter x="-10.9%" y="-10.9%" width="121.8%" height="121.8%" filterUnits="objectBoundingBox" id="filter-10">
            <feGaussianBlur stdDeviation="3" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.650103802 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="1-仪表盘" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(-701.000000, -1104.000000)" id="编组-88">
            <g transform="translate(315.000000, 1106.000000)">
                <g id="编组-27" transform="translate(387.868450, 0.000000)">
                    <g id="未知">
                        <g id="路径" transform="translate(0.000000, 28.345369)">
                            <mask id="mask-2" fill="white">
                                <use xlink:href="#path-1"></use>
                            </mask>
                            <g id="蒙版"></g>
                            <path d="M28.4527974,-27.9623234 L29.8560266,-26.7116748 C35.193959,-21.9484785 43.4879737,-18.8817066 54.5197008,-17.6021651 L56.6144349,-17.354512 L56.6144349,16.2850458 C56.6144349,27.4294391 47.4065014,37.137444 29.2373117,45.1490245 L28.2546469,45.5824176 L27.2922015,45.1118766 C8.92486129,36.1096832 -3.42472899e-13,26.682352 -3.42472899e-13,16.2850458 L-3.42472899e-13,-17.6186753 L2.35758681,-17.6186753 C10.3038272,-17.6186753 18.5735785,-20.7225952 26.9363394,-26.8520116 L28.4527974,-27.9623234 Z" fill="#5F96EA" fill-rule="nonzero" opacity="0.854106105" filter="url(#filter-3)" mask="url(#mask-2)"></path>
                        </g>
                        <path d="M28.4527974,6.89481947 L29.8560266,8.14546803 C35.193959,12.9086643 43.4879737,15.9754362 54.5197008,17.2549777 L56.6144349,17.5026309 L56.6144349,51.1421886 C56.6144349,62.286582 47.4065014,71.9945869 29.2373117,80.0061674 L28.2546469,80.4395604 L27.2922015,79.9690194 C8.92486129,70.9668261 0,61.5394948 0,51.1421886 L0,17.2384675 L2.35758681,17.2384675 C10.3038272,17.2384675 18.5735785,14.1345477 26.9363394,8.00513124 L28.4527974,6.89481947 Z" id="路径" fill="url(#linearGradient-4)" fill-rule="nonzero"></path>
                        <path d="M28.4836418,-0.642276791 L30.188925,0.877582013 C35.4667487,5.58714152 43.6752969,8.59899185 54.5784053,9.86361642 L54.5784053,9.86361642 L57.1144349,10.1634424 L57.1144349,44.2473692 C57.1144349,49.938988 54.7760512,55.2704481 50.102097,60.1982454 C45.492333,65.0583663 38.5975763,69.5304531 29.439079,73.5688301 L29.439079,73.5688301 L28.2437987,74.0959948 L27.0721506,73.5231739 C17.8223166,68.989649 10.9548235,64.343652 6.39546755,59.5279254 C1.76400591,54.6360385 -0.5,49.5614475 -0.5,44.2473692 L-0.5,44.2473692 L-0.5,9.84364808 L2.35758681,9.84364808 C10.2109272,9.84364808 18.3785682,6.76273926 26.6409617,0.70688643 L26.6409617,0.70688643 L28.4836418,-0.642276791 Z" id="路径" stroke="#FFFFFF" fill="url(#radialGradient-5)" fill-rule="nonzero"></path>
                        <path d="M28.1576457,6.00429583 L29.0575375,6.80437172 C33.6882627,10.9263782 40.8755176,13.6006935 50.4500682,14.7084978 L50.4500682,14.7084978 L51.8022789,14.8679732 L51.8022789,43.1572652 C51.8022789,47.7973046 49.8676168,52.1316105 46.0530074,56.1435345 C42.1738219,60.223375 36.363775,63.9651391 28.6588437,67.354249 L28.6588437,67.354249 L28.0296011,67.6310891 L27.414282,67.3309962 C19.6170492,63.5187862 13.8192832,59.6219902 9.97507931,55.5715803 C6.20311629,51.5972862 4.32529965,47.4823888 4.32529965,43.1572652 L4.32529965,43.1572652 L4.32529965,14.6978368 L5.84401956,14.6978368 C12.7408802,14.6978368 19.9239104,12.0236082 27.1848597,6.71479911 L27.1848597,6.71479911 L28.1576457,6.00429583 Z" id="路径" stroke="url(#linearGradient-6)" fill-rule="nonzero"></path>
                    </g>
                    <g id="形状结合备份" fill-rule="nonzero">
                        <use fill="url(#linearGradient-7)" xlink:href="#path-9"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                        <path stroke="url(#linearGradient-8)" stroke-width="1" d="M28.3580864,17.8929053 C28.8194979,17.8929053 29.2433531,18.0694833 29.5640415,18.3593212 C29.8527642,18.6202685 30.0571337,18.9727507 30.1309987,19.3684964 L30.1545305,19.5406098 L30.1605119,22.6991088 C33.297988,23.0948119 36.1171741,24.5186581 38.2697361,26.6227807 C40.347402,28.6536927 41.8038273,31.3182414 42.3264122,34.3029143 L42.3892539,34.6940092 L45.4305458,34.9749698 C45.9259707,34.9814632 46.3641545,35.1822261 46.6818652,35.5001831 C47.0092126,35.8277842 47.2090145,36.2810755 47.2090145,36.7790906 C47.2090145,37.2406648 47.0326006,37.6646427 46.7431181,37.9854344 C46.4822675,38.2744975 46.1298942,38.4790564 45.7342994,38.5529867 L45.5622535,38.576539 L42.4275934,38.5825195 C42.0322586,41.7223815 40.6095248,44.5436923 38.5070795,46.6978731 C36.477749,48.7771399 33.8152938,50.2346802 30.8329853,50.7577586 L30.4422003,50.8206611 L30.1611909,53.8857054 C30.154566,54.3706984 29.9532561,54.8126898 29.6314251,55.1347702 C29.3039952,55.4624539 28.8525759,55.6652758 28.3580864,55.6652758 C27.8966748,55.6652758 27.4728197,55.4886979 27.1521313,55.19886 C26.8634086,54.9379126 26.659039,54.5854305 26.5851741,54.1896847 L26.5616423,54.0175713 L26.5556609,50.8590723 C23.4181847,50.4633693 20.5989986,49.039523 18.4464367,46.9354005 C16.3688203,44.9045369 14.912415,42.2400669 14.3897979,39.2554804 L14.3269517,38.8643969 L11.2646565,38.5832042 C10.7798683,38.57656 10.3380304,38.3750757 10.0160723,38.0528681 C9.68878308,37.7253253 9.48613777,37.2737697 9.48613777,36.7790906 C9.48613777,36.3175164 9.66255161,35.8935385 9.95203416,35.5727467 C10.2128853,35.283683 10.5652596,35.0791238 10.9608553,35.005194 L11.1329018,34.9816419 L14.28858,34.9756566 C14.6839156,31.8357967 16.106649,29.0144877 18.2090933,26.8603081 C20.2384238,24.7810412 22.9008789,23.3235009 25.8831875,22.8004226 L26.2739724,22.7375201 L26.5549819,19.6724757 C26.5616068,19.1874828 26.7629166,18.7454914 27.0847476,18.423411 C27.4121776,18.0957273 27.8635968,17.8929053 28.3580864,17.8929053 Z M35.5112167,31.5487311 C35.2101937,31.5487758 34.8992116,31.6669651 34.6203523,31.8976349 L34.6203523,31.8976349 L26.4055315,40.1551201 L21.9571176,35.6947613 L21.838607,35.6113982 C21.6346677,35.4861369 21.400794,35.4292433 21.1695836,35.4404474 C20.8875645,35.4541137 20.6095313,35.5693675 20.3940551,35.7856931 C20.1789506,36.0022069 20.0644922,36.2822578 20.0508246,36.5661961 C20.0366243,36.8612002 20.1412924,37.1543042 20.4090372,37.4773781 L20.4090372,37.4773781 L25.678855,42.7635472 L25.7867387,42.8393887 C25.9724461,42.9549995 26.186179,43.0161995 26.4057279,43.0161995 C26.6847601,43.0161995 26.9497609,42.9072354 27.2529091,42.654733 L27.2529091,42.654733 L36.3911513,33.454816 L36.4742039,33.335234 C36.598976,33.1295073 36.6555358,32.8938468 36.6443672,32.6609981 C36.6307632,32.3773756 36.516368,32.0976827 36.3009881,31.8808922 C36.0849589,31.663451 35.8051641,31.5486874 35.5112167,31.5487311 Z"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>