"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[1690],{89345:(e,n,l)=>{l.d(n,{Z:()=>i});var t=l(8081),a=l.n(t),o=l(23645),r=l.n(o)()(a());r.push([e.id,".secondaryTitle .anticon[data-v-fb7e9b84] {\n  font-size: 12px;\n  cursor: pointer;\n}\n.scrollTable[data-v-fb7e9b84] {\n  width: 100%;\n}\n.scrollTable .title[data-v-fb7e9b84] {\n  display: flex;\n  height: auto;\n  border-bottom: 1px solid #ebeef2;\n  background: #f7f8fa;\n}\n.scrollTable .title p[data-v-fb7e9b84] {\n  margin-bottom: 0;\n  padding: 18px 24px;\n  font-weight: 600;\n  text-align: left;\n  color: #2e2e2e;\n  overflow-wrap: break-word;\n}\n.scrollTable .scroller[data-v-fb7e9b84] {\n  width: 100%;\n  border-bottom: 1px solid #ebeef2;\n}\n.scrollTable .scroller .row[data-v-fb7e9b84] {\n  display: flex;\n  width: 100%;\n  border-bottom: 1px solid #ebeef2;\n}\n.scrollTable .scroller .row[data-v-fb7e9b84]:hover {\n  background: #f7f8fa;\n}\n.scrollTable .scroller .row .col[data-v-fb7e9b84] {\n  overflow-wrap: break-word;\n}\n.scrollTable .scroller .row .col > div[data-v-fb7e9b84] {\n  padding: 4px 24px;\n  line-height: 24px;\n}\n",""]);const i=r},11690:(e,n,l)=>{l.r(n),l.d(n,{default:()=>j});var t=l(66252),a=l(3577),o=l(2262),r=l(49963),i=l(49421),s=l(79414);const d={class:"otherInfo"},c={class:"secondaryTitle"},b={class:"scrollTable"},f={class:"title"},u=(0,t.aZ)({__name:"OtherInfo",props:{paramsData:{},serialNum:{},source:{}},setup(e){const n=e,{t:l}=s.i18n.global,u=(0,o.iH)({}),m=(0,t.Fl)((()=>{var e;return null===(e=n.paramsData)||void 0===e?void 0:e.other_info_data.map((e=>(u.value[e.info_name]=!0,e)))}));function p(e){return e.map(((e,n)=>{let l={title:e,dataIndex:n.toString()};return 0==n&&(l.width=300),l}))}const v=[24,25,241];return(e,l)=>{const s=(0,t.up)("DynamicScrollerItem"),h=(0,t.up)("dynamic-scroller");return(0,t.wg)(),(0,t.iD)("div",d,[((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)(m.value,((e,l)=>{var d,m,w,g;return(0,t.wg)(),(0,t.iD)("div",{key:l},[(0,t._)("div",c,[(0,t.Uk)((0,a.zw)(n.serialNum?`${n.serialNum}.${l+1}  ${e.info_name}`:e.info_name)+" ",1),(0,t.Wm)((0,o.SU)(i.Z),{onClick:n=>u.value[e.info_name]=!u.value[e.info_name],style:(0,a.j5)({transform:u.value[e.info_name]?"rotate(0deg)":"rotate(180deg)"})},null,8,["onClick","style"])]),(0,t.wy)((0,t._)("div",b,[(0,t._)("div",f,[((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)(p(e.column_names),((n,l)=>((0,t.wg)(),(0,t.iD)("p",{key:n.dataIndex,style:(0,a.j5)({width:0==l?"300px":`calc((100% - 300px) / ${p(e.column_names).length-1})`})},(0,a.zw)(n.title),5)))),128))]),(0,t._)("div",{class:"scroller",style:(0,a.j5)({height:e.content.length>10?"300px":""})},[(0,t.Wm)(h,{buffer:800,prerender:200,style:{height:"100%"},"min-item-size":60,items:(m=e.content,w=e.mess_type,g=null===(d=e.column_names)||void 0===d?void 0:d.length,m.map(((e,l)=>(v.includes(w)&&"offline"==n.source&&e[1]&&(e[1]=(e=>{const n=e.charAt(0),l=e.charAt(e.length-1),t=e.slice(1,e.length-1),a="*".repeat(t.length);return e.length<3?n+"*":n+a+l})(e[1])),{id:l,data:e.length>g?e.slice(0,g):e}))))},{default:(0,t.w5)((({item:n,index:l,active:o})=>[(0,t.Wm)(s,{item:n,active:o,"data-index":l},{default:(0,t.w5)((()=>[(0,t._)("div",{class:"row",style:(0,a.j5)({borderBottom:e.content.length>1&&l!==e.content.length-1?"1px solid #ebeef2":"0px"})},[((0,t.wg)(!0),(0,t.iD)(t.HY,null,(0,t.Ko)(n.data,((e,l)=>((0,t.wg)(),(0,t.iD)("div",{class:"col",key:l,style:(0,a.j5)({width:0==l?"300px":`calc((100% - 300px) / ${n.data.length-1})`})},[(0,t._)("div",null,(0,a.zw)(e||"--"),1)],4)))),128))],4)])),_:2},1032,["item","active","data-index"])])),_:2},1032,["items"])],4)],512),[[r.F8,u.value[e.info_name]]])])})),128))])}}});var m=l(93379),p=l.n(m),v=l(7795),h=l.n(v),w=l(90569),g=l.n(w),_=l(3565),x=l.n(_),y=l(19216),k=l.n(y),T=l(44589),D=l.n(T),Z=l(89345),I={};I.styleTagTransform=D(),I.setAttributes=x(),I.insert=g().bind(null,"head"),I.domAPI=h(),I.insertStyleElement=k(),p()(Z.Z,I),Z.Z&&Z.Z.locals&&Z.Z.locals;const j=(0,l(83744).Z)(u,[["__scopeId","data-v-fb7e9b84"]])}}]);