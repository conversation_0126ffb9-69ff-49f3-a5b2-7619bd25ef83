"use strict";(self.webpackChunkrsas_web=self.webpackChunkrsas_web||[]).push([[83],{83:(e,t,r)=>{r.d(t,{batchExpAttackApi:()=>n,batchWebExpAttackApi:()=>g,correctConfigByHost:()=>k,correctConfigByImage:()=>A,correctImageVulApi:()=>c,correctSiteVulApi:()=>l,correctVulApi:()=>o,doAttackApi:()=>i,doVulConfirmApi:()=>_,getExpAttackProcessApi:()=>u,getSysatkApi:()=>a,getVulConfirmProcessApi:()=>d,getVulConfirmResultApi:()=>b,getWarningInfoApi:()=>p,getWebVulnInfoApi:()=>h,getWebWarningInfoApi:()=>f});var s=r(14068);const a=e=>s.h.get({url:`/interface/report/sys/host-sys-get-atk/${e.task_id}/${e.scan_vuls_id}`}),i=e=>s.h.post({url:"/interface/report/sys/host-sys-do-atk",data:e}),o=e=>s.h.post({url:"/interface/report/sys/host-correct-vul/",data:e},{isReturnNativeResponse:!0}),c=e=>s.h.post({url:"/interface/report/image-scan/image-correct-vul/",data:e},{isReturnNativeResponse:!0}),n=e=>s.h.post({url:"/interface/report/sys/host-sys-atk-batch",data:e}),p=e=>s.h.get({url:`/interface/report/sys/host-sys-atk-warning/${e.expType}`},{isReturnNativeResponse:!0}),u=e=>s.h.get({url:`/interface/report/sys/host-sys-atk-process/${e.task_id}/${e.task_ip_id}?first=${e.first||"no"}`}),l=e=>s.h.post({url:"/interface/report/web-scan/site-correct-vul/",data:e}),f=e=>s.h.get({url:`/interface/report/web-scan/get_warning_info/${e.expType}`}),g=e=>s.h.post({url:"/interface/report/web-scan/vul_confirm_batch_do",data:e}),h=e=>s.h.get({url:`/interface/report/web-scan/vul_confirm/${e.task_id}/${e.web_scan_vuls_id}`}),_=e=>s.h.post({url:"/interface/report/web-scan/vul_confirm_do",data:e}),d=e=>s.h.get({url:`/interface/report/web-scan/vul_confirm_progress/${e.task_id}/${e.site_id}`}),b=e=>s.h.get({url:`/interface/report/web-scan/vul_confirm_result/${e.scan_vuls_id}`}),k=e=>s.h.post({url:"/interface/report/sys/host-correct-config/",data:e},{isReturnNativeResponse:!0}),A=e=>s.h.post({url:"/interface/report/image-scan/image-correct-config/",data:e},{isReturnNativeResponse:!0})}}]);