<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.asset.dao.AmAssetBackDAO">

    <sql id="meta">
			a.ID_
			,a.NO_
			,a.USER_
			,a.TIME_
			,a.DEPT_
            ,a.REGION_
			,a.MEMO_
			,a.REF_
			,a.STATUS_
			,a.CDEPT_
			,a.CUSER_
			,a.CTIME_
			,a.CHECK_USER_
			,a.CHECK_TIME_
			,a.CHECK_MEMO_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.asset.orm.AmAssetBack">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into
        AM_ASSET_BACK(ID_,NO_,USER_,TIME_,DEPT_,REGION_,MEMO_,REF_,STATUS_,CDEPT_,CUSER_,CTIME_,CHECK_USER_,CHECK_TIME_,CHECK_MEMO_)
        values(#{id},#{no},#{user},#{time},#{dept},#{region},#{memo},#{ref},#{status},#{cdept},#{cuser},now(),#{checkUser},#{checkTime},#{checkMemo})
    </insert>

    <!-- 更新数据 -->
    <update id="updateCheck" parameterType="com.zy.dam.asset.orm.AmAssetBack">
		update AM_ASSET_BACK
		set STATUS_=#{status},CHECK_USER_=#{checkUser},CHECK_TIME_=#{checkTime},CHECK_MEMO_=#{checkMemo}
		where ID_=#{id}
	</update>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.asset.vo.AssetBackVo">
        select
        <include refid="meta"/>
        ,(select count(0) from AM_ASSET_BACK_DETAIL m where m.BILL_=a.ID_) asset_count
        ,b.NAME_ user_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        from AM_ASSET_BACK a left join SYS_USER b on a.USER_=b.ID_
        <where>
            <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.MEMO_ like
                concat('%',#{keyword},'%'))
            </if>
            <if test="status != null">and a.STATUS_=#{status}</if>
            <if test="user != null">and a.USER_=#{user}</if>
            <if test="userKey != null">and (b.NAME_ like concat('%',#{userKey},'%') or b.NO_ like
                concat('%',#{userKey},'%'))
            </if>
            <if test="begin != null">and a.TIME_ &gt;= #{begin}</if>
            <if test="end != null">and a.TIME_ &lt; date_add(#{end}, interval 1 day)</if>
            <if test="deptIds != null">
                <foreach collection="deptIds" item="deptId" open="and a.CDEPT_ in (" separator="," close=")">#{deptId}
                </foreach>
            </if>
        </where>
        order by a.CTIME_ desc
    </select>

    <!-- 获取唯一的资管-资产领用退库数据 -->
    <select id="findOne" resultType="com.zy.dam.asset.orm.AmAssetBack">
        select
        <include refid="meta"/>
        from AM_ASSET_BACK a where a.ID_=#{0}
    </select>

    <!-- 删除 -->
    <update id="delete">
		update AM_ASSET_BACK set STATUS_='9' where ID_=#{0}
	</update>

    <insert id="insertDetail">
		insert into AM_ASSET_BACK_DETAIL(BILL_, ASSET_, ORD_, DEPT_, REGION_, LOCATION_, USE_DEPT_, USE_USER_, ASSET_STATUS_,LAST_LNG_,LAST_LAT_,LAST_LOC_ADDR_)
		values(#{id}, #{asset}, #{ord}, #{dept}, #{region}, #{location}, #{useDept}, #{useUser}, #{assetStatus}, #{lastLng}, #{lastLat}, #{lastLocAddr})
	</insert>

    <!-- 更新资产退库，置为空置 -->
    <update id="updateBack">
        update AM_ASSET a,AM_ASSET_BACK_DETAIL b, AM_ASSET_BACK c
        set a.STATUS_='1',a.USE_DEPT_=null,a.USE_USER_=null,a.REGION_=c.REGION_,a.LOCATION_=NULL,a.LNG_=NULL,a.LAT_=NULL,a.LOC_ADDR_=NULL
        where a.ID_=b.ASSET_ and b.BILL_=c.ID_ and c.ID_=#{0}
    </update>

    <select id="findVo" resultType="com.zy.dam.asset.vo.AssetBackVo">
        select
        <include refid="meta"/>
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        from AM_ASSET_BACK a where a.ID_=#{0}
    </select>

    <select id="findDetail" resultType="com.zy.dam.asset.vo.AssetDetailVo">
        select a.ID_,a.TYPE_,a.NAME_,a.NO_,a.BO_DATE_,a.STATUS_
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.TYPE_) type_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.USE_DEPT_) use_dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USE_USER_) use_user_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=b.DEPT_) when_dept_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=b.REGION_) when_region_name
        ,(select m.NAME_ from AM_LOCATION m where m.ID_=b.LOCATION_) when_location_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=b.USE_DEPT_) when_use_dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=b.USE_USER_) when_use_user_name
        ,b.ASSET_STATUS_ when_status
        from AM_ASSET a,AM_ASSET_BACK_DETAIL b
        where a.ID_=b.ASSET_ and b.BILL_=#{0}
        order by b.ORD_
    </select>

    <update id="updateSnStatus">
        update AM_ASSET_SN set FLAG_='9' where ASSET_=#{0}
    </update>

    <select id="findExportByIds" resultType="com.zy.dam.report.vo.AssetBackExportVo">
        select
        <include refid="meta"/>
        ,(select count(0) from AM_ASSET_BACK_DETAIL m where m.BILL_=a.ID_) asset_count,b.NAME_ user_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,(SELECT GROUP_CONCAT(n.SN_ SEPARATOR ',') FROM AM_ASSET_BACK_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_ AND
        m.BILL_=a.ID_ AND n.FLAG_!='9') asset_sn
        ,(SELECT GROUP_CONCAT(c.NAME_ SEPARATOR ',') FROM AM_ASSET_BACK_DETAIL m,AM_ASSET n, AM_ASSET_TYPE c
        WHERE m.ASSET_=n.ID_ AND m.BILL_=a.ID_ AND n.TYPE_=c.CODE_ AND n.FLAG_!='9'AND c.FLAG_!='9') type_name
        ,(SELECT GROUP_CONCAT(n.NO_ SEPARATOR ',') FROM AM_ASSET_BACK_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_ AND
        m.BILL_=a.ID_ AND n.FLAG_!='9') asset_no
        ,(SELECT GROUP_CONCAT(n.SPEC_ SEPARATOR ',') FROM AM_ASSET_BACK_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_ AND
        m.BILL_=a.ID_ AND n.FLAG_!='9') asset_spec
        ,(SELECT GROUP_CONCAT(n.NAME_ SEPARATOR ',') FROM AM_ASSET_BACK_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_ AND
        m.BILL_=a.ID_ AND n.FLAG_!='9') asset_name
        ,ELT(a.STATUS_ ,'申请','已通过','','','已拒绝','','','','已作废') status
        from AM_ASSET_BACK a left join SYS_USER b on a.USER_=b.ID_ where b.FLAG_!='9'
        <foreach collection="ids" item="id" open="and a.ID_ in (" separator="," close=")">#{id}</foreach>
    </select>

    <select id="findExportByQuery" resultType="com.zy.dam.report.vo.AssetBackExportVo">
        select
        <include refid="meta"/>
        ,(select count(0) from AM_ASSET_BACK_DETAIL m where m.BILL_=a.ID_) asset_count,b.NAME_ user_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,(SELECT GROUP_CONCAT(n.SN_ SEPARATOR ',') FROM AM_ASSET_BACK_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_ AND
        m.BILL_=a.ID_ AND n.FLAG_!='9') asset_sn
        ,(SELECT GROUP_CONCAT(c.NAME_ SEPARATOR ',') FROM AM_ASSET_BACK_DETAIL m,AM_ASSET n, AM_ASSET_TYPE c
        WHERE m.ASSET_=n.ID_ AND m.BILL_=a.ID_ AND n.TYPE_=c.CODE_ AND n.FLAG_!='9'AND c.FLAG_!='9') type_name
        ,(SELECT GROUP_CONCAT(n.NO_ SEPARATOR ',') FROM AM_ASSET_BACK_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_ AND
        m.BILL_=a.ID_ AND n.FLAG_!='9') asset_no
        ,(SELECT GROUP_CONCAT(n.SPEC_ SEPARATOR ',') FROM AM_ASSET_BACK_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_ AND
        m.BILL_=a.ID_ AND n.FLAG_!='9') asset_spec
        ,(SELECT GROUP_CONCAT(n.NAME_ SEPARATOR ',') FROM AM_ASSET_BACK_DETAIL m,AM_ASSET n WHERE m.ASSET_=n.ID_ AND
        m.BILL_=a.ID_ AND n.FLAG_!='9') asset_name
        ,ELT(a.STATUS_ ,'申请','已通过','','','已拒绝','','','','已作废') status
        from AM_ASSET_BACK a left join SYS_USER b on a.USER_=b.ID_
        <where>
            <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.MEMO_ like
                concat('%',#{keyword},'%'))
            </if>
            <if test="status != null">and a.STATUS_=#{status}</if>
            <if test="user != null">and a.USER_=#{user}</if>
            <if test="userKey != null">and (b.NAME_ like concat('%',#{userKey},'%') or b.NO_ like
                concat('%',#{userKey},'%'))
            </if>
            <if test="begin != null">and a.TIME_ &gt;= #{begin}</if>
            <if test="end != null">and a.TIME_ &lt; date_add(#{end}, interval 1 day)</if>
            <if test="deptIds != null">
                <foreach collection="deptIds" item="deptId" open="and a.CDEPT_ in (" separator="," close=")">#{deptId}
                </foreach>
            </if>
        </where>
        order by a.CTIME_ desc
    </select>

    <!-- 更新资产信息-->
    <update id="updateRecover">
        update AM_ASSET a,AM_ASSET_BACK_DETAIL b, AM_ASSET_BACK c
        set a.STATUS_=b.ASSET_STATUS_,a.DEPT_=b.DEPT_,a.USE_DEPT_=b.USE_DEPT_,a.USE_USER_=b.USE_USER_,a.REGION_=b.REGION_,
        a.LOCATION_=b.LOCATION_,a.LNG_=b.LAST_LNG_,a.LAT_=b.LAST_LAT_,a.LOC_ADDR_=b.LAST_LOC_ADDR_,c.STATUS_='3'
        where a.ID_=b.ASSET_ and b.BILL_=c.ID_ and c.ID_=#{0}
    </update>

</mapper>
